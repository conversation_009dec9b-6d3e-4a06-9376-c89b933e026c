{
  "$schema": "https://biomejs.dev/schemas/2.0.0-beta.2/schema.json",
  "vcs": {
    "enabled": true,
    "clientKind": "git",
    "useIgnoreFile": true,
  },
  "files": {
    "ignoreUnknown": false,
    "includes": ["src/**", "!**/node_modules/**", "!src/db/generated/**"],
  },
  "formatter": { "enabled": true, "useEditorconfig": true },
  "assist": {
    "actions": {
      "source": {
        "organizeImports": "on",
      },
    },
  },
  "linter": {
    "enabled": true,
    "rules": {
      "nursery": {
        "useSortedClasses": {
          "level": "warn",
          "fix": "safe",
          "options": {
            "functions": ["clsx", "cva", "cn"],
          },
        },
      },
      "recommended": true,
      "style": {
        "useLiteralEnumMembers": "error",
        "useNodejsImportProtocol": "error",
        "useAsConstAssertion": "error",
        "useEnumInitializers": "error",
        "useSelfClosingElements": "error",
        "useConst": "error",
        "useSingleVarDeclarator": "error",
        "noUnusedTemplateLiteral": "error",
        "useNumberNamespace": "error",
        "noInferrableTypes": "error",
        "useExponentiationOperator": "error",
        "useTemplate": "error",
        "noParameterAssign": "error",
        "noNonNullAssertion": "error",
        "useDefaultParameterLast": "error",
        "useImportType": "error",
        "useExportType": "error",
        "noUselessElse": "error",
        "useShorthandFunctionType": "error",
      },
    },
  },
}
