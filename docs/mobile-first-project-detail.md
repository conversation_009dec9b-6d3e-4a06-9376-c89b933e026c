# Mobile-First Project Detail Page Design

## Overview

The project detail page has been redesigned with a mobile-first approach, featuring a static actionbar with direct access to <PERSON> (AI assistant). This design prioritizes mobile usability while maintaining desktop functionality.

## Key Features

### 1. Mobile Action Bar (`MobileActionBar`)

- **Location**: Fixed at the bottom of the screen on mobile devices
- **Visibility**: **Mobile only** (hidden on desktop to avoid duplication)
- **Design**: Static actionbar with backdrop blur and elevated shadow
- **Spacing**: Includes automatic spacer to prevent content overlap

### 2. Jack Chat Integration (`JackChatButton`)

- **Variants**:
  - `actionbar`: Optimized for mobile actionbar with Jack's avatar
  - `fab`: Floating action button variant
  - `button`: Standard button variant
- **Features**:
  - Jack's avatar as the button icon
  - Direct chat access via modal dialog
  - Touch-friendly sizing (48px minimum)
  - Optional text labels

### 3. Project Actions Enhancement

- **New Layout**: Added `actionbar` layout variant
- **Mobile Optimization**: Ghost variant buttons with icons and labels
- **Touch Targets**: Minimum 48px touch targets for accessibility
- **Responsive**: Different layouts for mobile vs desktop

## Component Structure

```tsx
// Mobile Action Bar Layout
<MobileActionBar variant="elevated">
  <MobileActionBarItem flex>
    <ProjectActions layout="actionbar" />
  </MobileActionBarItem>

  <MobileActionBarItem>
    <JackChatButton variant="actionbar" showLabel />
  </MobileActionBarItem>
</MobileActionBar>
```

## Design Principles

### Mobile-First Approach

1. **Touch-Friendly**: All interactive elements meet 48px minimum touch target
2. **Thumb Navigation**: Actions positioned for easy thumb access
3. **Visual Hierarchy**: Jack's avatar prominently featured in center of actionbar
4. **Performance**: Only renders mobile components when needed

### Desktop Experience

1. **Clean Interface**: Mobile actionbar hidden to avoid covering content
2. **Header Actions**: Project actions displayed in page header
3. **Floating Jack Button**: Dedicated FAB for AI assistant access
4. **No Duplication**: Avoids duplicate UI elements and chat interfaces

### Accessibility

1. **Screen Reader Support**: Proper ARIA labels and semantic HTML
2. **Keyboard Navigation**: Full keyboard accessibility maintained
3. **Focus Management**: Proper focus handling in modal dialogs
4. **Color Contrast**: Maintains design system color standards

## Technical Implementation

### Files Created/Modified

#### New Components

- `src/components/ui/mobile-action-bar.tsx` - Mobile actionbar container
- `src/components/chat/jack-chat-button.tsx` - Jack chat integration

#### Modified Components

- `src/app/(user)/projects/[id]/page.tsx` - Main project detail page
- `src/components/projects/project-actions.tsx` - Added actionbar layout support

### Key Features

#### Mobile Action Bar

```tsx
export function MobileActionBar({
  children,
  variant = "default"
}: MobileActionBarProps) {
  const isMobile = useIsMobile();

  if (!isMobile) return null;

  return (
    <>
      <div className="h-20" /> {/* Spacer */}
      <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-background/95 backdrop-blur">
        {children}
      </div>
    </>
  );
}
```

#### Jack Chat Button

```tsx
export function JackChatButton({
  variant = "actionbar",
  showLabel = false
}: JackChatButtonProps) {
  return (
    <Button variant="ghost" className="flex flex-col items-center">
      <Image src={jackAvatar} alt="Jack" className="rounded-full" />
      {showLabel && <span className="text-xs">Jack</span>}
    </Button>
  );
}
```

## Usage Examples

### Basic Implementation

```tsx
// In project detail page
<MobileActionBar>
  <MobileActionBarItem flex>
    <ProjectActions layout="actionbar" />
  </MobileActionBarItem>

  <MobileActionBarItem>
    <JackChatButton variant="actionbar" showLabel />
  </MobileActionBarItem>
</MobileActionBar>
```

### Customization Options

```tsx
// Different Jack button variants
<JackChatButton variant="fab" size="lg" />           // Floating action button
<JackChatButton variant="actionbar" showLabel />     // Actionbar with label
<JackChatButton variant="button" showLabel />        // Standard button
```

## Benefits

1. **Improved Mobile UX**: Dedicated mobile interface with thumb-friendly navigation
2. **Direct Jack Access**: Prominent placement of AI assistant for immediate help
3. **Consistent Design**: Follows established mobile-first design patterns
4. **Performance**: Conditional rendering reduces unnecessary DOM elements
5. **Accessibility**: Maintains full accessibility compliance
6. **Scalability**: Reusable components for other pages

## Future Enhancements

1. **Gesture Support**: Swipe gestures for action navigation
2. **Context Awareness**: Jack button adapts based on project status
3. **Notification Integration**: Badge support for unread messages
4. **Animation**: Smooth transitions and micro-interactions
5. **Customization**: Theme-aware styling and user preferences
