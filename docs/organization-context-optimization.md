# Organization Context Optimization

This document outlines the optimizations made to the organization context system in the TradeCrews application.

## Changes Made

### 1. Simplified Organization Context

**Before:**

```typescript
export type OrganizationContextType = {
  organization: Organization | null | undefined;
  setOrganization: (organization: Organization | null | undefined) => void;
  organizationList: Organization[] | null | undefined; // Unused
  setOrganizationList: (organizationList: Organization[] | null | undefined) => void; // No-op
};
```

**After:**

```typescript
export type OrganizationContextType = {
  organization: Organization | null | undefined;
  setOrganization: (organization: Organization | null | undefined) => void;
  isLoading: boolean;
  error: Error | null;
};
```

### 2. Role-Based Loading

The context now only fetches organization data for contractors, not homeowners:

```typescript
const { data: userOrganization, error } = useQuery({
  ...trpc.contractor.getForUser.queryOptions(),
  enabled: userRole === "contractor", // Only fetch for contractors
});
```

### 3. Better Loading States

Components now have access to loading and error states:

```typescript
const { organization, isLoading, error } = useOrganization();

if (isLoading) {
  return <LoadingComponent />;
}

if (error) {
  return <ErrorComponent error={error} />;
}
```

### 4. Server-Side Utilities

Added server-side functions for organization data:

```typescript
// src/lib/server/organization.ts
export async function getCurrentUserOrganization() {
  // Server-side organization fetching
}

export async function checkOrganizationAccess(organizationId: string) {
  // Server-side access control
}
```

### 5. Specialized Hooks

Created hooks for different use cases:

```typescript
// For components that require organization (contractors only)
const { organization } = useOrganizationRequired();

// For components that may or may not need organization
const { organization } = useOrganizationSafe();
```

## Usage Examples

### Basic Usage

```typescript
function ContractorComponent() {
  const { organization, isLoading, error } = useOrganization();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!organization) return <div>No organization found</div>;

  return <div>{organization.name}</div>;
}
```

### Required Organization

```typescript
function ContractorDashboard() {
  const { organization, isLoading } = useOrganizationRequired();

  if (isLoading) return <div>Loading...</div>;

  // organization is guaranteed to exist here
  return <div>Welcome to {organization.name}</div>;
}
```

### Safe Organization Access

```typescript
function UniversalComponent() {
  const { organization } = useOrganizationSafe();

  return (
    <div>
      {organization ? (
        <div>Organization: {organization.name}</div>
      ) : (
        <div>No organization (homeowner or not loaded)</div>
      )}
    </div>
  );
}
```

## Benefits

1. **Performance**: Only loads organization data when needed (contractors only)
2. **Simplicity**: Removed unused organizationList properties
3. **Better UX**: Proper loading and error states
4. **Type Safety**: Better TypeScript support with required hooks
5. **Server-Side Support**: Can prefetch organization data on the server
6. **Maintainability**: Cleaner, more focused code

## Migration Guide

### For Components Using `organizationList`

**Before:**

```typescript
const { organizationList } = useOrganization();
const currentOrg = organizationList?.[0];
```

**After:**

```typescript
const { organization } = useOrganization();
// organization is now the single organization for the user
```

### For Components Needing Loading States

**Before:**

```typescript
const { organization } = useOrganization();
// No loading state available
```

**After:**

```typescript
const { organization, isLoading } = useOrganization();
if (isLoading) return <LoadingComponent />;
```

### For Server-Side Organization Access

**Before:**

```typescript
// Had to use TRPC queries in server components
```

**After:**

```typescript
import { getCurrentUserOrganization } from "@/lib/server/organization";

const organization = await getCurrentUserOrganization();
```

## Files Modified

- `src/components/contexts/organization-context.tsx` - Main context optimization
- `src/components/org-switcher.tsx` - Updated to use new context structure
- `src/components/contractor/contractor-dashboard.tsx` - Added loading states
- `src/app/(user)/calendar/page.tsx` - Added loading states
- `src/components/job/professional-job-list.tsx` - Added loading states
- `src/lib/server/organization.ts` - New server-side utilities
- `src/hooks/use-organization-required.ts` - New specialized hooks

## Testing

Make sure to test:

1. Contractor login and organization loading
2. Homeowner login (should not fetch organization data)
3. Loading states in all components
4. Error handling when organization fails to load
5. Server-side organization access
