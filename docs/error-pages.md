# TradeCrews Error Pages Documentation

## Overview

TradeCrews includes a comprehensive set of error pages designed with mobile-first principles, dark mode support, and consistent branding. Each error page provides contextual help and clear recovery paths for users.

## Error Pages

### 1. **404 - Not Found** (`/not-found.tsx`)
**When it appears:** Page or resource doesn't exist
**Theme:** Construction/building theme with hammer icons
**Primary action:** Back to Home
**Secondary actions:** Go Back, Browse Projects
**Message:** "Project Not Found" - contextual to construction theme

### 2. **Generic Error** (`/error.tsx`)
**When it appears:** Unhandled JavaScript errors, component crashes
**Theme:** Technical difficulties with alert and wrench icons
**Primary action:** Try Again (reset error boundary)
**Secondary actions:** Back to Home
**Features:** 
- PostHog error tracking
- Development mode error details
- Contact support link

### 3. **401/403 - Unauthorized** (`/unauthorized.tsx`)
**When it appears:** User needs authentication to access resource
**Theme:** Security/access theme with shield and key icons
**Primary action:** Sign In
**Secondary actions:** Join as Homeowner, Join as Contractor
**Message:** "Access Restricted" - encourages joining the community

### 4. **403 - Forbidden** (`/403.tsx`)
**When it appears:** User is authenticated but lacks permissions
**Theme:** Security theme with shield and user check icons
**Primary action:** Back to Home
**Secondary actions:** Sign In with Different Account
**Message:** Explains role-based restrictions

### 5. **500 - Internal Server Error** (`/500.tsx`)
**When it appears:** Server-side errors, database issues
**Theme:** Server maintenance with crash and tool icons
**Primary action:** Try Again (reload page)
**Secondary actions:** Back to Home
**Features:**
- Status page links
- Social media updates
- Support contact

### 6. **Maintenance** (`/maintenance.tsx`)
**When it appears:** Planned maintenance windows
**Theme:** Construction theme with multiple tool icons
**Primary action:** Check Again (reload)
**Secondary actions:** Back to Home
**Features:**
- Estimated completion time
- What's coming preview
- Status updates

### 7. **Offline** (`/offline.tsx`)
**When it appears:** PWA offline mode, no internet connection
**Theme:** Network connectivity with wifi and signal icons
**Primary action:** Try Again (check connection)
**Secondary actions:** Back to Home, View Cached Content
**Features:**
- Connection troubleshooting tips
- Network status indicator
- Service worker integration

## Design System

### **Mobile-First Approach**
- **Touch targets:** Minimum 48px for comfortable interaction
- **Stacked layout:** Vertical button arrangement on mobile
- **Full-width buttons:** Easy thumb access
- **Responsive breakpoints:** Smooth transitions at `sm:` breakpoint

### **Dark Mode Support**
- **Adaptive backgrounds:** Uses design system color tokens
- **Reduced opacity:** Decorative elements dimmed in dark mode
- **Color variants:** Different link colors for light/dark themes
- **Contrast compliance:** WCAG guidelines maintained

### **Brand Consistency**
- **TradeCrews orange:** Primary action buttons use `tc_orange` variant
- **Logo placement:** Consistent positioning with backdrop blur
- **Construction themes:** Icons and messaging relate to home improvement
- **Typography:** Consistent heading hierarchy and spacing

### **Visual Elements**
- **Background decorations:** Gradient blobs matching hero page style
- **Icon themes:** Each error type has relevant icon combinations
- **Backdrop blur:** Modern glass-morphism effect on logo containers
- **Color coding:** Different gradient colors for different error types

## Technical Implementation

### **Component Structure**
```tsx
// Consistent layout pattern
<main className="relative min-h-screen bg-background">
  {/* Background decoration */}
  <div className="absolute inset-0 overflow-hidden">
    {/* Gradient blob */}
  </div>

  {/* Content */}
  <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8">
    <div className="w-full max-w-md text-center">
      {/* Logo */}
      {/* Error icon/number */}
      {/* Message */}
      {/* Actions */}
      {/* Help links */}
    </div>
  </div>

  {/* Bottom decoration */}
</main>
```

### **Button Patterns**
- **Primary:** `tc_orange` variant, full width on mobile
- **Secondary:** `outline` variant, responsive grid layout
- **Touch-friendly:** `touch-target-comfortable` class
- **Icon + text:** Consistent icon placement and sizing

### **Responsive Design**
- **Mobile:** Single column, stacked buttons, larger touch targets
- **Desktop:** Two-column secondary actions, refined spacing
- **Breakpoints:** Uses Tailwind's `sm:` prefix for mobile-first approach

## Error Tracking

### **PostHog Integration**
The generic error page (`error.tsx`) includes PostHog error tracking:
```tsx
useEffect(() => {
  posthog.captureException(error);
}, [error]);
```

### **Development Features**
- **Error details:** Technical information shown in development mode
- **Stack traces:** Collapsible error details for debugging
- **Environment awareness:** Different behavior in dev vs production

## Accessibility Features

### **Screen Reader Support**
- **Semantic HTML:** Proper heading hierarchy and landmarks
- **Alt text:** Descriptive image alternatives
- **ARIA labels:** Enhanced accessibility for interactive elements

### **Keyboard Navigation**
- **Focus management:** Proper tab order and focus indicators
- **Skip links:** Easy navigation for keyboard users
- **Interactive elements:** All buttons and links keyboard accessible

### **Color and Contrast**
- **WCAG compliance:** Meets accessibility guidelines
- **High contrast:** Sufficient color contrast ratios
- **Color independence:** Information not conveyed by color alone

## Usage Guidelines

### **When to Use Each Page**
1. **404:** Missing routes, deleted resources, typos in URLs
2. **Error:** Component crashes, JavaScript errors, API failures
3. **Unauthorized:** Authentication required, session expired
4. **403:** Insufficient permissions, role restrictions
5. **500:** Server errors, database issues, API downtime
6. **Maintenance:** Planned updates, system upgrades
7. **Offline:** Network issues, PWA offline mode

### **Customization**
Each error page can be customized by:
- **Updating messages:** Change text to match specific use cases
- **Adding features:** Include additional help or recovery options
- **Modifying actions:** Adjust button text and destinations
- **Theming:** Update colors and icons for different contexts

### **Testing**
Test error pages by:
- **Manual navigation:** Visit `/404`, `/500`, etc.
- **Error simulation:** Throw errors in components
- **Network simulation:** Test offline functionality
- **Device testing:** Verify mobile and desktop layouts

## Best Practices

### **User Experience**
- **Clear messaging:** Explain what happened in user-friendly terms
- **Recovery paths:** Provide multiple ways to get back on track
- **Brand consistency:** Maintain TradeCrews voice and visual identity
- **Mobile optimization:** Ensure excellent mobile experience

### **Performance**
- **Minimal JavaScript:** Keep error pages lightweight
- **Optimized images:** Use appropriate image sizes and formats
- **Fast loading:** Ensure error pages load quickly when needed
- **Offline support:** Basic functionality without network

### **Maintenance**
- **Regular testing:** Verify error pages work correctly
- **Content updates:** Keep help links and contact information current
- **Analytics:** Monitor error page usage and user behavior
- **Feedback:** Collect user feedback on error page effectiveness

## Future Enhancements

### **Planned Improvements**
1. **Dynamic content:** Personalized error messages based on user context
2. **Search integration:** Help users find what they were looking for
3. **Recent pages:** Show user's recent activity for easy navigation
4. **Chatbot integration:** AI assistance for error recovery
5. **Progressive enhancement:** Enhanced features for modern browsers

### **Analytics Integration**
- **Error tracking:** Detailed error reporting and analysis
- **User behavior:** Track recovery paths and success rates
- **Performance monitoring:** Monitor error page load times
- **A/B testing:** Test different messaging and layouts

This comprehensive error page system ensures that TradeCrews users always have a clear path forward, even when things go wrong, while maintaining the platform's professional and helpful brand experience.