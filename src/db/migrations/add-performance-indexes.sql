-- Performance Optimization Indexes
-- Based on query analysis from tRPC routers

-- ============================================================================
-- JOB-RELATED INDEXES
-- ============================================================================

-- Index for job status queries (most common filter)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_status 
ON job (status);

-- Composite index for job status and created_at (for listing queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_status_created_at 
ON job (status, created_at DESC);

-- Index for job property_id (foreign key queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_property_id 
ON job (property_id);

-- Composite index for job property_id and status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_property_status 
ON job (property_id, status);

-- Index for job completion queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_job_completed_at 
ON job (completed_at DESC) WHERE completed_at IS NOT NULL;

-- ============================================================================
-- BID-RELATED INDEXES
-- ============================================================================

-- Index for bid status (frequently filtered)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_status 
ON bid (status);

-- Composite index for bid job_id and status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_job_status 
ON bid (job_id, status);

-- Composite index for bid organization_id and status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_organization_status 
ON bid (organization_id, status);

-- Index for bid created_at (for ordering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_created_at 
ON bid (created_at DESC);

-- Composite index for organization bids with status and created_at
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bid_org_status_created 
ON bid (organization_id, status, created_at DESC);

-- ============================================================================
-- PROPERTY-RELATED INDEXES
-- ============================================================================

-- Index for property user_id (owner queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_user_id 
ON property (user_id);

-- Index for property address_id (join optimization)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_address_id 
ON property (address_id);

-- ============================================================================
-- ORGANIZATION-RELATED INDEXES
-- ============================================================================

-- Index for organization trade_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_trade_id 
ON organization (trade_id);

-- Index for organization name (search queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_name 
ON organization (name);

-- Full-text search index for organization name and description
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_search 
ON organization USING gin(to_tsvector('english', name || ' ' || coalesce(description, '')));

-- Index for organization address_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_address_id 
ON organization (address_id);

-- ============================================================================
-- MEMBERSHIP-RELATED INDEXES
-- ============================================================================

-- Index for membership user_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_membership_user_id 
ON membership (user_id);

-- Index for membership organization_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_membership_organization_id 
ON membership (organization_id);

-- Composite index for membership queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_membership_user_org 
ON membership (user_id, organization_id);

-- ============================================================================
-- REVIEW-RELATED INDEXES
-- ============================================================================

-- Index for review job_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_job_id 
ON review (job_id);

-- Index for review type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_type 
ON review (review_type);

-- Composite index for review job and type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_job_type 
ON review (job_id, review_type);

-- Index for review created_at (ordering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_created_at 
ON review (created_at DESC);

-- Index for review rating (statistics)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_rating 
ON review (rating);

-- ============================================================================
-- SCHEDULE-RELATED INDEXES
-- ============================================================================

-- Index for schedule job_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_job_id 
ON schedule (job_id);

-- Index for schedule status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_status 
ON schedule (status);

-- Index for schedule proposed_start_date (calendar queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_start_date 
ON schedule (proposed_start_date);

-- Composite index for schedule date range queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_date_range 
ON schedule (proposed_start_date, proposed_end_date);

-- ============================================================================
-- MESSAGE-RELATED INDEXES
-- ============================================================================

-- Index for message chat_id (already exists but ensuring)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_message_chat_id 
ON message (chat_id);

-- Index for message created_at (ordering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_message_created_at 
ON message (created_at DESC);

-- Composite index for message chat and created_at
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_message_chat_created 
ON message (chat_id, created_at DESC);

-- ============================================================================
-- CHAT-RELATED INDEXES
-- ============================================================================

-- Index for chat bid_id (unique constraint already exists)
-- Index for chat job_id (unique constraint already exists)

-- ============================================================================
-- ACCOUNT-RELATED INDEXES
-- ============================================================================

-- Index for account user_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_user_id 
ON account (user_id);

-- Index for account role
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_role 
ON account (role);

-- ============================================================================
-- TRADE-RELATED INDEXES
-- ============================================================================

-- Index for trade name (search and ordering)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trade_name 
ON trade (name);

-- Full-text search index for trade name
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trade_search 
ON trade USING gin(to_tsvector('english', name));

-- ============================================================================
-- ADDRESS-RELATED INDEXES
-- ============================================================================

-- Composite index for address location queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_address_location 
ON address (city, state);

-- Index for address zip code
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_address_zip 
ON address (zip);

-- Spatial index for location-based queries (if using PostGIS)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_address_location_spatial 
ON address USING gist(location) WHERE location IS NOT NULL;

-- ============================================================================
-- USER-RELATED INDEXES
-- ============================================================================

-- Index for user email (unique constraint already exists)
-- Index for user created_at
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_created_at 
ON "user" (created_at DESC);

-- ============================================================================
-- TASK-RELATED INDEXES
-- ============================================================================

-- Index for task job_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_job_id 
ON task (job_id);

-- Index for task trade_id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_trade_id 
ON task (trade_id);

-- Composite index for task job and trade
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_job_trade 
ON task (job_id, trade_id);

-- ============================================================================
-- PERFORMANCE MONITORING QUERIES
-- ============================================================================

-- Query to check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- ORDER BY idx_scan DESC;

-- Query to find unused indexes
-- SELECT schemaname, tablename, indexname, idx_scan
-- FROM pg_stat_user_indexes
-- WHERE idx_scan = 0
-- ORDER BY schemaname, tablename;

-- Query to check table sizes
-- SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
-- FROM pg_tables
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
