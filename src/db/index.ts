import { neon, neonConfig } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import ws from "ws";
import { env } from "@/env";
import * as schema from "./schema";

neonConfig.webSocketConstructor = ws;

// For server-side usage
const createDrizzleClient = () => {
  const sql = neon(env.DATABASE_URL);
  return drizzle(sql, { schema });
};

// Global instance for Next.js
const globalForDrizzle = globalThis as unknown as {
  db: ReturnType<typeof createDrizzleClient> | undefined;
};

export const db = globalForDrizzle.db ?? createDrizzleClient();

if (env.NODE_ENV !== "production") globalForDrizzle.db = db;

export type Db = typeof db;
export type Tx = Parameters<Parameters<Db["transaction"]>[0]>[0];
