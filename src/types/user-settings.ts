export interface UserSettings {
  notifications?: {
    email?: {
      marketing?: boolean;
      jobUpdates?: boolean;
      messages?: boolean;
      bids?: boolean;
    };
    push?: {
      enabled?: boolean;
      jobUpdates?: boolean;
      messages?: boolean;
      bids?: boolean;
    };
  };
  theme?: "light" | "dark" | "system";
  display?: {
    compactView?: boolean;
    showTutorials?: boolean;
  };
}

export const defaultUserSettings: UserSettings = {
  notifications: {
    email: {
      marketing: true,
      jobUpdates: true,
      messages: true,
      bids: true,
    },
    push: {
      enabled: false,
      jobUpdates: true,
      messages: true,
      bids: true,
    },
  },
  theme: "system",
  display: {
    compactView: false,
    showTutorials: true,
  },
};
