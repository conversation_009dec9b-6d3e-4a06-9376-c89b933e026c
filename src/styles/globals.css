@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans:
    var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Mobile-first utilities */
@layer utilities {
  /* Touch-friendly targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-target-comfortable {
    min-height: 48px;
    min-width: 48px;
  }

  .touch-target-large {
    min-height: 56px;
    min-width: 56px;
  }

  /* Safe area support */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  .safe-area {
    padding-top: env(safe-area-inset-top);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
  }

  /* Mobile-specific viewport units */
  .h-screen-mobile {
    height: 100vh; /* Fallback */
  }

  @supports (height: 100dvh) {
    .h-screen-mobile {
      height: 100dvh; /* Dynamic viewport height */
    }
  }

  .min-h-screen-mobile {
    min-height: 100vh; /* Fallback */
  }

  @supports (min-height: 100dvh) {
    .min-h-screen-mobile {
      min-height: 100dvh;
    }
  }

  /* Improved scrolling on mobile */
  .scroll-smooth-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hide scrollbars on mobile while keeping functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
  --tradecrews-blue: oklch(0.41 0.1291 259.9);
  --tradecrews-orange: oklch(0.69 0.1841 44.49);
  --microsoft-blue: oklch(0.7849 0.13 221.76);

  /* TradeCrews color palette - Orange */
  --tradecrews-orange-50: oklch(0.98 0.03 44.49);
  --tradecrews-orange-100: oklch(0.95 0.05 44.49);
  --tradecrews-orange-200: oklch(0.9 0.08 44.49);
  --tradecrews-orange-300: oklch(0.85 0.12 44.49);
  --tradecrews-orange-400: oklch(0.77 0.16 44.49);
  --tradecrews-orange-500: oklch(0.69 0.1841 44.49);
  --tradecrews-orange-600: oklch(0.62 0.16 44.49);
  --tradecrews-orange-700: oklch(0.55 0.14 44.49);
  --tradecrews-orange-800: oklch(0.48 0.12 44.49);
  --tradecrews-orange-900: oklch(0.41 0.1 44.49);
  --tradecrews-orange-950: oklch(0.35 0.08 44.49);

  /* TradeCrews color palette - Blue */
  --tradecrews-blue-50: oklch(0.95 0.02 259.9);
  --tradecrews-blue-100: oklch(0.9 0.03 259.9);
  --tradecrews-blue-200: oklch(0.8 0.05 259.9);
  --tradecrews-blue-300: oklch(0.7 0.07 259.9);
  --tradecrews-blue-400: oklch(0.6 0.09 259.9);
  --tradecrews-blue-500: oklch(0.5 0.11 259.9);
  --tradecrews-blue-600: oklch(0.41 0.1291 259.9);
  --tradecrews-blue-700: oklch(0.35 0.11 259.9);
  --tradecrews-blue-800: oklch(0.3 0.09 259.9);
  --tradecrews-blue-900: oklch(0.25 0.07 259.9);
  --tradecrews-blue-950: oklch(0.2 0.05 259.9);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-tradecrews-orange: var(--tradecrews-orange);
  --color-tradecrews-blue: var(--tradecrews-blue);
  --color-microsoft-blue: var(--microsoft-blue);

  /* TradeCrews color palette - Orange */
  --color-tradecrews-orange-50: var(--tradecrews-orange-50);
  --color-tradecrews-orange-100: var(--tradecrews-orange-100);
  --color-tradecrews-orange-200: var(--tradecrews-orange-200);
  --color-tradecrews-orange-300: var(--tradecrews-orange-300);
  --color-tradecrews-orange-400: var(--tradecrews-orange-400);
  --color-tradecrews-orange-500: var(--tradecrews-orange-500);
  --color-tradecrews-orange-600: var(--tradecrews-orange-600);
  --color-tradecrews-orange-700: var(--tradecrews-orange-700);
  --color-tradecrews-orange-800: var(--tradecrews-orange-800);
  --color-tradecrews-orange-900: var(--tradecrews-orange-900);
  --color-tradecrews-orange-950: var(--tradecrews-orange-950);

  /* TradeCrews color palette - Blue */
  --color-tradecrews-blue-50: var(--tradecrews-blue-50);
  --color-tradecrews-blue-100: var(--tradecrews-blue-100);
  --color-tradecrews-blue-200: var(--tradecrews-blue-200);
  --color-tradecrews-blue-300: var(--tradecrews-blue-300);
  --color-tradecrews-blue-400: var(--tradecrews-blue-400);
  --color-tradecrews-blue-500: var(--tradecrews-blue-500);
  --color-tradecrews-blue-600: var(--tradecrews-blue-600);
  --color-tradecrews-blue-700: var(--tradecrews-blue-700);
  --color-tradecrews-blue-800: var(--tradecrews-blue-800);
  --color-tradecrews-blue-900: var(--tradecrews-blue-900);
  --color-tradecrews-blue-950: var(--tradecrews-blue-950);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
