"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSession } from "@/lib/auth-client";

export function useRequireAuth() {
  const { data, isPending } = useSession();
  const user = data?.user;
  const router = useRouter();

  useEffect(() => {
    if (!isPending && !user) {
      router.push("/sign-in");
    }
  }, [user, isPending, router]);

  return { user, isPending };
}

export function useRedirectIfAuthenticated(redirectTo = "/dashboard") {
  const { data, isPending } = useSession();
  const user = data?.user;
  const router = useRouter();

  useEffect(() => {
    if (!isPending && user) {
      router.push(redirectTo);
    }
  }, [user, isPending, router, redirectTo]);

  return { user, isPending };
}
