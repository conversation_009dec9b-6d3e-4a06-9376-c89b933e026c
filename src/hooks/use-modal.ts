import { useCallback, useState } from "react";

interface UseModalOptions<T> {
  /**
   * Initial data for the modal
   */
  initialData?: T;

  /**
   * Callback when modal is opened
   */
  onOpen?: (data?: T) => void;

  /**
   * Callback when modal is closed
   */
  onClose?: () => void;
}

/**
 * Hook for managing modal state
 */
export function useModal<T = undefined>({
  initialData,
  onOpen,
  onClose,
}: UseModalOptions<T> = {}) {
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState<T | undefined>(initialData);

  const open = useCallback(
    (newData?: T) => {
      setIsOpen(true);
      setData(newData);
      onOpen?.(newData);
    },
    [onOpen],
  );

  const close = useCallback(() => {
    setIsOpen(false);
    onClose?.();
  }, [onClose]);

  return {
    isOpen,
    data,
    open,
    close,
  };
}
