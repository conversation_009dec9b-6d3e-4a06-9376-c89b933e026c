import { useQuery } from "@tanstack/react-query";
import { useTRPC } from "@/components/integrations/trpc/client";
import { useSession } from "@/lib/auth-client";

export function useSidebarNotifications() {
  const trpc = useTRPC();
  const { data: session } = useSession();
  const isProfessional = session?.user?.role === "contractor";

  // Fetch unread message count
  const { data: unreadMessageCount } = useQuery(
    trpc.dashboard.getUnreadMessageCount.queryOptions(),
  );

  // Fetch pending bids count for contractors
  const { data: contractorDashboardStats } = useQuery(
    trpc.dashboard.getContractorDashboardStats.queryOptions(undefined, {
      enabled: isProfessional,
    }),
  );

  // Fetch pending bids for homeowners
  const { data: pendingBidsData } = useQuery(
    trpc.dashboard.getPendingBidsForUser.queryOptions(undefined, {
      enabled: !isProfessional,
    }),
  );

  // Fetch jobs with approaching deadlines for contractors
  const { data: jobsWithDeadlines } = useQuery(
    trpc.dashboard.getJobsWithDeadlines.queryOptions(
      { hoursAhead: 24 },
      { enabled: isProfessional },
    ),
  );

  return {
    unreadMessages: unreadMessageCount || 0,
    pendingBids: isProfessional
      ? contractorDashboardStats?.proposedBids || 0
      : pendingBidsData?.length || 0,
    urgentDeadlines:
      jobsWithDeadlines?.filter((job) => {
        const hoursUntilDeadline =
          (new Date(job.job.deadline).getTime() - Date.now()) /
          (1000 * 60 * 60);
        return hoursUntilDeadline <= 6;
      }).length || 0,
  };
}
