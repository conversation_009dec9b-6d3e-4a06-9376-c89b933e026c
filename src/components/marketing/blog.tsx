import { allPosts } from "contentlayer/generated";
import { compareDesc } from "date-fns";
import { ArrowRightIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { formatLongDate } from "@/lib/utils";
import { Button } from "../ui/button";

export default function Blog() {
  const posts = allPosts.sort((a, b) =>
    compareDesc(new Date(a.date), new Date(b.date))
  );

  // Only show the 3 most recent posts
  const recentPosts = posts.slice(0, 3);

  return (
    <div className="relative bg-white py-24 sm:py-32">
      {/* Background decoration */}
      <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] bg-gradient-to-tr from-orange-500 to-orange-300 opacity-10 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="font-semibold text-base text-orange-600 leading-7">
            Our Blog
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Expert advice for your renovation journey
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Learn how to plan, budget, and execute your home improvement
            projects with insights from industry professionals.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl auto-rows-fr grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {recentPosts.map((post) => (
            <article
              key={post._id}
              className="relative isolate flex flex-col justify-end overflow-hidden rounded-2xl bg-gray-900 px-8 pt-80 pb-8 sm:pt-48 lg:pt-80"
            >
              <Image
                src={
                  post.image ||
                  "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1073&q=80"
                }
                alt={post.title}
                width={1073}
                height={1073}
                className="-z-10 absolute inset-0 h-full w-full object-cover"
              />
              <div className="-z-10 absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/40" />
              <div className="-z-10 absolute inset-0 rounded-2xl ring-1 ring-gray-900/10 ring-inset" />

              <div className="flex flex-wrap items-center gap-y-1 overflow-hidden text-gray-300 text-sm leading-6">
                <time dateTime={post.date} className="mr-8">
                  {formatLongDate(post.date)}
                </time>
                <div className="-ml-4 flex items-center gap-x-4">
                  <svg
                    viewBox="0 0 2 2"
                    className="-ml-0.5 h-0.5 w-0.5 flex-none fill-white/50"
                  >
                    <title>dot</title>
                    <circle cx={1} cy={1} r={1} />
                  </svg>
                  <div className="flex gap-x-2.5">
                    <span>{post.category || "Home Improvement"}</span>
                  </div>
                </div>
              </div>
              <h3 className="mt-3 font-semibold text-lg text-white leading-6">
                <Link href={post.url}>
                  <span className="absolute inset-0" />
                  {post.title}
                </Link>
              </h3>
              <p className="mt-3 line-clamp-2 text-gray-300 text-sm">
                {post.description}
              </p>
            </article>
          ))}
        </div>

        <div className="mt-10 flex justify-center">
          <Link href="/blog">
            <Button
              variant="outline"
              className="gap-2 border-orange-200 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
            >
              View all articles <ArrowRightIcon className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
