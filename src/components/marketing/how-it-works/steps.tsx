import {
  Bar<PERSON>hart3Icon,
  CheckCircleIcon,
  ClipboardIcon,
  UsersIcon,
} from "lucide-react";
import Image from "next/image";

const steps = [
  {
    id: 1,
    name: "Create Your Project",
    description:
      "Describe your project in detail including scope, timeline, and budget. Add photos to help contractors understand your vision.",
    icon: ClipboardIcon,
    imageSrc: "/images/create-project.png",
  },
  {
    id: 2,
    name: "Get Matched with Professionals",
    description:
      "Our system matches you with verified contractors who specialize in your project type and are available in your area.",
    icon: UsersIcon,
    imageSrc: "/images/match-professionals.jpg",
  },
  {
    id: 3,
    name: "Compare Quotes and Reviews",
    description:
      "Review competitive bids from multiple contractors. Compare pricing, timelines, and past customer reviews to make an informed decision.",
    icon: BarChart3Icon,
    imageSrc: "/images/compare-quotes.jpg",
  },
  {
    id: 4,
    name: "Hire and Manage Your Project",
    description:
      "Select your preferred professional and manage your project through our platform. Track progress, communicate, and process payments securely.",
    icon: CheckCircleIcon,
    imageSrc: "/images/manage-project.jpg",
  },
];

export function HowItWorksSteps() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-semibold text-base text-orange-600 leading-7">
            Simple Process
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Four easy steps to your perfect project
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Our streamlined process makes it easy to find the right professional
            and get your project done right.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          {steps.map((step, stepIdx) => (
            <div
              key={step.id}
              className={`flex flex-col items-center gap-12 lg:flex-row ${stepIdx % 2 !== 0 ? "lg:flex-row-reverse" : ""} mb-24 last:mb-0`}
            >
              <div className="flex-1">
                <div className="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-50">
                  <step.icon
                    className="h-6 w-6 text-orange-600"
                    aria-hidden="true"
                  />
                </div>
                <h3 className="mb-4 font-semibold text-2xl text-gray-900 leading-7">
                  <span className="mr-2 text-orange-600">{step.id}.</span>{" "}
                  {step.name}
                </h3>
                <p className="text-gray-600 text-lg leading-7">
                  {step.description}
                </p>
              </div>
              <div className="flex-1 overflow-hidden rounded-xl shadow-lg">
                <div className="relative aspect-[4/3]">
                  <div className="absolute inset-0 animate-pulse bg-gray-200" />
                  {/* Replace with actual images */}
                  <Image
                    src={step.imageSrc}
                    alt={step.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
