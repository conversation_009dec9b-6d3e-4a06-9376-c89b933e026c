import { StarIcon } from "lucide-react";

const testimonials = [
  {
    id: 1,
    content:
      "TradeCrews made finding a reliable contractor so easy. I posted my kitchen renovation project and had three qualified professionals bidding within 24 hours. The whole process was smooth from start to finish.",
    author: "<PERSON>",
    role: "Homeowner",
    rating: 5,
  },
  {
    id: 2,
    content:
      "As a plumbing contractor, TradeCrews has transformed how I find new clients. The platform connects me with serious homeowners who are ready to start projects, and the project management tools keep everything organized.",
    author: "<PERSON>",
    role: "Plumbing Contractor",
    rating: 5,
  },
  {
    id: 3,
    content:
      "I was skeptical at first, but TradeCrews exceeded my expectations. The verification process gave me confidence in the professionals I was considering, and the secure payment system protected my investment.",
    author: "<PERSON>",
    role: "Homeowner",
    rating: 4,
  },
];

export function Testimonials() {
  return (
    <div className="bg-gray-50 py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-semibold text-base text-orange-600 leading-7">
            Testimonials
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Hear from our satisfied users
          </p>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            Don't just take our word for it. See what homeowners and
            professionals are saying about their experience with TradeCrews.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="flex flex-col justify-between rounded-xl border border-gray-200 bg-white p-8 shadow-sm"
            >
              <div>
                <div className="mb-4 flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                      key={i}
                      className={`h-5 w-5 ${
                        i < testimonial.rating
                          ? "text-yellow-400"
                          : "text-gray-200"
                      }`}
                    />
                  ))}
                </div>
                <p className="text-gray-700 text-lg italic">
                  "{testimonial.content}"
                </p>
              </div>
              <div className="mt-8">
                <p className="font-semibold text-gray-900">
                  {testimonial.author}
                </p>
                <p className="text-gray-600 text-sm">{testimonial.role}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
