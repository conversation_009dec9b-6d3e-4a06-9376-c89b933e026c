import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

const faqs = [
  {
    question: "How does TradeCrews verify professionals?",
    answer:
      "We conduct thorough background checks on all trade professionals, verifying their business licenses, insurance coverage, and professional credentials. We also collect and verify reviews from past clients to ensure quality and reliability.",
  },
  {
    question: "Is there a fee to use TradeCrews as a homeowner?",
    answer:
      "No, homeowners can use TradeCrews to post projects, receive bids, and communicate with professionals at no cost. We only charge a small service fee when you hire a professional through our platform, which helps us maintain our verification processes and secure payment system.",
  },
  {
    question: "How long does it typically take to receive bids?",
    answer:
      "Most homeowners receive their first bids within 24-48 hours of posting a project. The exact timing depends on the project complexity, location, and current demand for specific trade skills.",
  },
  {
    question: "Can I hire professionals for emergency services?",
    answer:
      "Yes, TradeCrews has an expedited matching process for emergency services. Simply mark your project as 'Urgent' when posting, and our system will prioritize matching you with available professionals who can respond quickly.",
  },
  {
    question: "How does the payment protection work?",
    answer:
      "Our secure payment system holds your payment in escrow until you confirm that the work has been completed to your satisfaction. This protects both homeowners and professionals by ensuring that funds are only released when the job is done right.",
  },
  {
    question: "What if I'm not satisfied with the work?",
    answer:
      "If you're not satisfied with the work performed, you can use our dispute resolution process. Our team will review the situation, facilitate communication between you and the professional, and help reach a fair resolution.",
  },
];

export function FAQ() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-semibold text-base text-tradecrews-orange leading-7">
            Frequently asked questions
          </h2>
          <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
            Everything you need to know
          </p>
          <p className="mt-6 text-gray-700 text-lg leading-8">
            Can't find the answer you're looking for? Contact our support team
            at{" "}
            <a
              href="mailto:<EMAIL>"
              className="font-medium text-tradecrews-orange hover:text-tradecrews-orange/90"
            >
              <EMAIL>
            </a>
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-3xl">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={faq.question} value={`item-${index}`}>
                <AccordionTrigger className="text-left font-medium text-gray-900 text-lg">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-base text-gray-700">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
