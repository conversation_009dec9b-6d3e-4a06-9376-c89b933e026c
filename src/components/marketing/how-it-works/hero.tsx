import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";

export function HowItWorksHero() {
  return (
    <div className="relative isolate overflow-hidden bg-white">
      <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-orange-400 to-orange-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 py-24 text-center sm:py-32 lg:px-8 lg:py-40">
        <h1 className="mt-10 font-bold text-4xl text-gray-900 tracking-tight sm:text-6xl">
          How TradeCrews Works
        </h1>
        <p className="mx-auto mt-6 max-w-3xl text-gray-600 text-lg leading-8">
          Our platform makes finding and hiring qualified trade professionals
          simple and reliable. Follow these easy steps to transform your home
          with confidence.
        </p>
        <div className="mt-10 flex flex-col gap-4 sm:flex-row sm:justify-center">
          <Link
            href="/sign-up/homeowner"
            className={buttonVariants({
              variant: "tc_orange",
              size: "lg",
              className: "gap-2",
            })}
          >
            Get Started <ArrowRightIcon className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}
