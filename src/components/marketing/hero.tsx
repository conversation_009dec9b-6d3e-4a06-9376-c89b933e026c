import { ArrowRightIcon, CheckCircle2Icon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import type { Session } from "@/lib/auth";
import { buttonVariants } from "../ui/button";

export default function Hero({ session }: { session?: Session | null }) {

  return (
    <div className="relative isolate overflow-hidden bg-white">
      <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
        <div
          className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-orange-400 to-orange-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40">
        <div className="mx-auto max-w-2xl lg:mx-0 lg:flex-auto">
          <div className="flex">
            <div className="relative flex items-center gap-x-2 rounded-full px-4 py-1 text-gray-600 text-sm leading-6 ring-1 ring-gray-900/10 hover:ring-gray-900/20">
              <span className="font-semibold text-orange-600">New</span>
              <span className="h-0.5 w-0.5 rounded-full bg-gray-500" />
              <span>Instant contractor matching now available</span>
            </div>
          </div>
          <h1 className="mt-10 max-w-lg font-bold text-4xl text-gray-900 tracking-tight sm:text-6xl">
            Transform Your Home with Trusted Professionals
          </h1>
          <p className="mt-6 text-gray-600 text-lg leading-8">
            TradeCrews connects homeowners with verified contractors for all
            your renovation needs. Get competitive bids, manage projects, and
            achieve quality results with confidence.
          </p>
          <div className="mt-6 flex flex-col gap-4 sm:flex-row sm:items-center">
            {session ? (
              <Link
                href="/dashboard"
                className={buttonVariants({
                  variant: "tc_orange",
                  size: "lg",
                  className: "gap-2",
                })}
              >
                Dashboard <ArrowRightIcon className="h-4 w-4" />
              </Link>
            ) : (
              <Link
                href="/sign-up/homeowner"
                className={buttonVariants({
                  variant: "tc_orange",
                  size: "lg",
                  className: "gap-2",
                })}
              >
                Find Contractors <ArrowRightIcon className="h-4 w-4" />
              </Link>
            )}
            <Link
              href="/how-it-works"
              className={buttonVariants({ variant: "tc_blue", size: "lg" })}
            >
              How it works
            </Link>
          </div>

          <div className="mt-10 flex items-center gap-x-6">
            <div className="flex flex-col gap-y-3">
              <div className="flex items-center gap-x-2">
                <CheckCircle2Icon className="h-5 w-5 flex-none text-orange-500" />
                <span className="text-gray-600">Verified professionals</span>
              </div>
              <div className="flex items-center gap-x-2">
                <CheckCircle2Icon className="h-5 w-5 flex-none text-orange-500" />
                <span className="text-gray-600">Competitive quotes</span>
              </div>
              <div className="flex items-center gap-x-2">
                <CheckCircle2Icon className="h-5 w-5 flex-none text-orange-500" />
                <span className="text-gray-600">Secure payments</span>
              </div>
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <Link
              href="/contractors"
              className="font-semibold text-orange-600 text-sm hover:text-orange-500"
            >
              Sign up as a contractor →
            </Link>

            <Link
              href="/sign-in"
              className="font-semibold text-gray-600 text-sm hover:text-gray-800"
            >
              Already have an account? Sign in
            </Link>
          </div>
        </div>

        <div className="mt-16 sm:mt-24 lg:mt-0 lg:flex-shrink-0 lg:flex-grow">
          <div className="relative mx-auto w-full max-w-lg lg:max-w-md">
            <div className="-inset-y-6 -inset-x-4 lg:-inset-x-6 absolute z-0 scale-95 bg-orange-50 opacity-50 blur-2xl" />
            <div className="relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-xl">
              <Image
                src="/images/renovation-hero.jpg"
                alt="Home renovation project"
                width={500}
                height={500}
                className="w-full object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute right-0 bottom-0 left-0 p-6">
                <div className="rounded-lg bg-white/90 p-4 backdrop-blur-sm">
                  <p className="font-medium text-gray-900">
                    Kitchen Renovation
                  </p>
                  <div className="mt-1 flex items-center justify-between">
                    <p className="text-gray-500 text-sm">3 bids received</p>
                    <p className="font-medium text-orange-600">
                      $12,500 - $18,000
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="-z-10 absolute inset-x-0 top-[calc(100%-13rem)] transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
        <div
          className="-translate-x-1/2 relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] bg-gradient-to-tr from-orange-500 to-orange-300 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>
    </div>
  );
}
