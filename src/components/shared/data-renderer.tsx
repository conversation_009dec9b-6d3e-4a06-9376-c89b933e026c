"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, WifiOff } from "lucide-react";
import type { ReactNode } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

export interface DataState<TData> {
  data: TData | undefined;
  isLoading: boolean;
  isError: boolean;
  error?: Error | null;
  isFetching?: boolean;
  isRefetching?: boolean;
  refetch?: () => void;
}

export interface DataRendererProps<TData> {
  // Data state
  state: DataState<TData>;

  // Content renderers
  children: (data: TData) => ReactNode;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
  emptyComponent?: ReactNode;

  // Empty state configuration
  isEmpty?: (data: TData) => boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyAction?: {
    label: string;
    onClick: () => void;
  };

  // Error state configuration
  errorTitle?: string;
  errorDescription?: string;
  showRetry?: boolean;
  retryLabel?: string;

  // Loading state configuration
  loadingTitle?: string;
  loadingDescription?: string;
  skeletonCount?: number;
  skeletonHeight?: string;

  // Layout options
  variant?: "default" | "card" | "inline";
  showRefreshIndicator?: boolean;

  // Styling
  className?: string;
  contentClassName?: string;
}

export function DataRenderer<TData>({
  state,
  children,
  loadingComponent,
  errorComponent,
  emptyComponent,
  isEmpty = (data) => Array.isArray(data) && data.length === 0,
  emptyTitle = "No data found",
  emptyDescription = "There's nothing to display at the moment.",
  emptyAction,
  errorTitle = "Something went wrong",
  errorDescription,
  showRetry = true,
  retryLabel = "Try again",
  loadingTitle,
  loadingDescription,
  skeletonCount = 3,
  skeletonHeight = "h-20",
  variant = "default",
  showRefreshIndicator = true,
  className,
  contentClassName,
}: DataRendererProps<TData>) {
  const { data, isLoading, isError, error, isFetching, isRefetching, refetch } =
    state;

  // Loading state
  if (isLoading) {
    if (loadingComponent) {
      return <div className={className}>{loadingComponent}</div>;
    }

    if (variant === "card") {
      return (
        <Card className={className}>
          <CardHeader>
            <CardTitle>{loadingTitle || "Loading..."}</CardTitle>
            {loadingDescription && (
              <CardDescription>{loadingDescription}</CardDescription>
            )}
          </CardHeader>
          <CardContent>
            <LoadingSkeleton count={skeletonCount} height={skeletonHeight} />
          </CardContent>
        </Card>
      );
    }

    return (
      <div className={className}>
        {(loadingTitle || loadingDescription) && (
          <div className="mb-4 space-y-1">
            {loadingTitle && (
              <h3 className="font-medium text-lg">{loadingTitle}</h3>
            )}
            {loadingDescription && (
              <p className="text-muted-foreground text-sm">
                {loadingDescription}
              </p>
            )}
          </div>
        )}
        <LoadingSkeleton count={skeletonCount} height={skeletonHeight} />
      </div>
    );
  }

  // Error state
  if (isError) {
    if (errorComponent) {
      return <div className={className}>{errorComponent}</div>;
    }

    const errorMsg =
      error?.message || errorDescription || "An unexpected error occurred";

    if (variant === "card") {
      return (
        <Card className={className}>
          <CardContent className="pt-6">
            <ErrorState
              title={errorTitle}
              description={errorMsg}
              showRetry={showRetry && !!refetch}
              retryLabel={retryLabel}
              onRetry={refetch}
            />
          </CardContent>
        </Card>
      );
    }

    return (
      <div className={className}>
        <ErrorState
          title={errorTitle}
          description={errorMsg}
          showRetry={showRetry && !!refetch}
          retryLabel={retryLabel}
          onRetry={refetch}
        />
      </div>
    );
  }

  // Empty state
  if (data && isEmpty(data)) {
    if (emptyComponent) {
      return <div className={className}>{emptyComponent}</div>;
    }

    if (variant === "card") {
      return (
        <Card className={className}>
          <CardContent className="pt-6">
            <EmptyState
              title={emptyTitle}
              description={emptyDescription}
              action={emptyAction}
            />
          </CardContent>
        </Card>
      );
    }

    return (
      <div className={className}>
        <EmptyState
          title={emptyTitle}
          description={emptyDescription}
          action={emptyAction}
        />
      </div>
    );
  }

  // Success state with data
  if (data) {
    return (
      <div className={cn("relative", className)}>
        {/* Refresh indicator */}
        {showRefreshIndicator && (isFetching || isRefetching) && (
          <div className="absolute top-2 right-2 z-10">
            <div className="flex items-center gap-1 rounded-md bg-background/80 px-2 py-1 text-muted-foreground text-xs backdrop-blur-sm">
              <RefreshCw className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          </div>
        )}

        <div className={contentClassName}>{children(data)}</div>
      </div>
    );
  }

  // Fallback - should not reach here
  return null;
}

function LoadingSkeleton({ count, height }: { count: number; height: string }) {
  return (
    <div className="space-y-3">
      {[...Array(count)].map((_, i) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
        <Skeleton key={i} className={cn("w-full", height)} />
      ))}
    </div>
  );
}

function ErrorState({
  title,
  description,
  showRetry,
  retryLabel,
  onRetry,
}: {
  title: string;
  description: string;
  showRetry: boolean;
  retryLabel: string;
  onRetry?: () => void;
}) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription className="mt-2">
        <div className="space-y-3">
          <p>{description}</p>
          {showRetry && onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-8"
            >
              <RefreshCw className="mr-2 h-3 w-3" />
              {retryLabel}
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

function EmptyState({
  title,
  description,
  action,
}: {
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
        <WifiOff className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="mb-2 font-semibold text-lg">{title}</h3>
      <p className="mb-4 max-w-sm text-muted-foreground text-sm">
        {description}
      </p>
      {action && (
        <Button onClick={action.onClick} variant="outline">
          {action.label}
        </Button>
      )}
    </div>
  );
}

// Utility function to create data state from query result
export function createDataState<TData>(queryResult: {
  data: TData | undefined;
  isLoading: boolean;
  isError: boolean;
  error?: Error | null;
  isFetching?: boolean;
  isRefetching?: boolean;
  refetch?: () => void;
}): DataState<TData> {
  return {
    data: queryResult.data,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
    isFetching: queryResult.isFetching,
    isRefetching: queryResult.isRefetching,
    refetch: queryResult.refetch,
  };
}

// Hook for aggregating multiple query states
export function useAggregatedDataState<T extends Record<string, any>>(
  queries: T,
): DataState<{ [K in keyof T]: T[K]["data"] }> {
  const isLoading = Object.values(queries).some((q: any) => q.isLoading);
  const isError = Object.values(queries).some((q: any) => q.isError);
  const error = Object.values(queries).find((q: any) => q.error)?.error;
  const isFetching = Object.values(queries).some((q: any) => q.isFetching);
  const isRefetching = Object.values(queries).some((q: any) => q.isRefetching);

  const data = Object.keys(queries).reduce(
    (acc, key) => {
      acc[key as keyof T] = queries[key].data;
      return acc;
    },
    {} as { [K in keyof T]: T[K]["data"] },
  );

  const refetch = () => {
    Object.values(queries).forEach((q: any) => {
      if (q.refetch) q.refetch();
    });
  };

  return {
    data: Object.values(queries).every((q: any) => q.data !== undefined)
      ? data
      : undefined,
    isLoading,
    isError,
    error,
    isFetching,
    isRefetching,
    refetch,
  };
}
