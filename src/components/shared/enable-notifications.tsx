"use client";

import { <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { usePushNotifications } from "@/hooks/use-push-notifications";

export function EnableNotifications() {
  const { isSupported, permission, isSubscribed, isLoading, subscribe } =
    usePushNotifications();

  if (!isSupported) {
    return null;
  }

  if (isSubscribed) {
    return (
      <Button variant="outline" size="sm" disabled>
        <Bell className="mr-2 h-4 w-4" />
        Notifications enabled
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={subscribe}
      disabled={isLoading || permission === "denied"}
    >
      <Bell className="mr-2 h-4 w-4" />
      {isLoading ? "Enabling..." : "Enable notifications"}
    </Button>
  );
}
