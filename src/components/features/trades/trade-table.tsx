"use client";

import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useTRPC } from "@/components/integrations/trpc/client";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function TradeTable() {
  const trpc = useTRPC();
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Organizations</TableHead>
          <TableHead>Quick Hire</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {trades?.map((trade) => (
          <TableRow key={trade.id}>
            <TableCell>{trade.name}</TableCell>
            <TableCell>{trade.organizationCount}</TableCell>
            <TableCell>{trade.availableForQuickHire ? "Yes" : "No"}</TableCell>
            <TableCell className="text-right">
              <Link
                href={`/admin/trades/${trade.id}/edit`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Edit
              </Link>
              <Link
                href={`/admin/trades/${trade.id}/delete`}
                className={buttonVariants({
                  variant: "destructive",
                })}
              >
                Delete
              </Link>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
