"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import type { Organization } from "@/db/schema";

export default function TradeOrganizations({
  organizations,
}: {
  organizations: Organization[];
}) {
  return (
    <>
      {organizations?.map((org) => (
        <TableRow key={org.id}>
          <TableCell colSpan={3}>{org.name}</TableCell>
        </TableRow>
      ))}
    </>
  );
}
