"use client";

import { ChevronRightIcon } from "lucide-react";
import type { Trade } from "@/db/schema";

export function TradeItem({ trade }: Readonly<{ trade: Trade }>) {
  return (
    <li className="relative flex justify-between py-5">
      <div className="flex min-w-0">
        <p className="font-semibold text-gray-900 text-sm/6 dark:text-white">
          {trade.name}
        </p>
      </div>
      <div className="ml-4 flex flex-shrink-0">
        <div className="flex flex-col">
          <div className="text-gray-500 text-sm leading-5">
            {trade.organizationCount} Organizations
          </div>
        </div>
        <ChevronRightIcon className="ml-4 h-5 w-5 text-gray-400" />
      </div>
    </li>
  );
}
