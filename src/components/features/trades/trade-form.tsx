"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { type SubmitHandler, useForm } from "react-hook-form";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { Trade } from "@/db/schema";
import { type TradeFormData, tradeSchema } from "@/lib/schema";

interface TradeFormProps {
  initialData?: Trade | null | undefined;
}

export function TradeForm({ initialData }: TradeFormProps) {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(tradeSchema),
    defaultValues: {
      name: "",
      availableForQuickHire: false,
    },
    values: {
      name: initialData?.name || "",
      availableForQuickHire: initialData?.availableForQuickHire || false,
    },
  });

  const queryClient = useQueryClient();
  const trpc = useTRPC();
  const listKey = trpc.trades.list.queryKey();

  const createTrade = useMutation(
    trpc.trades.create.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: listKey });
      },
    }),
  );
  const updateTrade = useMutation(
    trpc.trades.update.mutationOptions({
      onSuccess: (data, variables) => {
        queryClient.setQueryData([listKey, { id: variables.id }], data);
      },
    }),
  );

  const mutation = initialData ? updateTrade : createTrade;

  const onSubmit: SubmitHandler<TradeFormData> = async (data, e) => {
    e?.preventDefault();

    mutation.mutate(
      {
        name: data.name,
        availableForQuickHire: data.availableForQuickHire,
        id: initialData?.id || "",
      },
      {
        onSuccess: () => {
          router.push("/admin/trades");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating trade:", error);
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Trade name" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="availableForQuickHire"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Available for Quick Hire</FormLabel>
                <FormDescription>
                  Allow this trade to be used for quick hire jobs without
                  bidding
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <Button type="submit" disabled={mutation.isPending}>
          {mutation.isPending ? "Saving..." : "Save"}
        </Button>
      </form>
    </Form>
  );
}
