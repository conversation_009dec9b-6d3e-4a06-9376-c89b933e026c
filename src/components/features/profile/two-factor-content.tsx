"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useRouter } from "next/navigation";
import { useId, useState } from "react";
import QRCode from "react-qr-code";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";

interface TwoFactorContentProps {
  user: {
    id: string;
    twoFactorEnabled?: boolean | null;
  };
}

export function TwoFactorContent({ user }: TwoFactorContentProps) {
  const router = useRouter();
  const [code, setCode] = useState("");
  const [step, setStep] = useState<"setup" | "verify">("setup");
  const [password, setPassword] = useState("");
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  const id = useId();
  const passwordId = `password-${id}`;

  const enableTwoFactor = useMutation({
    mutationFn: async ({ password }: { password: string }) => {
      const { data, error } = await authClient.twoFactor.enable({
        password,
      });
      if (error) throw new Error(error.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(`Failed to enable 2FA: ${error.message}`);
    },
  });

  const disableTwoFactor = useMutation({
    mutationFn: async ({ password }: { password: string }) => {
      const { error } = await authClient.twoFactor.disable({
        password,
      });
      if (error) throw new Error(error.message);
    },
    onError: (error: Error) => {
      toast.error(`Failed to disable 2FA: ${error.message}`);
    },
  });

  const { data: qr, isLoading } = useQuery({
    queryKey: ["twoFactorSetup", password],
    queryFn: async () => {
      if (!password) return null;
      const result = await enableTwoFactor.mutateAsync({ password });
      return result;
    },
    enabled: !!password && step === "setup",
  });

  const verifyMutation = useMutation({
    mutationFn: async (code: string) => {
      const { data, error } = await authClient.twoFactor.verifyTotp({ code });
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      toast.success("Two-factor authentication enabled successfully");
      router.push("/profile");
    },
    onError: (error: Error) => {
      toast.error(`Verification failed: ${error.message}`);
    },
  });

  const handleVerify = async () => {
    if (!code) {
      toast.error("Please enter the verification code");
      return;
    }

    await authClient.twoFactor.verifyTotp({ code });
  };

  const handleDisable = async () => {
    if (!password) {
      toast.error("Password is required");
      return;
    }

    try {
      await disableTwoFactor.mutateAsync({ password });
      toast.success("Two-factor authentication disabled");
      router.push("/profile");
    } catch (error) {
      if (error instanceof Error) {
        toast.error(`Failed to disable 2FA: ${error.message}`);
      } else {
        toast.error("Failed to disable 2FA");
      }
    } finally {
      setShowPasswordDialog(false);
      setPassword("");
      setIsDisabling(false);
    }
  };

  const handlePasswordSubmit = () => {
    if (isDisabling) {
      handleDisable();
    } else {
      setShowPasswordDialog(false);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="mx-auto max-w-md p-8">
      <Card>
        <CardHeader>
          <CardTitle>
            {user.twoFactorEnabled
              ? "Manage Two-Factor Authentication"
              : "Setup Two-Factor Authentication"}
          </CardTitle>
          <CardDescription>
            {user.twoFactorEnabled
              ? "Your account is protected with two-factor authentication"
              : "Protect your account with an authentication app"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {user.twoFactorEnabled ? (
            <div className="space-y-4">
              <p>
                Two-factor authentication is currently enabled for your account.
              </p>
              <p className="text-muted-foreground text-sm">
                If you want to disable two-factor authentication, click the
                button below. This will make your account less secure.
              </p>
            </div>
          ) : step === "setup" ? (
            <div className="space-y-4">
              {!password ? (
                <Button
                  className="w-full"
                  onClick={() => setShowPasswordDialog(true)}
                >
                  Start Setup
                </Button>
              ) : (
                <>
                  <p className="text-sm">
                    Scan the QR code below with your authentication app (like
                    Google Authenticator, Authy, or 1Password) to set up
                    two-factor authentication.
                  </p>

                  {qr?.totpURI && (
                    <div className="flex justify-center py-4">
                      <QRCode value={qr.totpURI || ""} />
                    </div>
                  )}

                  <Button className="w-full" onClick={() => setStep("verify")}>
                    Continue
                  </Button>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm">
                Enter the 6-digit code from your authentication app to verify
                and enable two-factor authentication.
              </p>

              <InputOTP
                maxLength={6}
                value={code}
                onChange={setCode}
                pattern={REGEXP_ONLY_DIGITS}
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          {user.twoFactorEnabled ? (
            <>
              <Button variant="outline" onClick={() => router.push("/profile")}>
                Back to Profile
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  setIsDisabling(true);
                  setShowPasswordDialog(true);
                }}
              >
                Disable 2FA
              </Button>
            </>
          ) : step === "setup" ? (
            <Button variant="outline" onClick={() => router.push("/profile")}>
              Cancel
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={() => setStep("setup")}>
                Back
              </Button>
              <Button
                onClick={handleVerify}
                disabled={verifyMutation.isPending}
              >
                {verifyMutation.isPending ? "Verifying..." : "Verify & Enable"}
              </Button>
            </>
          )}
        </CardFooter>
      </Card>

      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isDisabling
                ? "Disable Two-Factor Authentication"
                : "Setup Two-Factor Authentication"}
            </DialogTitle>
            <DialogDescription>
              {isDisabling
                ? "Please enter your password to disable two-factor authentication."
                : "Please enter your password to setup two-factor authentication."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor={passwordId}>Password</Label>
              <Input
                id={passwordId}
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPasswordDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handlePasswordSubmit}>
              {isDisabling ? "Disable 2FA" : "Continue"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
