"use client";

import { type Driver, driver } from "driver.js";
import { useEffect, useState } from "react";
import "driver.js/dist/driver.css";
import { useLocalStorage } from "@/hooks/use-local-storage";

interface EnhancedJobWizardTourProps {
  isOpen: boolean;
  currentStep: string;
}

export function EnhancedJobWizardTour({
  isOpen,
  currentStep,
}: EnhancedJobWizardTourProps) {
  const [hasSeenTour, setHasSeenTour] = useLocalStorage(
    "seen-enhanced-job-wizard-tour",
    false,
  );
  const [driverInstance, setDriverInstance] = useState<Driver | null>(null);
  const [currentTourStep, setCurrentTourStep] = useState<string | null>(null);

  // Define tour steps for each wizard step
  const tourSteps = {
    type: [
      {
        element: ".grid.grid-cols-2.gap-6",
        popover: {
          title: "Choose Your Project Type",
          description:
            "Start by selecting the type of project you want to create. Standard projects allow multiple contractors to bid, while Quick <PERSON>re lets you directly hire a professional.",
          side: "bottom" as const,
        },
      },
      {
        element: "button:first-child",
        popover: {
          title: "Standard Project",
          description:
            "Choose this for larger projects where you want to collect and compare multiple bids from different contractors.",
          side: "right" as const,
        },
      },
      {
        element: "button:last-child",
        popover: {
          title: "Quick Hire",
          description:
            "Perfect for small jobs or when you want to hire a specific professional directly without the bidding process.",
          side: "left" as const,
        },
      },
    ],
    basics: [
      {
        element: "[name='name']",
        popover: {
          title: "Project Name",
          description:
            "Give your project a clear, descriptive name that contractors will easily understand.",
          side: "bottom" as const,
        },
      },
      {
        element: "[name='propertyId']",
        popover: {
          title: "Select Property",
          description:
            "Choose which of your properties this project is for. This helps contractors understand the location and context.",
          side: "top" as const,
        },
      },
      {
        element: ".border.rounded-lg.p-4.bg-gray-50",
        popover: {
          title: "Project Templates",
          description:
            "Save time by starting with a pre-built template. Templates include common tasks and estimated budgets for typical projects.",
          side: "left" as const,
        },
      },
    ],
    tasks: [
      {
        element: ".space-y-4",
        popover: {
          title: "Define Your Tasks",
          description:
            "Break down your project into specific tasks. Each task should be assigned to a trade (like plumbing, electrical, etc.).",
          side: "top" as const,
        },
      },
      {
        element: "[name='tasks.0.name']",
        popover: {
          title: "Task Name",
          description:
            "Be specific about what work needs to be done. For example: 'Install new kitchen cabinets' rather than just 'Kitchen work'.",
          side: "bottom" as const,
        },
      },
      {
        element: "[name='tasks.0.tradeId']",
        popover: {
          title: "Select Trade",
          description:
            "Choose the appropriate trade for this task. This helps match your project with the right professionals.",
          side: "top" as const,
        },
      },
      {
        element: "button:contains('Add Another Task')",
        popover: {
          title: "Add More Tasks",
          description:
            "Most projects involve multiple tasks. Add as many as needed to fully describe your project scope.",
          side: "top" as const,
        },
      },
    ],
    budget: [
      {
        element: "[name='budget']",
        popover: {
          title: "Set Your Budget",
          description:
            "Enter the total amount you're planning to spend. This helps contractors provide accurate bids within your range.",
          side: "right" as const,
        },
      },
      {
        element: "[name='startsAt']",
        popover: {
          title: "Project Start Date",
          description:
            "When would you like the project to begin? This is optional but helps contractors plan their schedule.",
          side: "bottom" as const,
        },
      },
      {
        element: "[name='deadline']",
        popover: {
          title: "Bid Deadline",
          description:
            "Set a deadline for contractors to submit their bids. This creates urgency and helps you move forward quickly.",
          side: "bottom" as const,
        },
      },
    ],
    images: [
      {
        element: ".space-y-6",
        popover: {
          title: "Add Project Images",
          description:
            "Photos help contractors better understand your project. Include before photos, reference images, or plans if available.",
          side: "top" as const,
        },
      },
    ],
    review: [
      {
        element: ".space-y-4",
        popover: {
          title: "Review Your Project",
          description:
            "Take a moment to review all the details before creating your project. Make sure everything looks correct.",
          side: "top" as const,
        },
      },
      {
        element: "button:contains('Create Project')",
        popover: {
          title: "Create Your Project",
          description:
            "Once you're satisfied with all the details, click here to create your project and start receiving bids!",
          side: "top" as const,
        },
      },
    ],
  };

  useEffect(() => {
    if (!isOpen || hasSeenTour || !currentStep) return;

    // Clean up previous instance
    if (driverInstance) {
      driverInstance.destroy();
    }

    const steps = tourSteps[currentStep as keyof typeof tourSteps];
    if (!steps || steps.length === 0) return;

    const instance = driver({
      showProgress: true,
      steps,
      onDestroyStarted: () => {
        if (currentStep === "review") {
          setHasSeenTour(true);
        }
      },
      onDestroyed: () => {
        if (currentStep === "review") {
          setHasSeenTour(true);
        }
      },
    });

    setDriverInstance(instance);
    setCurrentTourStep(currentStep);

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      instance.drive();
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [
    isOpen,
    currentStep,
    hasSeenTour,
    setHasSeenTour,
    driverInstance,
    tourSteps[currentStep as keyof typeof tourSteps],
  ]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (driverInstance) {
        driverInstance.destroy();
      }
    };
  }, [driverInstance]);

  return null;
}
