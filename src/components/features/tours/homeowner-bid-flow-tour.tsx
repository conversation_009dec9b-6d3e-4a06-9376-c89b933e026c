"use client";

import { type Driver, driver } from "driver.js";
import { useEffect, useState } from "react";
import "driver.js/dist/driver.css";
import { usePathname, useRouter } from "next/navigation";
import { useLocalStorage } from "@/hooks/use-local-storage";

export function HomeownerBidFlowTour() {
  const [hasSeenTour, setHasSeenTour] = useLocalStorage(
    "seen-bid-flow-tour",
    false,
  );
  const [driverInstance, setDriverInstance] = useState<Driver | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  // Determine which page we're on based on the URL
  const isProjectPage =
    pathname?.includes("/jobs/") && !pathname?.includes("/bids/");
  const isBidPage = pathname?.includes("/bids/");

  useEffect(() => {
    if (hasSeenTour) return;

    // Create driver instance with appropriate steps based on current page
    const instance = driver({
      showProgress: true,
      steps: isProjectPage
        ? [
            {
              element: ".space-y-4", // Target the bids container
              popover: {
                title: "Project Bids",
                description:
                  "Here you can see all bids submitted by contractors for your project.",
                side: "left",
              },
            },
            {
              element: ".BidCard", // Target the first bid card
              popover: {
                title: "Bid Details",
                description:
                  "Each card shows a summary of the bid including price and contractor information.",
                side: "bottom",
              },
            },
            {
              element: "a[href*='/bids/']", // Target the "View Details" link
              popover: {
                title: "View Bid Details",
                description:
                  "Click here to see the full details of a bid and potentially accept it.",
                side: "right",
                onNextClick: () => {
                  // Get the href of the first bid link
                  const bidLink = document.querySelector(
                    "a[href*='/bids/']",
                  ) as HTMLAnchorElement;
                  if (bidLink) {
                    // Save that we're in the middle of a tour
                    localStorage.setItem("bid-flow-tour-active", "true");
                    // Navigate to the bid details page
                    router.push(bidLink.href);
                  }
                  return false; // Prevent default next behavior
                },
              },
            },
          ]
        : isBidPage
          ? [
              {
                element: ".bid-details", // Target the bid details section
                popover: {
                  title: "Bid Information",
                  description:
                    "Review all the details of the bid including price, timeline, and contractor's approach.",
                  side: "left",
                },
              },
              {
                element: ".contractor-info", // Target the contractor info section
                popover: {
                  title: "Contractor Information",
                  description:
                    "Learn about the contractor who submitted this bid, including their distance from your property.",
                  side: "right",
                },
              },
              {
                element: "#chat", // Target the chat section
                popover: {
                  title: "Chat with Contractor",
                  description:
                    "Use the chat to communicate with the contractor, ask questions, and clarify any details.",
                  side: "left",
                },
              },
              {
                element: "a[href*='/accept']", // Target the accept bid button
                popover: {
                  title: "Accept Bid",
                  description:
                    "When you're ready to move forward with this contractor, click here to accept their bid.",
                  side: "top",
                },
              },
            ]
          : [],
      onDestroyStarted: () => {
        setHasSeenTour(true);
        localStorage.removeItem("bid-flow-tour-active");
      },
      onDestroyed: () => {
        setHasSeenTour(true);
        localStorage.removeItem("bid-flow-tour-active");
      },
    });

    setDriverInstance(instance);

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      // Check if we're continuing a tour from another page
      const isTourActive =
        localStorage.getItem("bid-flow-tour-active") === "true";

      if (isProjectPage || (isBidPage && isTourActive)) {
        instance.drive();
      }
    }, 1000);

    return () => {
      clearTimeout(timer);
      if (driverInstance) {
        driverInstance.destroy();
      }
    };
  }, [
    hasSeenTour,
    setHasSeenTour,
    isProjectPage,
    isBidPage,
    router,
    driverInstance,
  ]);

  return (
    <div className="hidden">
      {/* This component doesn't render anything visible */}
    </div>
  );
}
