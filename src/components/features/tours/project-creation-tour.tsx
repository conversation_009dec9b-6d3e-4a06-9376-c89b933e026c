"use client";

import { driver } from "driver.js";
import { useEffect } from "react";
import "driver.js/dist/driver.css";
import { useLocalStorage } from "@/hooks/use-local-storage";

export function ProjectCreationTour() {
  const [hasSeenTour, setHasSeenTour] = useLocalStorage(
    "seen-project-tour",
    false,
  );

  useEffect(() => {
    if (hasSeenTour) return;

    const driverInstance = driver({
      showProgress: true,
      steps: [
        {
          element: "[name='name']",
          popover: {
            title: "Project Name",
            description: "Give your project a descriptive name.",
            side: "bottom",
          },
        },
        {
          element: "#propertyId",
          popover: {
            title: "Select Property",
            description: "Choose which of your properties this project is for.",
            side: "top",
          },
        },
        {
          element: "#tasks",
          popover: {
            title: "Add Tasks",
            description:
              "Break down your project into specific tasks and assign trades.",
            side: "top",
          },
        },
        {
          element: "[name='budget']",
          popover: {
            title: "Set a Budget",
            description:
              "Define how much you're planning to spend on this project.",
            side: "right",
          },
        },
        {
          element: "#startsAt",
          popover: {
            title: "Project Timeline",
            description:
              "Set when you want your project to start and when you would like bids to be submitted by.",
            side: "left",
          },
        },
        {
          element: "[type='submit']",
          popover: {
            title: "Create Your Project",
            description:
              "When you're done, click here to create your project and find professionals.",
            side: "top",
          },
        },
      ],
      onDestroyStarted: () => {
        setHasSeenTour(true);
      },
      onDestroyed: () => {
        setHasSeenTour(true);
      },
    });

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      driverInstance.drive();
    }, 1000);

    return () => {
      clearTimeout(timer);
      driverInstance.destroy();
    };
  }, [hasSeenTour, setHasSeenTour]);

  return (
    <div className="hidden">
      {/* This component doesn't render anything visible */}
    </div>
  );
}
