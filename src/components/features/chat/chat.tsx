"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  AlertCircle,
  Check,
  Clock,
  Edit,
  HelpCircle,
  Send,
  Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { Message } from "@/db/schema";
import { useSession } from "@/lib/auth-client";
import {
  sendTypingIndicator,
  subscribeToChatChannel,
  unsubscribeFromChatChannel,
  updatePresence,
} from "@/lib/pusher-client";

const bidCommands = [
  {
    name: "update-amount",
    description: "Update bid amount",
    example: "/update-amount 1000",
    role: "professional",
  },
  {
    name: "update-duration",
    description: "Update estimated duration",
    example: "/update-duration 14",
    role: "professional",
  },
  {
    name: "accept-bid",
    description: "Accept this bid",
    example: "/accept-bid",
    role: "homeowner",
  },
  {
    name: "withdraw-bid",
    description: "Withdraw this bid",
    example: "/withdraw-bid",
    role: "professional",
  },
  {
    name: "edit-description",
    description: "Update bid description",
    example: "/edit-description New text",
    role: "professional",
  },
  {
    name: "update-name",
    description: "Update job name",
    example: "/update-name New Name",
    role: "homeowner",
  },
  {
    name: "complete-job",
    description: "Mark job as completed",
    example: "/complete-job",
    role: "homeowner",
  },
];

const jobCommands = [
  {
    name: "update-name",
    description: "Update job name",
    example: "/update-name New Name",
    role: "homeowner",
  },
  {
    name: "complete-job",
    description: "Mark job as completed",
    example: "/complete-job",
    role: "homeowner",
  },
  {
    name: "add-task",
    description: "Add a new task to the job",
    example: "/add-task Painting Plumbing",
    role: "homeowner",
  },
];

export function Chat({
  bidId,
  jobId,
  userId,
}: {
  bidId?: string;
  jobId?: string;
  userId: string;
}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [newMessage, setNewMessage] = useState("");
  const [chatId, setChatId] = useState<string>();
  const [typingUsers, setTypingUsers] = useState<Map<string, string>>(
    new Map(),
  );
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageCountRef = useRef<number>(0);
  const router = useRouter();
  const [availableCommands, setAvailableCommands] = useState<
    typeof bidCommands
  >([]);
  const { data: session } = useSession();
  const user = session?.user;
  const isHomeowner = user?.role === "homeowner";
  const isProfessional = user?.role === "contractor";

  // Add a useEffect to determine available commands based on user role
  useEffect(() => {
    const userRole = isHomeowner
      ? "homeowner"
      : isProfessional
        ? "professional"
        : null;

    if (!userRole) return;

    if (bidId) {
      setAvailableCommands(bidCommands.filter((cmd) => cmd.role === userRole));
    } else if (jobId) {
      setAvailableCommands(jobCommands.filter((cmd) => cmd.role === userRole));
    }
  }, [isHomeowner, isProfessional, bidId, jobId]);

  // Fetch messages
  const { data: messages, isLoading } = useQuery(
    trpc.messages.listMessages.queryOptions({
      bidId,
      jobId,
    }),
  );

  const createMessageMutation = useMutation(
    trpc.messages.createMessage.mutationOptions({
      onSuccess: () => {
        setNewMessage("");
      },
    }),
  );

  const ensureChatMutation = useMutation(
    trpc.messages.ensureChat.mutationOptions({
      onSuccess: (chat) => {
        setChatId(chat?.id);
      },
    }),
  );

  // Handle new messages from Pusher
  const handlePusherMessage = useCallback(
    (data: { content: string; isCommand?: boolean; commandData?: string }) => {
      // Invalidate and refetch messages to update the local cache
      queryClient.invalidateQueries({
        queryKey: trpc.messages.listMessages.queryKey({ bidId, jobId }),
      });

      // If this is a command message, refresh relevant data
      if (data.isCommand && data.content.includes("[Command executed:")) {
        // Parse command data if available
        const commandData = data.commandData
          ? JSON.parse(data.commandData)
          : null;

        // Invalidate relevant queries based on the command
        if (bidId) {
          // Invalidate bid data
          queryClient.invalidateQueries({
            queryKey: trpc.bids.getById.queryKey({ id: bidId }),
          });
        }

        if (jobId) {
          // Invalidate job data
          queryClient.invalidateQueries({
            queryKey: trpc.projects.getById.queryKey({ id: jobId }),
          });
        }

        // Refresh the page to show updated data
        router.refresh();
      }
    },
    [queryClient, bidId, jobId, router, trpc],
  );

  // Handle typing indicators from Pusher
  const handlePusherTyping = useCallback(
    (data: { userId: string; isTyping: boolean; userName?: string }) => {
      if (data.userId === userId) return; // Ignore own typing

      setTypingUsers((prev) => {
        const newMap = new Map(prev);
        if (data.isTyping) {
          // Use provided userName or fallback to userId
          newMap.set(data.userId, data.userName || data.userId);
        } else {
          newMap.delete(data.userId);
        }
        return newMap;
      });
    },
    [userId],
  );

  // Handle presence updates from Pusher
  const handlePusherPresence = useCallback(
    (data: { userId: string; status: string }) => {
      if (data.userId === userId) return; // Ignore own presence

      setOnlineUsers((prev) => {
        const newSet = new Set(prev);
        if (data.status === "online") {
          newSet.add(data.userId);
        } else {
          newSet.delete(data.userId);
        }
        return newSet;
      });
    },
    [userId],
  );

  // Extract chatId from messages or create a new chat if none exists
  useEffect(() => {
    if (messages && messages.length > 0) {
      setChatId(messages[0]?.chatId);
    } else if (!isLoading && !chatId) {
      // Ensure a chat exists
      ensureChatMutation.mutate({ bidId, jobId });
    }
  }, [messages, isLoading, chatId, bidId, jobId, ensureChatMutation]);

  // Set up Pusher subscription when chatId is available
  useEffect(() => {
    if (!chatId) return;

    subscribeToChatChannel(chatId, {
      onMessage: handlePusherMessage,
      onTyping: handlePusherTyping,
      onPresence: handlePusherPresence,
      onSubscriptionSucceeded: () => {
        console.log("Successfully subscribed to chat channel");
        // Update presence to online
        updatePresence(chatId, userId, "online");
      },
      onSubscriptionError: (error) => {
        console.error("Failed to subscribe to chat channel:", error);
      },
    });

    // Update presence to online when component mounts
    updatePresence(chatId, userId, "online");

    return () => {
      // Update presence to offline when component unmounts
      updatePresence(chatId, userId, "offline");
      unsubscribeFromChatChannel(chatId);
    };
  }, [
    chatId,
    handlePusherMessage,
    handlePusherTyping,
    handlePusherPresence,
    userId,
  ]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  });

  // Track if we've already sent a typing indicator
  const isAlreadyTyping = useRef(false);

  // Handle typing in message input
  const handleTyping = useCallback(() => {
    if (!chatId) return;

    // Only send typing indicator if we haven't already
    if (!isAlreadyTyping.current) {
      // Get user's name
      const userName = user?.name || userId;

      sendTypingIndicator(chatId, userId, true, userName);
      isAlreadyTyping.current = true;
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(chatId, userId, false);
      isAlreadyTyping.current = false;
    }, 3000);
  }, [chatId, userId, user]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      const [firstName, lastName] = user?.name.split(" ") || [];
      const userInitials = user
        ? `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase()
        : userId.slice(0, 2).toUpperCase();

      await createMessageMutation.mutateAsync({
        bidId,
        jobId,
        content: newMessage.trim(),
        senderInitials: userInitials,
        senderAvatarUrl: user?.image || "",
      });
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const isOwnMessage = (senderId: string) => senderId === userId;

  const fetchSuggestions = useCallback(async () => {
    if (!chatId || !messages?.length) return;

    try {
      console.log("Fetching suggestions for chat:", bidId || jobId);
      const response = await fetch("/api/suggestions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          bidId,
          jobId,
          messageHistory: messages?.slice(-5), // Last 5 messages for context
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Suggestions API error:", errorData);
        return;
      }

      const data = await response.json();
      console.log("Received suggestions:", data.suggestions);
      setSuggestedQuestions(data.suggestions || []);
    } catch (error) {
      console.error("Failed to fetch suggestions:", error);
    }
  }, [bidId, jobId, chatId, messages]);

  useEffect(() => {
    if (messages?.length && chatId) {
      // Only fetch suggestions every 5 messages or on initial load
      if (
        messages.length % 5 === 0 ||
        messages.length !== messageCountRef.current
      ) {
        console.log(
          "Triggering suggestions fetch, message count:",
          messages.length,
        );
        fetchSuggestions();
        messageCountRef.current = messages.length;
      }
    }
  }, [messages, fetchSuggestions, chatId]);

  const renderCommandMessage = (message: Message) => {
    if (!message.isCommand) return null;

    let commandData = null;
    try {
      if (message.commandData) {
        commandData = JSON.parse(message.commandData);
      }
    } catch (e) {
      console.error("Failed to parse command data:", e);
    }

    const isSuccess = message.content.includes("[Command executed:");
    const icon = isSuccess ? (
      <Check className="h-4 w-4 text-green-500" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-500" />
    );

    return (
      <div className="flex items-center gap-2 text-muted-foreground text-sm">
        {icon}
        <span>
          {message.content
            .replace(/\[(Command executed|Command failed): /, "")
            .replace(/\]$/, "")}
        </span>

        {commandData && (
          <Tooltip>
            <TooltipTrigger>
              <div className="ml-2 rounded bg-muted px-2 py-1 text-xs">
                {commandData.amount && <span>${commandData.amount}</span>}
                {commandData.estimatedDuration && (
                  <span>
                    <Clock className="mr-1 inline h-3 w-3" />
                    {commandData.estimatedDuration} days
                  </span>
                )}
                {commandData.status && (
                  <span>Status: {commandData.status}</span>
                )}
                {commandData.name && (
                  <span>
                    <Edit className="mr-1 inline h-3 w-3" />
                    {commandData.name}
                  </span>
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Command details</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-muted-foreground">Loading messages...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col rounded-lg border">
      {/* Header */}
      <div className="flex items-center justify-between border-b bg-muted/50 p-4">
        <h3 className="font-medium">Chat</h3>
        {onlineUsers.size > 0 && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            {onlineUsers.size + 1} online
          </Badge>
        )}
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="h-56 space-y-4 overflow-y-scroll">
          {messages?.map((message) => (
            <div
              key={message.id}
              className={`mb-4 flex ${
                isOwnMessage(message.senderId) ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  isOwnMessage(message.senderId)
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                <div className="mb-1 flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    {message.senderAvatarUrl && (
                      <AvatarImage src={message.senderAvatarUrl} />
                    )}
                    <AvatarFallback>
                      {message.senderInitials || message.senderId.slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="font-medium text-xs">
                    {message.senderType === "homeowner"
                      ? "Homeowner"
                      : "Professional"}
                  </span>
                  <span className="text-muted-foreground text-xs">
                    {format(message.createdAt, "h:mm a")}
                  </span>
                </div>

                {message.isCommand ? (
                  renderCommandMessage(message)
                ) : (
                  <p className="whitespace-pre-wrap">{message.content}</p>
                )}
              </div>
            </div>
          ))}

          {/* Typing indicator */}
          {typingUsers.size > 0 && (
            <div className="flex justify-start">
              <div className="flex items-center gap-2 text-muted-foreground text-sm">
                <div className="flex space-x-1">
                  <div className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground" />
                  <div
                    className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground"
                    style={{ animationDelay: "0.1s" }}
                  />
                  <div
                    className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground"
                    style={{ animationDelay: "0.2s" }}
                  />
                </div>
                <span>
                  {Array.from(typingUsers.values()).join(", ")}{" "}
                  {typingUsers.size === 1 ? "is" : "are"} typing...
                </span>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message input */}
      <form onSubmit={handleSendMessage} className="border-t p-4">
        {suggestedQuestions.length > 0 && (
          <div className="mb-3 flex flex-wrap gap-2">
            <p className="mb-1 w-full text-muted-foreground text-xs">
              Suggested questions:
            </p>
            {suggestedQuestions.map((question) => (
              <Badge
                key={question}
                variant="outline"
                className="cursor-pointer px-2 py-1 text-xs transition-colors hover:bg-accent hover:text-accent-foreground"
                onClick={() => setNewMessage(question)}
              >
                {question}
              </Badge>
            ))}
          </div>
        )}
        <div className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              handleTyping();
            }}
            placeholder="Type a message..."
            disabled={createMessageMutation.isPending}
          />
          <Button
            type="submit"
            size="icon"
            disabled={!newMessage.trim() || createMessageMutation.isPending}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </form>

      {/* Command help section */}
      <div className="mt-1 text-center">
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="link"
              size="sm"
              className="text-muted-foreground text-xs"
            >
              <HelpCircle className="mr-1 h-3 w-3" />
              Available commands
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Available Commands</DialogTitle>
            </DialogHeader>
            {availableCommands.length > 0 ? (
              <dl className="grid grid-cols-2 space-y-1 text-sm">
                {availableCommands.map((command) => (
                  <>
                    <dt key={command.name}>
                      <code className="mr-2">{command.example}</code>
                    </dt>
                    <dd
                      className="text-muted-foreground"
                      key={`${command.name}-description`}
                    >
                      {command.description}
                    </dd>
                  </>
                ))}
              </dl>
            ) : (
              <p className="text-muted-foreground text-sm">
                No commands available for your role.
              </p>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
