"use client";

import { useChat } from "@ai-sdk/react";
import { createIdGenerator } from "ai";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Clock,
  MessageCircle,
  Send,
  Sparkles,
  User,
  Zap,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import jackAvatar from "@/assets/images/jack.png";
import { SmartQuickActions } from "@/components/features/chat/smart-quick-actions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { cn, formatChatDate } from "@/lib/utils";

interface EnhancedPopupChatProps {
  title?: string;
  supportName?: string;
  supportAvatar?: string;
  userAvatar?: string | null | undefined;
  userName?: string | null | undefined;
  userRole?: string;
  apiEndpoint?: string;
  className?: string;
}

// Context-aware greeting function

function getContextualGreeting(
  pathname: string,
  userName: string,
  userRole: string,
) {
  if (pathname.includes("/projects/") && !pathname.includes("/new")) {
    return `Hi ${userName}! I can see you're looking at a project. How can I help you with this one?`;
  }
  if (pathname.includes("/dashboard")) {
    return `Welcome back ${userName}! Ready to tackle your ${userRole === "homeowner" ? "home projects" : "business"} today?`;
  }
  if (pathname.includes("/properties")) {
    return `Hi ${userName}! I'm here to help you manage your properties and plan improvements.`;
  }
  if (pathname.includes("/bids")) {
    return `Hi ${userName}! Let me help you ${userRole === "homeowner" ? "evaluate bids" : "optimize your bidding strategy"}.`;
  }
  return `Hi ${userName}! I'm Jack, your AI assistant. How can I help you today?`;
}

export function EnhancedPopupChat({
  title = "Ask Jack",
  supportName = "Jack",
  supportAvatar = "",
  userName = "You",
  userAvatar = "",
  userRole = "homeowner",
  apiEndpoint = "/api/chat",
  className,
}: EnhancedPopupChatProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [chatId, setChatId] = useLocalStorage<string>("popup-chat-id", "");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  useEffect(() => {
    const initChat = async () => {
      if (!chatId) {
        try {
          const response = await fetch("/api/chat/create", {
            method: "POST",
          });
          if (response.ok) {
            const data = await response.json();
            setChatId(data.chatId);
          }
        } catch (error) {
          console.error("Failed to create chat:", error);
        }
      }
    };

    initChat();
  }, [chatId, setChatId]);

  supportAvatar = jackAvatar.src;

  const { messages, input, handleInputChange, handleSubmit, isLoading } =
    useChat({
      api: apiEndpoint,
      id: chatId,
      generateId: createIdGenerator({
        prefix: "msgc",
        size: 16,
      }),
      sendExtraMessageFields: true,
      experimental_prepareRequestBody: ({ messages }) => {
        const lastMessage = messages[messages.length - 1];
        return {
          message: lastMessage,
          chatId,
          context: {
            pathname,
            userRole,
          },
        };
      },
      onFinish: () => {
        setIsTyping(false);
      },
    });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  });

  // Show typing indicator when loading
  useEffect(() => {
    if (isLoading) {
      setIsTyping(true);
    }
  }, [isLoading]);

  const contextualGreeting = getContextualGreeting(
    pathname,
    userName || "there",
    userRole,
  );

  const handleQuickAction = (action: string) => {
    const actionMessages = {
      // Context-aware actions
      "manage-current-project":
        "Help me manage this project - what should I focus on?",
      "find-contractors-current":
        "Help me find qualified contractors for this specific project",
      "schedule-current-project":
        "Can you help me create a timeline for this project?",
      "bid-current-project":
        "Help me create a competitive bid for this project",
      "analyze-current-project":
        "Analyze this project's requirements and complexity",
      "check-rates-current": "What are typical rates for projects like this?",
      "review-active-projects": "Show me an overview of my active projects",
      "check-project-updates":
        "Are there any important updates on my projects?",
      "show-opportunities":
        "Show me new bidding opportunities that match my skills",
      "review-performance":
        "How is my contractor performance and what can I improve?",
      "check-pending-bids": "Review my pending bids and their status",
      "evaluate-bids": "Help me evaluate and compare the bids I've received",
      "compare-contractors": "Compare the qualifications of these contractors",
      "negotiate-terms": "Help me negotiate better terms with contractors",
      "improve-bidding": "How can I improve my bidding strategy?",
      "track-bid-performance": "Show me analytics on my bidding performance",
      "find-similar-projects":
        "Find projects similar to ones I've successfully completed",
      "add-property": "Help me add a new property to my account",
      "analyze-property-trends": "Show me property value trends in my area",
      "plan-improvements": "Help me plan cost-effective property improvements",

      // Default actions
      "create-project": "Help me create a new home improvement project",
      "find-contractors": "How do I find qualified contractors for my project?",
      "project-timeline": "Can you help me plan a timeline for my project?",
      "find-jobs": "Show me relevant projects I can bid on",
      "analyze-competition": "Help me analyze the competition for bidding",
      "check-performance": "How is my contractor performance?",
      "platform-insights": "Show me platform analytics and insights",
      "user-stats": "Display user statistics",
      "manage-users": "Help me manage platform users",
      "explore-features": "What features are available to me?",
      "get-help": "I need help getting started",
    };

    const message = actionMessages[action as keyof typeof actionMessages];
    if (message) {
      handleInputChange({ target: { value: message } } as any);
    }
  };

  return (
    <>
      {/* Floating button to open chat - hidden on mobile for project pages */}
      <Button
        data-chat-trigger
        onClick={() => setIsOpen(true)}
        className="fixed right-6 bottom-6 z-50 hidden h-14 w-14 rounded-full border-2 border-tradecrews-orange/20 bg-tradecrews-blue p-0 shadow-lg hover:bg-tradecrews-blue-600 lg:block"
      >
        <div className="relative">
          <MessageCircle className="m-auto h-6 w-6 text-foreground" />
        </div>
      </Button>

      {/* Chat dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen} modal={false}>
        <DialogContent
          className={cn(
            "fixed top-auto right-6 bottom-6 left-auto flex h-[600px] w-96 translate-x-0 translate-y-0 flex-col border-2 border-tradecrews-blue/20 p-0 sm:w-[420px]",
            className,
          )}
        >
          {/* Enhanced Header */}
          <DialogHeader className="flex flex-row items-center justify-between border-b bg-gradient-to-r from-tradecrews-blue-50 to-tradecrews-orange-50 p-4 dark:from-tradecrews-blue-950/30 dark:to-tradecrews-orange-950/30">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="h-10 w-10 border-2 border-tradecrews-blue">
                  {supportAvatar && <AvatarImage src={supportAvatar} />}
                  <AvatarFallback className="bg-tradecrews-blue text-white">
                    <Bot className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
                <div className="-bottom-1 -right-1 absolute flex h-5 w-5 items-center justify-center rounded-full border-2 border-background bg-tradecrews-orange">
                  <Sparkles className="h-3 w-3 text-white" />
                </div>
              </div>
              <div>
                <DialogTitle className="text-foreground">{title}</DialogTitle>
                <p className="text-muted-foreground text-xs">
                  {isTyping
                    ? "Jack is typing..."
                    : "AI Assistant • Always here to help"}
                </p>
              </div>
            </div>
            <Badge
              variant="secondary"
              className="bg-tradecrews-orange-100 text-tradecrews-orange-700 dark:bg-tradecrews-orange-900/50 dark:text-tradecrews-orange-300"
            >
              Online
            </Badge>
          </DialogHeader>

          {/* Messages */}
          <ScrollArea className="flex-1 overflow-y-scroll p-4">
            <div className="space-y-4">
              {/* Show enhanced introduction when no messages yet */}
              {messages.length === 0 && (
                <div className="space-y-4">
                  <Card className="border-l-4 border-l-tradecrews-blue bg-gradient-to-br from-tradecrews-blue-50 via-background to-tradecrews-orange-50 dark:from-tradecrews-blue-950/30 dark:via-background dark:to-tradecrews-orange-950/30">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Bot className="h-5 w-5 text-tradecrews-blue" />
                          <p className="font-medium text-foreground">
                            {contextualGreeting}
                          </p>
                        </div>
                        <SmartQuickActions
                          userRole={userRole}
                          onQuickAction={handleQuickAction}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Enhanced Message Cards */}
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  {message.role === "assistant" && (
                    <Avatar className="h-8 w-8 border-2 border-tradecrews-blue/20">
                      <AvatarImage src={supportAvatar} />
                      <AvatarFallback className="bg-tradecrews-blue text-white">
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}

                  <Card
                    className={`max-w-[85%] ${
                      message.role === "user"
                        ? "border-tradecrews-blue bg-tradecrews-blue text-white"
                        : "border-border bg-card"
                    }`}
                  >
                    <CardContent className="p-3">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-xs">
                            {message.role === "user" ? userName : supportName}
                          </span>
                          {message.role === "assistant" && (
                            <Badge
                              variant="secondary"
                              className="bg-tradecrews-orange-100 text-tradecrews-orange-700 text-xs dark:bg-tradecrews-orange-900/50 dark:text-tradecrews-orange-300"
                            >
                              AI
                            </Badge>
                          )}
                        </div>

                        <div
                          className={
                            message.role === "user"
                              ? "text-white"
                              : "text-foreground"
                          }
                        >
                          {message.parts.map((part, i) => {
                            switch (part.type) {
                              case "text":
                                return (
                                  <div
                                    key={`${message.id}-part-${i}`}
                                    className="prose prose-sm dark:prose-invert max-w-none"
                                  >
                                    {part.text}
                                  </div>
                                );
                              case "tool-invocation": {
                                const { toolInvocation } = part;
                                return toolInvocation.state ===
                                  "result" ? null : (
                                  <div className="flex items-center gap-2 text-muted-foreground text-xs">
                                    <Zap className="h-3 w-3 animate-pulse" />
                                    <span className="animate-pulse">
                                      Working on it...
                                    </span>
                                  </div>
                                );
                              }
                            }
                          })}
                        </div>

                        {message.createdAt && (
                          <div className="flex items-center gap-1 text-xs opacity-70">
                            <Clock className="h-3 w-3" />
                            <span>{formatChatDate(message.createdAt)}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {message.role === "user" && (
                    <Avatar className="h-8 w-8 border-2 border-tradecrews-orange/20">
                      <AvatarImage src={userAvatar as string} />
                      <AvatarFallback className="bg-tradecrews-orange text-white">
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex justify-start gap-3">
                  <Avatar className="h-8 w-8 border-2 border-tradecrews-blue/20">
                    <AvatarImage src={supportAvatar} />
                    <AvatarFallback className="bg-tradecrews-blue text-white">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <Card className="border-border bg-card">
                    <CardContent className="p-3">
                      <div className="flex items-center gap-2">
                        <div className="flex space-x-1">
                          <div className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue [animation-delay:-0.3s]" />
                          <div className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue [animation-delay:-0.15s]" />
                          <div className="h-2 w-2 animate-bounce rounded-full bg-tradecrews-blue" />
                        </div>
                        <span className="text-muted-foreground text-xs">
                          Jack is thinking...
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Enhanced Message Input */}
          <form onSubmit={handleSubmit} className="border-t bg-background p-4">
            <div className="flex gap-3">
              <div className="relative flex-1">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask Jack anything..."
                  className="border-tradecrews-blue/20 pr-12 focus:border-tradecrews-blue"
                  disabled={isLoading}
                />
                {input && (
                  <Button
                    type="submit"
                    size="sm"
                    disabled={isLoading}
                    className="absolute top-1 right-1 h-8 w-8 bg-tradecrews-blue p-0 hover:bg-tradecrews-blue-600"
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                )}
              </div>
              {!input && (
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
                >
                  <Send className="h-4 w-4" />
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
