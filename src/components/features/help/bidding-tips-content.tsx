"use client";

import {
  CheckCircleIcon,
  DollarSignIcon,
  LightbulbIcon,
  MessageSquareIcon,
  ShieldCheckIcon,
  TrendingUpIcon,
  UserIcon,
  UsersIcon,
} from "lucide-react";
import { JackChatButton } from "@/components/features/chat/jack-chat-button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSession } from "@/lib/auth-client";

export function BiddingTipsContent() {
  const { data: session } = useSession();
  const isHomeowner = session?.user.role === "homeowner";
  const isContractor = session?.user.role === "contractor";

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-50 dark:bg-orange-950/20">
          <TrendingUpIcon className="h-8 w-8 text-orange-600" />
        </div>
        <h1 className="mb-4 font-bold text-3xl text-foreground sm:text-4xl">
          Bidding Tips & Strategies
        </h1>
        <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
          Master the art of bidding with expert tips tailored to your role. Get
          the best value and win more projects.
        </p>
      </div>

      {/* Role-specific content */}
      {isHomeowner && <HomeownerTips />}
      {isContractor && <ContractorTips />}
      {!isHomeowner && !isContractor && (
        <>
          <HomeownerTips />
          <ContractorTips />
        </>
      )}

      {/* General Tips */}
      <GeneralTips />

      {/* AI Assistant CTA */}
      <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
        <CardContent className="p-6">
          <div className="flex flex-col items-center gap-4 text-center sm:flex-row sm:text-left">
            <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30">
              <MessageSquareIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div className="flex-1">
              <h3 className="mb-2 font-semibold text-foreground">
                Need Personalized Help?
              </h3>
              <p className="text-muted-foreground">
                Chat with Jack, our AI assistant, for specific bidding advice
                tailored to your projects.
              </p>
            </div>
            <JackChatButton
              variant="button"
              size="lg"
              showLabel
              className="bg-orange-600 text-white hover:bg-orange-700"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function HomeownerTips() {
  const tips = [
    {
      icon: DollarSignIcon,
      title: "Compare Total Value, Not Just Price",
      description:
        "Look beyond the bottom line. Consider timeline, materials quality, warranty, and contractor experience.",
      details: [
        "Review what's included in each bid",
        "Check if permits and cleanup are covered",
        "Compare material specifications",
        "Factor in timeline differences",
      ],
    },
    {
      icon: ShieldCheckIcon,
      title: "Verify Contractor Credentials",
      description:
        "Ensure contractors are properly licensed, insured, and have good reviews.",
      details: [
        "Check license status and expiration",
        "Verify insurance coverage",
        "Read recent customer reviews",
        "Ask for local references",
      ],
    },
    {
      icon: CheckCircleIcon,
      title: "Red Flags to Avoid",
      description: "Watch out for these warning signs when evaluating bids.",
      details: [
        "Significantly lower bids without explanation",
        "Requests for large upfront payments",
        "Door-to-door solicitation",
        "No written contract or warranty",
      ],
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <UserIcon className="h-5 w-5 text-blue-600" />
        <h2 className="font-semibold text-foreground text-xl">
          For Homeowners
        </h2>
        <Badge variant="secondary">Evaluating Bids</Badge>
      </div>

      <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
        {tips.map((tip) => (
          <TipCard key={tip.title} {...tip} />
        ))}
      </div>
    </div>
  );
}

function ContractorTips() {
  const tips = [
    {
      icon: TrendingUpIcon,
      title: "Create Competitive Bids",
      description:
        "Stand out from the competition with detailed, professional proposals.",
      details: [
        "Provide detailed scope of work",
        "Break down costs transparently",
        "Include timeline with milestones",
        "Highlight your unique value proposition",
      ],
    },
    {
      icon: LightbulbIcon,
      title: "Pricing Strategies",
      description:
        "Price your services competitively while maintaining profitability.",
      details: [
        "Research local market rates",
        "Factor in all costs including overhead",
        "Consider project complexity",
        "Build in contingency for unexpected issues",
      ],
    },
    {
      icon: UsersIcon,
      title: "Build Trust & Credibility",
      description:
        "Establish confidence with homeowners through professionalism.",
      details: [
        "Respond promptly to inquiries",
        "Provide detailed written estimates",
        "Share portfolio of similar projects",
        "Offer references from recent clients",
      ],
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <UsersIcon className="h-5 w-5 text-green-600" />
        <h2 className="font-semibold text-foreground text-xl">
          For Contractors
        </h2>
        <Badge variant="secondary">Winning Bids</Badge>
      </div>

      <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
        {tips.map((tip) => (
          <TipCard key={tip.title} {...tip} />
        ))}
      </div>
    </div>
  );
}

function GeneralTips() {
  const tips = [
    "Communicate clearly and promptly throughout the bidding process",
    "Ask questions to clarify project requirements and expectations",
    "Document all agreements and changes in writing",
    "Set realistic timelines and stick to commitments",
    "Use TradeCrews messaging system for all project communications",
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquareIcon className="h-5 w-5 text-purple-600" />
          Communication Best Practices
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {tips.map((tip) => (
            <li key={tip} className="flex items-start gap-3">
              <CheckCircleIcon className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
              <span className="text-foreground">{tip}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

interface TipCardProps {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  details: string[];
}

function TipCard({ icon: Icon, title, description, details }: TipCardProps) {
  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
            <Icon className="h-5 w-5 text-muted-foreground" />
          </div>
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">{description}</p>
        <ul className="space-y-2">
          {details.map((detail) => (
            <li key={detail} className="flex items-start gap-2 text-sm">
              <CheckCircleIcon className="mt-0.5 h-3 w-3 flex-shrink-0 text-green-500" />
              <span className="text-muted-foreground">{detail}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
