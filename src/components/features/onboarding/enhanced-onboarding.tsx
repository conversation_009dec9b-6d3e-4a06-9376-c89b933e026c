"use client";

import { ArrowR<PERSON>, Building2, CheckCircle, Home } from "lucide-react";
import { useRouter } from "next/navigation";
import { usePostHog } from "posthog-js/react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { ContractorForm } from "@/components/features/contractors/contractor-form";
import { PropertyWizard } from "@/components/features/properties/property-wizard";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  createOnboardingAnalytics,
  createTimeTracker,
} from "@/lib/analytics/onboarding-analytics";
import { authClient, useSession } from "@/lib/auth-client";

type OnboardingStep = "welcome" | "setup" | "complete";

interface EnhancedOnboardingProps {
  userRole: "homeowner" | "contractor";
}

export function EnhancedOnboarding({ userRole }: EnhancedOnboardingProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const posthog = usePostHog();
  const [step, setStep] = useState<OnboardingStep>("welcome");
  const [showPropertyWizard, setShowPropertyWizard] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);
  const [stepTimeTracker, setStepTimeTracker] = useState(createTimeTracker());
  const [onboardingStartTime] = useState(Date.now());

  const userId = session?.user?.id;
  const onboardingAnalytics = createOnboardingAnalytics(posthog);

  // Track onboarding start when component mounts
  useEffect(() => {
    onboardingAnalytics.trackOnboardingStarted(userRole, userId);
  }, [userRole, userId, onboardingAnalytics]);

  // Track step views
  useEffect(() => {
    onboardingAnalytics.trackOnboardingStepViewed(step, userRole, userId);
  }, [step, userRole, userId, onboardingAnalytics]);

  const getStepNumber = (currentStep: OnboardingStep): number => {
    const steps = ["welcome", "setup", "complete"];
    return steps.indexOf(currentStep) + 1;
  };

  const getProgress = (): number => {
    return (getStepNumber(step) / 3) * 100;
  };

  const handleStepChange = (newStep: OnboardingStep) => {
    // Track completion of current step
    const timeSpent = stepTimeTracker.getTimeSpent();
    onboardingAnalytics.trackOnboardingStepCompleted(
      step,
      userRole,
      timeSpent,
      userId,
    );

    // Move to new step and reset timer
    setStep(newStep);
    setStepTimeTracker(createTimeTracker());
  };

  const completeOnboarding = async () => {
    setIsCompleting(true);
    try {
      // Track completion of final step
      const timeSpent = stepTimeTracker.getTimeSpent();
      onboardingAnalytics.trackOnboardingStepCompleted(
        step,
        userRole,
        timeSpent,
        userId,
      );

      // Track overall onboarding completion
      const totalTime = Math.round((Date.now() - onboardingStartTime) / 1000);
      onboardingAnalytics.trackOnboardingCompleted(userRole, totalTime, userId);

      // Update user onboarding status
      await authClient.updateUser({
        onboardingComplete: true,
      });

      toast.success("Welcome to TradeCrews!", {
        description: "Your account is now set up and ready to use.",
      });

      // Client-side redirect to dashboard
      router.push("/dashboard");
    } catch (error) {
      console.error("Error completing onboarding:", error);
      onboardingAnalytics.trackOnboardingError(
        step,
        error instanceof Error ? error.message : "Unknown error",
        userRole,
        userId,
      );
      toast.error("Something went wrong. Please try again.");
      setIsCompleting(false);
    }
  };

  const handleSetupComplete = async () => {
    handleStepChange("complete");
  };

  const handlePropertyCreated = () => {
    // Track property creation
    const timeSpent = stepTimeTracker.getTimeSpent();
    onboardingAnalytics.trackPropertyCreated("residential", timeSpent, userId);

    setShowPropertyWizard(false);
    handleSetupComplete();
  };

  const handleOrganizationCreated = () => {
    // Track contractor profile creation
    const timeSpent = stepTimeTracker.getTimeSpent();
    onboardingAnalytics.trackContractorProfileCreated(timeSpent, userId);

    handleSetupComplete();
  };

  const handlePropertyWizardOpen = () => {
    onboardingAnalytics.trackPropertyWizardOpened("onboarding", userId);
    setShowPropertyWizard(true);
  };

  // Track abandonment on unmount if not completed
  useEffect(() => {
    return () => {
      if (step !== "complete" && !isCompleting) {
        const timeSpent = stepTimeTracker.getTimeSpent();
        onboardingAnalytics.trackOnboardingAbandoned(
          step,
          userRole,
          timeSpent,
          userId,
        );
      }
    };
  }, [
    step,
    userRole,
    userId,
    stepTimeTracker,
    isCompleting,
    onboardingAnalytics,
  ]);

  if (step === "welcome") {
    return (
      <div className="mx-auto max-w-2xl space-y-6">
        <div className="space-y-4 text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100">
            {userRole === "homeowner" ? (
              <Home className="h-8 w-8 text-orange-600" />
            ) : (
              <Building2 className="h-8 w-8 text-orange-600" />
            )}
          </div>
          <div>
            <h1 className="font-bold text-3xl">Welcome to TradeCrews!</h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {userRole === "homeowner"
                ? "Let's get your home projects started"
                : "Let's get your contracting business set up"}
            </p>
          </div>
        </div>

        <Progress value={getProgress()} className="w-full" />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="outline">Step 1 of 3</Badge>
              Getting Started
            </CardTitle>
            <CardDescription>
              {userRole === "homeowner"
                ? "As a homeowner, you can create projects, get bids from qualified contractors, and manage your home improvements all in one place."
                : "As a contractor, you can find projects in your area, submit competitive bids, and grow your business with qualified leads."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              {userRole === "homeowner" ? (
                <>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Create Projects</p>
                      <p className="text-muted-foreground text-sm">
                        Post detailed project descriptions and get multiple bids
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Compare Contractors</p>
                      <p className="text-muted-foreground text-sm">
                        Review profiles, ratings, and proposals to find the best
                        fit
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Manage Projects</p>
                      <p className="text-muted-foreground text-sm">
                        Track progress, communicate, and handle payments
                        securely
                      </p>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Find Projects</p>
                      <p className="text-muted-foreground text-sm">
                        Browse projects in your area that match your skills
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Submit Bids</p>
                      <p className="text-muted-foreground text-sm">
                        Create competitive proposals with detailed estimates
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 rounded-lg border p-3">
                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium">Grow Your Business</p>
                      <p className="text-muted-foreground text-sm">
                        Build your reputation and get more qualified leads
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>

            <Button
              onClick={() => handleStepChange("setup")}
              className="w-full"
              size="lg"
            >
              Let's Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === "setup") {
    return (
      <div className="mx-auto max-w-2xl space-y-6">
        <div className="space-y-2 text-center">
          <h2 className="font-bold text-2xl">Set Up Your Account</h2>
          <p className="text-muted-foreground">
            {userRole === "homeowner"
              ? "Add your first property to start creating projects"
              : "Create your contractor profile to start bidding on projects"}
          </p>
        </div>

        <Progress value={getProgress()} className="w-full" />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="outline">Step 2 of 3</Badge>
              {userRole === "homeowner"
                ? "Add Your First Property"
                : "Create Your Organization"}
            </CardTitle>
            <CardDescription>
              {userRole === "homeowner"
                ? "This will be where you manage projects and improvements. You can add more properties later."
                : "Set up your contractor organization profile. This helps homeowners learn about your business."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {userRole === "homeowner" ? (
              <>
                <div className="py-8 text-center">
                  <Home className="mx-auto mb-4 h-12 w-12 text-orange-500" />
                  <p className="mb-6 text-muted-foreground">
                    We'll guide you through adding your property step-by-step
                  </p>
                  <Button
                    onClick={handlePropertyWizardOpen}
                    size="lg"
                    variant="tc_orange"
                  >
                    Add Property
                  </Button>
                </div>

                <PropertyWizard
                  open={showPropertyWizard}
                  onOpenChange={setShowPropertyWizard}
                  onSuccess={handlePropertyCreated}
                />
              </>
            ) : (
              <ContractorForm
                onboarding
                onSuccess={handleOrganizationCreated}
              />
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === "complete") {
    return (
      <div className="mx-auto max-w-2xl space-y-6">
        <div className="space-y-4 text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h2 className="font-bold text-3xl">You're All Set!</h2>
            <p className="mt-2 text-lg text-muted-foreground">
              Your account is ready to use
            </p>
          </div>
        </div>

        <Progress value={100} className="w-full" />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="outline">Step 3 of 3</Badge>
              Welcome to TradeCrews
            </CardTitle>
            <CardDescription>
              {userRole === "homeowner"
                ? "You can now create projects and start getting bids from qualified contractors."
                : "You can now browse projects and start submitting bids to grow your business."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="py-4 text-center">
              <p className="mb-6 text-muted-foreground">
                {userRole === "homeowner"
                  ? "Ready to create your first project?"
                  : "Ready to find your first project?"}
              </p>
            </div>

            <div className="space-y-3">
              <Button
                onClick={completeOnboarding}
                className="w-full"
                size="lg"
                disabled={isCompleting}
              >
                {isCompleting ? "Setting up..." : "Go to Dashboard"}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>

              <p className="text-center text-muted-foreground text-sm">
                You can always access help and tutorials from your dashboard
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
