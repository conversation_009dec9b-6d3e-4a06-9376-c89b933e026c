"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Star } from "lucide-react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ProjectReviewsProps {
  projectId: string;
}

export function ProjectReviews({ projectId }: ProjectReviewsProps) {
  const trpc = useTRPC();

  const { data: reviews, isLoading } = useQuery(
    trpc.reviews.listForJob.queryOptions({ jobId: projectId }),
  );

  if (isLoading) {
    return <div>Loading reviews...</div>;
  }

  if (!reviews || reviews.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            No reviews yet for this project.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Separate reviews by type
  const homeownerReviews = reviews.filter(
    (r) => r.reviewType === "HOMEOWNER_REVIEW",
  );
  const contractorReviews = reviews.filter(
    (r) => r.reviewType === "CONTRACTOR_REVIEW",
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Reviews</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {homeownerReviews.length > 0 && (
          <div>
            <h3 className="mb-2 font-medium text-lg">Homeowner Review</h3>
            {homeownerReviews.map((review) => (
              <div key={review.id} className="rounded-md border p-4">
                <div className="mb-2 flex items-center">
                  <div className="mr-2 flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= review.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-muted-foreground text-sm">
                    {format(review.createdAt, "PPP")}
                  </span>
                </div>
                <p>{review.comment}</p>
              </div>
            ))}
          </div>
        )}

        {contractorReviews.length > 0 && (
          <div>
            <h3 className="mb-2 font-medium text-lg">Contractor Review</h3>
            {contractorReviews.map((review) => (
              <div key={review.id} className="rounded-md border p-4">
                <div className="mb-2 flex items-center">
                  <div className="mr-2 flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= review.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-muted-foreground text-sm">
                    {format(review.createdAt, "PPP")}
                  </span>
                </div>
                <p>{review.comment}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
