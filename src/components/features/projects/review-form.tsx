"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Star } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod/v4";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

interface ReviewFormProps {
  projectId: string;
  reviewType: "HOMEOWNER_REVIEW" | "CONTRACTOR_REVIEW";
  targetName: string;
}

const reviewSchema = z.object({
  rating: z.number().min(1, "Please select a rating").max(5),
  comment: z.string().min(1, "Please provide a comment"),
});

type FormSchema = z.infer<typeof reviewSchema>;

export function ReviewForm({
  projectId,
  reviewType,
  targetName,
}: ReviewFormProps) {
  const router = useRouter();
  const trpc = useTRPC();

  const form = useForm({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      rating: 0,
      comment: "",
    },
  });

  const [hoveredRating, setHoveredRating] = useState(0);

  const submitReview = useMutation(
    trpc.reviews.create.mutationOptions({
      onSuccess: () => {
        toast.success("Review submitted successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error submitting review: ${error.message}`);
      },
    }),
  );

  const onSubmit = (data: FormSchema) => {
    submitReview.mutate({
      jobId: projectId,
      rating: data.rating,
      comment: data.comment,
      reviewType,
    });
  };

  const reviewTypeLabel =
    reviewType === "HOMEOWNER_REVIEW"
      ? "Review Homeowner"
      : "Review Contractor";

  return (
    <Card>
      <CardHeader>
        <CardTitle>{reviewTypeLabel}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="rating"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    How was your experience with {targetName}?
                  </FormLabel>
                  <FormControl>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          type="button"
                          onClick={() => field.onChange(star)}
                          onMouseEnter={() => setHoveredRating(star)}
                          onMouseLeave={() => setHoveredRating(0)}
                          className="focus:outline-none"
                        >
                          <Star
                            className={`h-8 w-8 ${
                              star <= (hoveredRating || field.value)
                                ? "fill-yellow-400 text-yellow-400"
                                : "text-gray-300"
                            }`}
                          />
                        </button>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comments</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Share your experience..."
                      className="min-h-[100px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={submitReview.isPending}
              className="w-full"
            >
              {submitReview.isPending ? "Submitting..." : "Submit Review"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
