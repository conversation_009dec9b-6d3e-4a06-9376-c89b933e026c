"use client";

import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useTRPC } from "@/components/integrations/trpc/client";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatShortDate } from "@/lib/utils";

export function ProjectTable() {
  const trpc = useTRPC();
  const { data: projects } = useQuery(trpc.projects.listForUser.queryOptions());

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Starts At</TableHead>
          <TableHead>Deadline</TableHead>
          <TableHead>Budget</TableHead>
          <TableHead>Status</TableHead>

          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {projects?.map((project) => (
          <TableRow key={project.id} className="border-b">
            <TableCell>{project.name}</TableCell>
            <TableCell>{formatShortDate(project.startsAt)}</TableCell>
            <TableCell>{formatShortDate(project.deadline)}</TableCell>
            <TableCell>${project.budget}</TableCell>
            <TableCell>{project.status}</TableCell>
            <TableCell className="text-right">
              <Link
                href={`/projects/${project.id}/edit`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Edit
              </Link>
              <Link
                href={`/projects/${project.id}/delete`}
                className={buttonVariants({
                  variant: "destructive",
                })}
              >
                Delete
              </Link>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
