import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { buttonVariants } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import type { Job } from "@/db/schema";
import { cn, getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";

export function ProjectCard({ project }: { project: Job }) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-48 w-full overflow-hidden">
          <Image
            src={project.property?.imageUrl || ""}
            alt={project.property?.name || "Property"}
            width={400}
            height={300}
            className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
            priority={false}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="font-semibold">Property: {project.property?.name}</h3>
          <Badge variant="outline">${project.budget}</Badge>
        </div>
        Bids: {project.bidsCount}
        <br />
        Status:{" "}
        <div className="mt-2 flex flex-wrap gap-2">
          <Badge
            variant={getStatusVariant(project.status, JOB_STATUS_VARIANTS)}
          >
            {project.status}
          </Badge>
          {project.jobType === "QUICK_HIRE" && (
            <Badge variant="secondary">Quick Hire</Badge>
          )}
          {project.isRecurring && (
            <Badge variant="outline">
              {project.recurringFrequency} Service
            </Badge>
          )}
        </div>
        <br />
        Created: {format(project.createdAt, "PPP")}
        {/* Rest of the card content */}
      </CardContent>
      <CardFooter className="border-t bg-muted/30 p-4">
        <div className="flex w-full gap-2">
          <Link
            className={cn(
              buttonVariants({ variant: "tc_blue", size: "sm" }),
              "flex-1",
            )}
            href={`/projects/${project.id}`}
          >
            View Details
          </Link>
          {!(
            project.status === "AWARDED" || project.status === "COMPLETED"
          ) && (
            <Link
              className={cn(
                buttonVariants({ variant: "tc_orange", size: "sm" }),
                "flex-1",
              )}
              href={`/projects/${project.id}/edit`}
            >
              Edit
            </Link>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
