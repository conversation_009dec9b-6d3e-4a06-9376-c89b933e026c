"use client";

import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import type { JobImage } from "@/db/schema";

interface ImageViewerDialogProps {
  images: Partial<JobImage>[];
  initialIndex?: number;
  children: React.ReactNode;
  title?: string;
}

export function ImageViewerDialog({
  images,
  initialIndex = 0,
  children,
  title = "Project Images",
}: ImageViewerDialogProps) {
  const [open, setOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  const currentImage = images[currentIndex];

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const handlePrevious = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  // Reset to initial index when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setCurrentIndex(initialIndex);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowRight") {
      handleNext(e as unknown as React.MouseEvent);
    } else if (e.key === "ArrowLeft") {
      handlePrevious(e as unknown as React.MouseEvent);
    } else if (e.key === "Escape") {
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className="max-w-4xl overflow-hidden border-none bg-black/90 p-0"
        onKeyDown={handleKeyDown}
        aria-label={`Image ${currentIndex + 1} of ${images.length}`}
      >
        <DialogTitle className="sr-only">{title}</DialogTitle>
        <div className="relative flex h-full flex-col items-center justify-center">
          {/* Image */}
          <div className="relative h-[80vh] w-full">
            <Image
              src={currentImage?.url || ""}
              alt={
                currentImage?.description || `Project image ${currentIndex + 1}`
              }
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 80vw"
              priority
            />
          </div>

          {/* Navigation buttons */}
          {images.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="-translate-y-1/2 absolute top-1/2 left-4 rounded-full bg-black/40 text-white hover:bg-black/60"
                onClick={handlePrevious}
                aria-label="Previous image"
              >
                <ChevronLeftIcon className="h-6 w-6" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="-translate-y-1/2 absolute top-1/2 right-4 rounded-full bg-black/40 text-white hover:bg-black/60"
                onClick={handleNext}
                aria-label="Next image"
              >
                <ChevronRightIcon className="h-6 w-6" />
              </Button>
            </>
          )}

          {/* Image description */}
          {currentImage?.description && (
            <div className="absolute bottom-0 w-full bg-black/70 p-4 text-white">
              <p>{currentImage.description}</p>
            </div>
          )}

          {/* Image counter */}
          {images.length > 1 && (
            <div
              className="-translate-x-1/2 absolute bottom-4 left-1/2 rounded-full bg-black/60 px-3 py-1 text-sm text-white"
              aria-live="polite"
              aria-atomic="true"
            >
              {currentIndex + 1} / {images.length}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
