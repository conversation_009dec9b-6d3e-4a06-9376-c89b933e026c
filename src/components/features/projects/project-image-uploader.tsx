"use client";

import "@uppy/core/dist/style.min.css";
import "@uppy/dashboard/dist/style.min.css";

import { Dashboard, useUppyEvent } from "@uppy/react";
import { ImageIcon, PlusIcon, TrashIcon, XIcon } from "lucide-react";
import Image from "next/image";
import { useEffect, useId, useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import type { JobImage } from "@/db/schema";
import { useSession } from "@/lib/auth-client";
import { createUppy, formatUploadedFileUrl } from "@/lib/uppy";

interface ProjectImageUploaderProps {
  initialImages?: Partial<JobImage>[];
  onChange: (images: Partial<JobImage>[]) => void;
  maxImages?: number;
}

export function ProjectImageUploader({
  initialImages = [],
  onChange,
  maxImages = 10,
}: ProjectImageUploaderProps) {
  const { data: session } = useSession();
  const user = session?.user;
  const [images, setImages] = useState<Partial<JobImage>[]>(initialImages);
  const [currentImageUrl, setCurrentImageUrl] = useState<string>("");
  const [currentDescription, setCurrentDescription] = useState<string>("");
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, boolean>>(
    {},
  );
  const id = useId();
  const descriptionId = `description-${id}`;

  const [uppy] = useState(() =>
    createUppy({
      type: "job-image",
      maxFiles: 1,
      maxFileSize: 10 * 1024 * 1024,
      allowedFileTypes: ["image/*"],
      autoProceed: false,
    }),
  );

  // Track when files are added to Uppy
  useUppyEvent(uppy, "file-added", (file) => {
    // If we're at the max images, remove the file
    if (images.length >= maxImages) {
      uppy.removeFile(file.id);
      toast.error(`Maximum of ${maxImages} images allowed`);
    }
  });

  useUppyEvent(uppy, "transloadit:result", (stepName, result) => {
    if (stepName === "compressed-image") {
      // Check if we've already processed this file
      if (uploadedFiles[result.name]) return;

      // Make sure we're using the correct URL format
      const url = formatUploadedFileUrl(result.name);
      setCurrentImageUrl(url);

      // Mark this file as processed
      setUploadedFiles((prev) => ({ ...prev, [result.name]: true }));

      // Log successful upload
      console.log("Image uploaded successfully:", url);
    }
  });

  useUppyEvent(uppy, "error", (error) => {
    console.error("Uppy error:", error);
    toast.error("Failed to upload image. Please try again.");
  });

  useUppyEvent(uppy, "complete", (result) => {
    if (result.failed?.length && result.failed?.length > 0) {
      toast.error(`${result.failed?.length} file(s) failed to upload`);
    }
  });

  useEffect(() => {
    if (user) {
      uppy.setOptions({ meta: { userId: user.id } });
    }
  }, [user, uppy.setOptions]);

  const addImage = () => {
    if (!currentImageUrl || images.length >= maxImages) return;

    const newImages = [
      ...images,
      {
        url: currentImageUrl,
        description: currentDescription,
      },
    ];

    setImages(newImages);
    onChange(newImages);
    setCurrentImageUrl("");
    setCurrentDescription("");

    // Reset Uppy to prepare for the next upload
    // Reset the uploadedFiles state to allow new uploads
    setUploadedFiles({});
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
    onChange(newImages);
  };

  return (
    <div className="space-y-6">
      <div className="mb-4 flex items-center gap-2">
        <ImageIcon className="h-5 w-5 text-orange-500" />
        <h3 className="font-medium text-lg">Project Images</h3>
        <div className="ml-auto text-muted-foreground text-sm">
          {images.length}/{maxImages} images
        </div>
      </div>
      <Separator className="mb-6" />

      {images.length > 0 && (
        <div className="mb-6 grid gap-4 sm:grid-cols-2 md:grid-cols-3">
          {images.map((image, index) => (
            <div
              key={image.url}
              className="relative overflow-hidden rounded-md border"
            >
              <div className="relative aspect-video">
                <Image
                  src={image.url || ""}
                  alt={image.description || `Project image ${index + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-3">
                <p className="text-sm">
                  {image.description || "No description"}
                </p>
              </div>
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8 rounded-full bg-red-500 hover:bg-red-600"
                onClick={() => removeImage(index)}
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {images.length < maxImages && (
        <div className="space-y-4 rounded-lg border p-4">
          <div className="rounded-lg bg-muted/30 p-4">
            <Dashboard
              uppy={uppy}
              height={250}
              width="100%"
              theme="auto"
              proudlyDisplayPoweredByUppy={false}
              showLinkToFileUploadResult={false}
              showProgressDetails
              note={`Images should be in JPG or PNG format, max 10MB. ${
                maxImages - images.length
              } remaining.`}
              metaFields={[
                {
                  id: "description",
                  name: "Description",
                  placeholder: "Describe this image",
                },
              ]}
              showRemoveButtonAfterComplete
              doneButtonHandler={() => {
                setCurrentImageUrl("");
                setCurrentDescription("");
              }}
            />
          </div>

          {currentImageUrl && (
            <div className="space-y-4">
              <div className="relative mx-auto max-w-md overflow-hidden rounded-md border">
                <div className="relative aspect-video">
                  <Image
                    src={currentImageUrl}
                    alt="Preview"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white"
                  onClick={() => {
                    setCurrentImageUrl("");
                  }}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor={descriptionId}>Image Description</Label>
                <Input
                  id={descriptionId}
                  value={currentDescription}
                  onChange={(e) => setCurrentDescription(e.target.value)}
                  placeholder="Describe this image (optional)"
                  className="focus-visible:ring-orange-500"
                />
              </div>

              <Button
                type="button"
                onClick={addImage}
                className="w-full bg-orange-600 hover:bg-orange-700"
                disabled={!currentImageUrl}
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                Add Image to Project
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
