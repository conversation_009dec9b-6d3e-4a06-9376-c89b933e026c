"use client";

import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useState } from "react";
import type { ProjectsMapProps } from "@/components/features/projects/projects-map";
import { useTRPC } from "@/components/integrations/trpc/client";
import { useOrganization } from "@/components/providers/organization-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Slider } from "@/components/ui/slider";
import type { Organization } from "@/db/schema";

export function ProfessionalProjectList() {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const [maxDistance, setMaxDistance] = useState(50);
  const { organization, isLoading: isLoadingOrganization } = useOrganization();
  const organizationId = organization?.id;

  // Fetch projects with distance information
  const { data: projectsData, isLoading } = useQuery(
    trpc.projects.listPublishedByDistance.queryOptions(
      {
        maxDistance,
      },
      {
        enabled: !!organizationId,
      },
    ),
  );

  // Filter projects based on search query
  const filteredProjects = projectsData?.filter((project) =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Show loading state if organization is still loading
  if (isLoadingOrganization) {
    return (
      <div className="p-8">
        <Card>
          <CardHeader>
            <CardTitle>Loading Projects...</CardTitle>
            <CardDescription>
              Please wait while we load your organization data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton
                  // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                  key={`loading-skeleton-${i}`}
                  className="h-20"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-4 flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search projects..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">
            Distance: {maxDistance} miles
          </span>
          <Slider
            className="w-40"
            value={[maxDistance]}
            min={5}
            max={100}
            step={5}
            onValueChange={(value) => setMaxDistance(value[0] as number)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Available Projects</CardTitle>
            <CardDescription>
              {filteredProjects?.length || 0} projects within {maxDistance}{" "}
              miles
            </CardDescription>
          </CardHeader>
          <CardContent className="max-h-[600px] overflow-y-auto">
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton
                    key={`skeleton-${
                      // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                      i
                    }`}
                    className="h-20"
                  />
                ))}
              </div>
            ) : (
              <div className="divide-y">
                {filteredProjects?.map((project) => (
                  <div key={project.id} className="py-3">
                    <Link
                      href={`/projects/${project.id}`}
                      className="block rounded-md bg-muted p-2 transition-colors hover:bg-muted/50"
                    >
                      <div className="font-medium">{project.name}</div>
                      <div className="text-muted-foreground text-sm">
                        {project.property?.name || "Property"}
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="text-sm">${project.budget}</span>
                        {project.distance && (
                          <span className="text-muted-foreground text-xs">
                            {Number(project.distance).toFixed(1)} miles away
                          </span>
                        )}
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Project Locations</CardTitle>
          </CardHeader>
          <CardContent>
            <ProjectsMap
              projects={
                filteredProjects?.filter(
                  (project) =>
                    project.property?.address?.location &&
                    typeof project.property.address.location === "object",
                ) || []
              }
              organization={organization as Organization}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function ProjectsMap({ projects, organization }: ProjectsMapProps) {
  const ProjectsMap = dynamic(
    () => import("@/components/features/projects/projects-map"),
    {
      loading: () => <Skeleton className="h-[60vh]" />,
      ssr: false,
    },
  );

  return (
    <div>
      <ProjectsMap projects={projects} organization={organization} />
    </div>
  );
}
