"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import type { Schedule } from "@/db/schema";
import { type ScheduleFormData, scheduleFormSchema } from "@/lib/schema";
import { cn } from "@/lib/utils";

interface ScheduleFormProps {
  jobId: string;
  existingSchedule?: Schedule;
  onComplete?: () => void;
}

export function ScheduleForm({
  jobId,
  existingSchedule,
  onComplete,
}: ScheduleFormProps) {
  const trpc = useTRPC();

  const form = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      startDate: existingSchedule?.proposedStartDate || new Date(),
      endDate: existingSchedule?.proposedEndDate || new Date(),
      notes: existingSchedule?.notes || "",
    },
  });

  const proposeSchedule = useMutation(
    trpc.schedules.proposeSchedule.mutationOptions({
      onSuccess: () => {
        toast.success("Schedule proposed successfully!");
        if (onComplete) onComplete();
      },
      onError: (error) => {
        toast.error(`Error proposing schedule: ${error.message}`);
      },
    }),
  );

  const onSubmit = (data: ScheduleFormData) => {
    proposeSchedule.mutate({
      jobId,
      proposedStartDate: data.startDate,
      proposedEndDate: data.endDate,
      notes: data.notes,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Start Date & Time</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP p") // Format with date and time
                        ) : (
                          <span>Pick a date and time</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="p-2">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            // Preserve the time from the existing value
                            const newDate = new Date(date);
                            newDate.setHours(
                              field.value.getHours(),
                              field.value.getMinutes(),
                            );
                            field.onChange(newDate);
                          }
                        }}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                      <div className="px-4 pt-4 pb-2">
                        <FormLabel>Time</FormLabel>
                        <input
                          type="time"
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          value={format(field.value, "HH:mm")}
                          onChange={(e) => {
                            const [hours, minutes] = e.target.value
                              .split(":")
                              .map(Number);
                            const newDate = new Date(field.value);
                            newDate.setHours(hours as number, minutes);
                            field.onChange(newDate);
                          }}
                        />
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>End Date & Time</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP p") // Format with date and time
                        ) : (
                          <span>Pick a date and time</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="p-2">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={(date) => {
                          if (date) {
                            // Preserve the time from the existing value
                            const newDate = new Date(date);
                            newDate.setHours(
                              field.value.getHours(),
                              field.value.getMinutes(),
                            );
                            field.onChange(newDate);
                          }
                        }}
                        disabled={(date) => date < form.getValues("startDate")}
                        initialFocus
                      />
                      <div className="px-4 pt-4 pb-2">
                        <FormLabel>Time</FormLabel>
                        <input
                          type="time"
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          value={format(field.value, "HH:mm")}
                          onChange={(e) => {
                            const [hours, minutes] = e.target.value
                              .split(":")
                              .map(Number);
                            const newDate = new Date(field.value);
                            newDate.setHours(hours as number, minutes);
                            field.onChange(newDate);
                          }}
                        />
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Add any additional details about the schedule"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Include any special instructions or requirements
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="bg-orange-600 hover:bg-orange-700"
          disabled={proposeSchedule.isPending}
        >
          {proposeSchedule.isPending ? "Submitting..." : "Propose Schedule"}
        </Button>
      </form>
    </Form>
  );
}
