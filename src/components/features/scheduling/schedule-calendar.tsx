"use client";

import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import { format, isSameDay, parseISO } from "date-fns";
import { X } from "lucide-react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import "./calendar-styles.css";
import type { Job } from "@/db/schema";

interface ScheduleCalendarProps {
  jobs: Job[];
}

export function ScheduleCalendar({ jobs }: ScheduleCalendarProps) {
  const [selectedDay, setSelectedDay] = useState<Date | undefined>(undefined);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle theme mounting to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Filter jobs that have schedules
  const scheduledJobs = jobs.filter(
    (job) => job.schedules && job.schedules.length > 0,
  );

  // Get jobs for the selected day
  const jobsForSelectedDay = selectedDay
    ? scheduledJobs.filter((job) => {
        if (!job.schedules || job.schedules.length === 0) return false;

        return job.schedules.some((schedule) => {
          const startDate = schedule.confirmedStartDate;
          const endDate = schedule.confirmedEndDate;

          if (!startDate || !endDate) return false;

          // Check if selected day is between start and end dates (inclusive)
          return (
            (selectedDay >= startDate && selectedDay <= endDate) ||
            isSameDay(selectedDay, startDate) ||
            isSameDay(selectedDay, endDate)
          );
        });
      })
    : [];

  // Create events for the calendar
  const events = scheduledJobs
    .flatMap((job) => {
      if (!job.schedules || job.schedules.length === 0) return [];

      return job.schedules
        .filter(
          (schedule) =>
            schedule.status === "CONFIRMED" &&
            schedule.confirmedStartDate &&
            schedule.confirmedEndDate,
        )
        .map((schedule) => {
          const startDate =
            schedule.confirmedStartDate instanceof Date
              ? schedule.confirmedStartDate
              : parseISO(schedule.confirmedStartDate as unknown as string);

          const endDate =
            schedule.confirmedEndDate instanceof Date
              ? schedule.confirmedEndDate
              : parseISO(schedule.confirmedEndDate as unknown as string);

          // FullCalendar requires end date to be exclusive, so add 1 day
          const exclusiveEndDate = new Date(endDate);
          exclusiveEndDate.setDate(exclusiveEndDate.getDate() + 1);

          // Use theme-appropriate colors
          const isQuickHire = job.jobType === "QUICK_HIRE";

          return {
            id: `${job.id}-${schedule.id}`,
            title: job.name,
            start: startDate,
            end: exclusiveEndDate,
            backgroundColor: isQuickHire
              ? "var(--tradecrews-blue)"
              : "var(--color-chart-2)",
            borderColor: isQuickHire
              ? "var(--tradecrews-blue)"
              : "var(--color-chart-2)",
            textColor: "#ffffff",
            allDay: true,
            classNames: [isQuickHire ? "quick-hire-event" : "regular-event"],
            extendedProps: {
              jobType: job.jobType,
              scheduleId: schedule.id,
            },
          };
        });
    })
    .filter(Boolean);

  if (!mounted) {
    return (
      <div className="h-[400px] animate-pulse rounded-md border bg-muted" />
    );
  }

  return (
    <div className="space-y-4">
      <div
        className={cn(
          "grid gap-6",
          selectedDay ? "grid-cols-1 md:grid-cols-2" : "grid-cols-1",
        )}
      >
        <div className="col-span-1">
          <div
            className={cn(
              "overflow-hidden rounded-xl border shadow-sm",
              "calendar-container",
              theme === "dark" ? "calendar-dark" : "calendar-light",
            )}
          >
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="dayGridMonth"
              headerToolbar={{
                left: "prev,next today",
                center: "title",
                right: "dayGridMonth,timeGridWeek",
              }}
              events={events}
              eventClick={(info) => {
                const job = scheduledJobs.find(
                  (job) => job.id === info.event.id,
                );
                if (job?.schedules) {
                  const startDate = job.schedules[0]?.confirmedStartDate;
                  setSelectedDay(startDate as Date);
                }
              }}
              dateClick={(info) => {
                setSelectedDay(new Date(info.date));
              }}
              height="auto"
              buttonText={{
                today: "Today",
                month: "Month",
                week: "Week",
              }}
              eventContent={(eventInfo) => {
                const jobType = eventInfo.event.extendedProps.jobType;
                return (
                  <div
                    className={cn(
                      "overflow-hidden text-ellipsis whitespace-nowrap p-1 font-medium text-xs",
                      jobType === "QUICK_HIRE"
                        ? "quick-hire-event"
                        : "regular-event",
                    )}
                  >
                    {eventInfo.event.title}
                  </div>
                );
              }}
            />
          </div>
        </div>

        {selectedDay && (
          <div className="col-span-1">
            <div className="rounded-xl border p-4 shadow-sm">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="font-medium text-lg">
                  Jobs for {format(selectedDay, "MMMM d, yyyy")}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSelectedDay(undefined)}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {jobsForSelectedDay.length > 0 ? (
                <div className="space-y-4">
                  {jobsForSelectedDay.map((job) => (
                    <div
                      key={job.id}
                      className="rounded-xl border p-3 shadow-sm"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{job.name}</p>
                          {job.jobType === "QUICK_HIRE" && (
                            <Badge variant="secondary" className="mt-1">
                              Quick Hire
                            </Badge>
                          )}
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/jobs/${job.id}`}>View</Link>
                        </Button>
                      </div>

                      {job.schedules && (
                        <div className="mt-2 text-muted-foreground text-sm">
                          <p>
                            {format(
                              job.schedules[0]?.confirmedStartDate instanceof
                                Date
                                ? job.schedules[0].confirmedStartDate
                                : parseISO(
                                    job.schedules[0]
                                      ?.confirmedStartDate as unknown as string,
                                  ),
                              "MMM d",
                            )}{" "}
                            -{" "}
                            {format(
                              job.schedules[0]?.confirmedEndDate instanceof Date
                                ? job.schedules[0].confirmedEndDate
                                : parseISO(
                                    job.schedules[0]
                                      ?.confirmedEndDate as unknown as string,
                                  ),
                              "MMM d, yyyy",
                            )}
                          </p>
                          <p className="mt-1">
                            Status: {job.schedules[0]?.status}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-xl bg-muted/50 p-4 text-center text-muted-foreground">
                  <p>No jobs scheduled for this day.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
