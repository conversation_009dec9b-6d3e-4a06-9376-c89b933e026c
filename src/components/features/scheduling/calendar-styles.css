/* Base calendar styling */
.calendar-container .fc {
  --fc-border-color: var(--border);
  --fc-page-bg-color: var(--background);
  --fc-neutral-bg-color: var(--muted);
  --fc-neutral-text-color: var(--muted-foreground);
  --fc-today-bg-color: var(--accent);

  font-family: var(--font-sans);
  font-size: 0.875rem;
}

/* Button styling */
.calendar-container .fc .fc-button {
  border-radius: var(--radius);
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  transition: all 0.15s ease;
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
}

.calendar-container .fc .fc-button:hover {
  background-color: var(--primary);
  opacity: 0.9;
}

.calendar-container .fc .fc-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-container .fc .fc-button-primary:not(:disabled).fc-button-active,
.calendar-container .fc .fc-button-primary:not(:disabled):active {
  background-color: var(--primary);
  opacity: 0.8;
  box-shadow: none;
}

/* Header styling */
.calendar-container .fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
}

/* Cell styling */
.calendar-container .fc .fc-daygrid-day {
  transition: background-color 0.15s ease;
}

.calendar-container .fc .fc-daygrid-day:hover {
  background-color: var(--accent);
}

.calendar-container .fc .fc-daygrid-day-number {
  padding: 0.5rem;
  color: var(--foreground);
}

/* Event styling */
.calendar-container .fc .fc-event {
  border-radius: var(--radius-sm);
  border-width: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 0.125rem;
}

.calendar-container .fc .quick-hire-event {
  background-color: var(--tradecrews-orange-500);
  border-color: var(--tradecrews-orange-500);
}

.calendar-container .fc .regular-event {
  background-color: var(--tradecrews-blue-600);
  border-color: var(--tradecrews-blue-600);
}

/* Additional event types */
.calendar-container .fc .priority-event {
  background-color: var(--tradecrews-orange-700);
  border-color: var(--tradecrews-orange-700);
}

.calendar-container .fc .info-event {
  background-color: var(--tradecrews-blue-300);
  border-color: var(--tradecrews-blue-300);
  color: var(--tradecrews-blue-900);
}

/* Dark mode specific adjustments */
.calendar-dark .fc .fc-day-other .fc-daygrid-day-top {
  opacity: 0.5;
}

.calendar-dark .fc .fc-col-header-cell {
  background-color: var(--card);
}

.calendar-dark .fc .fc-scrollgrid {
  border-color: var(--border);
}

/* Light mode specific adjustments */
.calendar-light .fc .fc-day-today {
  background-color: var(--accent) !important;
}
