"use client";

import { useState } from "react";
import { completeOnboarding } from "@/app/onboarding/_actions";
import { PropertyForm } from "@/components/features/properties/property-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function HomeownerOnboarding() {
  const [step, setStep] = useState<"intro" | "property">("intro");

  const handleNext = () => {
    setStep("property");
  };

  const handlePropertyCreated = async () => {
    await completeOnboarding();
  };

  if (step === "intro") {
    return (
      <div className="space-y-4">
        <p>
          As a homeowner, you can create projects and find professionals to help
          with your home improvement needs.
        </p>
        <p>Let's start by adding your first property.</p>
        <Button onClick={handleNext}>Get Started</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Your First Property</CardTitle>
          <CardDescription>
            This will be where you can manage projects and improvements.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PropertyForm onboarding onSuccess={handlePropertyCreated} />
        </CardContent>
      </Card>
    </div>
  );
}
