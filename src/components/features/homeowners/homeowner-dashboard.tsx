"use client";

import { useQuery } from "@tanstack/react-query";
import { Calendar, Filter, PlusCircle, Search } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { QueryRenderer } from "@/components/core/query-renderer";
import { ProjectsList } from "@/components/features/projects/projects-list";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function HomeownerDashboard() {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const { data: jobs, isLoading } = useQuery(
    trpc.projects.listForUser.queryOptions(),
  );

  // Filter jobs based on search query
  const filteredJobs = jobs?.filter((job) =>
    job.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Group jobs by status
  const jobsByStatus = {
    all: filteredJobs || [],
    draft: filteredJobs?.filter((job) => job.status === "DRAFT") || [],
    published: filteredJobs?.filter((job) => job.status === "PUBLISHED") || [],
    closed: filteredJobs?.filter((job) => job.status === "CLOSED") || [],
    awarded: filteredJobs?.filter((job) => job.status === "AWARDED") || [],
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle className="font-semibold text-lg sm:text-xl">
              My Projects
            </CardTitle>
            <CardDescription className="text-sm">
              Manage and track all your property projects
            </CardDescription>
          </div>
          <Button className="min-h-[44px] w-full sm:w-auto" asChild>
            {/** biome-ignore lint/nursery/useUniqueElementIds: Id is unique for tour */}
            <Link
              href="/projects/new"
              className="flex items-center justify-center gap-1"
              id="new-project-button"
            >
              <PlusCircle className="h-4 w-4" />
              <span>New Project</span>
            </Link>
          </Button>
        </div>
        <div className="mt-4 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
          <div className="relative flex-1">
            <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search jobs..."
              className="min-h-[44px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            className="min-h-[44px] w-full min-w-[44px] sm:w-auto"
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Mobile: Dropdown */}
          <div className="px-6 sm:hidden">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="min-h-[44px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <option value="all">All Jobs ({jobsByStatus.all.length})</option>
              <option value="draft">
                Drafts ({jobsByStatus.draft.length})
              </option>
              <option value="published">
                Published ({jobsByStatus.published.length})
              </option>
              <option value="awarded">
                Awarded ({jobsByStatus.awarded.length})
              </option>
            </select>
          </div>

          {/* Desktop: Tabs */}
          <div className="hidden px-6 sm:block">
            <TabsList className="h-auto w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="all"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                All Jobs
                <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-200">
                  {jobsByStatus.all.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="draft"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Drafts
                <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-200">
                  {jobsByStatus.draft.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="published"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Published
                <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-200">
                  {jobsByStatus.published.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="awarded"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Awarded
                <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
                  {jobsByStatus.awarded.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="m-0">
            <QueryRenderer
              data={jobsByStatus.all}
              isLoading={isLoading}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No jobs found.</p>
                </div>
              }
            >
              {() => (
                <ProjectsList
                  projects={jobsByStatus.all}
                  isLoading={isLoading}
                  icon={<Calendar className="h-3.5 w-3.5" />}
                  actionLabel="View Details"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>
          <TabsContent value="draft" className="m-0">
            <QueryRenderer
              data={jobsByStatus.draft}
              isLoading={isLoading}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No jobs found.</p>
                </div>
              }
            >
              {() => (
                <ProjectsList
                  projects={jobsByStatus.draft}
                  isLoading={isLoading}
                  icon={<Calendar className="h-3.5 w-3.5" />}
                  actionLabel="View Details"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>
          <TabsContent value="published" className="m-0">
            <QueryRenderer
              data={jobsByStatus.published}
              isLoading={isLoading}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No jobs found.</p>
                </div>
              }
            >
              {() => (
                <ProjectsList
                  projects={jobsByStatus.published}
                  isLoading={isLoading}
                  icon={<Calendar className="h-3.5 w-3.5" />}
                  actionLabel="View Details"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>
          <TabsContent value="awarded" className="m-0">
            <QueryRenderer
              data={jobsByStatus.awarded}
              isLoading={isLoading}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No jobs found.</p>
                </div>
              }
            >
              {() => (
                <ProjectsList
                  projects={jobsByStatus.awarded}
                  isLoading={isLoading}
                  icon={<Calendar className="h-3.5 w-3.5" />}
                  actionLabel="View Details"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t px-6 py-4">
        <div className="text-muted-foreground text-xs">
          Showing {filteredJobs?.length || 0} of {jobs?.length || 0} jobs
        </div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/projects">View All Jobs</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
