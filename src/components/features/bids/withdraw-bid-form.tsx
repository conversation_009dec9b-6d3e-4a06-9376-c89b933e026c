"use client";

import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface WithdrawBidFormProps {
  bidId: string;
}

export function WithdrawBidForm({ bidId }: WithdrawBidFormProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [confirmed, setConfirmed] = useState(false);

  const withdrawBid = useMutation(
    trpc.bids.withdraw.mutationOptions({
      onSuccess: () => {
        toast.success("Bid withdrawn successfully");
        router.push(`/bids/${bidId}`);
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error withdrawing bid: ${error.message}`);
      },
    }),
  );

  const handleWithdraw = () => {
    if (!confirmed) {
      toast.error("Please confirm that you want to withdraw this bid");
      return;
    }

    withdrawBid.mutate({ id: bidId });
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="font-semibold text-red-600 text-xl">Withdraw Bid</h2>
            <p className="mt-2 text-muted-foreground">
              Are you sure you want to withdraw this bid? This action cannot be
              undone.
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              checked={confirmed}
              onCheckedChange={(checked) => setConfirmed(!!checked)}
            />
            <Label htmlFor="confirm" className="font-medium text-sm">
              I understand that withdrawing this bid will remove it from
              consideration and cannot be undone.
            </Label>
          </div>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/bids/${bidId}`)}
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleWithdraw}
              disabled={withdrawBid.isPending}
              className="w-full"
            >
              {withdrawBid.isPending ? "Withdrawing..." : "Withdraw Bid"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
