"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { BidCreationTour } from "@/components/features/tours/bid-creation-tour";
import { useTRPC } from "@/components/integrations/trpc/client";
import { useOrganization } from "@/components/providers/organization-context";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { Bid } from "@/db/schema";
import { type BidFormData, bidSchema } from "@/lib/schema";

type BidFormProps = {
  jobId: string;
  initialData?: Bid;
  bidId?: string;
  isEditing?: boolean;
};

export function BidForm({
  jobId,
  initialData,
  bidId,
  isEditing = false,
}: BidFormProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const { organization } = useOrganization();

  const form = useForm<BidFormData>({
    resolver: zodResolver(bidSchema),
    defaultValues: {
      name: initialData?.name || "",
      amount: initialData?.amount || 0,
      description: initialData?.description || "",
      estimatedDuration: initialData?.estimatedDuration || 7,
    },
  });

  const createBid = useMutation(
    trpc.bids.create.mutationOptions({
      onSuccess: () => {
        toast.success("Bid submitted successfully");
        router.push("/dashboard");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error submitting bid: ${error.message}`);
      },
    }),
  );

  const editBid = useMutation(
    trpc.bids.edit.mutationOptions({
      onSuccess: () => {
        toast.success("Bid updated successfully");
        router.push(`/bids/${bidId}`);
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error updating bid: ${error.message}`);
      },
    }),
  );

  const onSubmit = (data: BidFormData) => {
    if (isEditing && bidId) {
      // Edit existing bid
      editBid.mutate({
        id: bidId,
        ...data,
      });
    } else {
      // Create new bid
      if (!organization?.id) {
        toast.error("You must be part of an organization to submit a bid");
        return;
      }

      createBid.mutate({
        jobId,
        organizationId: organization.id,
        name: data.name,
        amount: data.amount,
        description: data.description,
        estimatedDuration: data.estimatedDuration,
      });
    }
  };

  const isPending = isEditing ? editBid.isPending : createBid.isPending;

  return (
    <>
      {!isEditing && <BidCreationTour />}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bid Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter a name for your bid" {...field} />
                </FormControl>
                <FormDescription>
                  A clear, concise name for your proposal
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bid Amount ($)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter your bid amount"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  The total amount you're bidding for this project
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimatedDuration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estimated Duration (days)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter estimated completion time in days"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  How many days you estimate to complete the project
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bid Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your approach to this project, including materials, methods, and any special considerations"
                    className="min-h-32"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Provide details about how you'll approach the project
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                isEditing && bidId
                  ? router.push(`/bids/${bidId}`)
                  : router.back()
              }
            >
              Cancel
            </Button>
            <Button type="submit" variant="tc_orange" disabled={isPending}>
              {isPending
                ? isEditing
                  ? "Updating..."
                  : "Submitting..."
                : isEditing
                  ? "Update Bid"
                  : "Submit Bid"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
