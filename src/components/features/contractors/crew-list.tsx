"use client";

import { useQuery } from "@tanstack/react-query";
import { StarIcon, Users2Icon } from "lucide-react";
import Link from "next/link";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Badge } from "@/components/ui/badge";

interface CrewListProps {
  organizationId: string;
}

export function CrewList({ organizationId }: CrewListProps) {
  const trpc = useTRPC();

  const { data: crewMembers, isLoading } = useQuery(
    trpc.contractor.getCrewMembers.queryOptions(
      { organizationId },
      { enabled: !!organizationId },
    ),
  );

  const { data: reviews } = useQuery(
    trpc.reviews.listForOrganizations.queryOptions(
      {
        organizationIds: crewMembers?.map((c) => c.crewMember.id) || [],
      },
      { enabled: !!crewMembers && crewMembers.length > 0 },
    ),
  );

  // Calculate average ratings for each crew member
  const crewMemberRatings = reviews?.reduce(
    (acc, review) => {
      const orgId = review.job.bids.find(
        (b) => b.status === "ACCEPTED",
      )?.organizationId;
      if (!orgId) return acc;

      if (!acc[orgId]) {
        acc[orgId] = { total: 0, count: 0 };
      }

      acc[orgId].total += review.rating;
      acc[orgId].count += 1;

      return acc;
    },
    {} as Record<string, { total: number; count: number }>,
  );

  if (isLoading) {
    return <div className="py-4 text-center">Loading crew members...</div>;
  }

  if (!crewMembers || crewMembers.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        <Users2Icon className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>No crew members added yet.</p>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2">
      {crewMembers.map((crew) => {
        const rating = crewMemberRatings?.[crew.crewMember.id];
        const averageRating = rating ? rating.total / rating.count : 0;

        return (
          <Link
            href={`/contractors/${crew.crewMember.id}`}
            key={crew.id}
            className="block rounded-lg border p-4 transition-colors hover:border-orange-500/30"
          >
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-medium">{crew.crewMember.name}</h3>
                {crew.crewMember.trade && (
                  <Badge variant="outline" className="mt-1">
                    {crew.crewMember.trade.name}
                  </Badge>
                )}
              </div>

              {rating && (
                <div className="flex items-center">
                  <span className="mr-1 font-medium">
                    {averageRating.toFixed(1)}
                  </span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <StarIcon
                        key={star}
                        className={`h-4 w-4 ${
                          star <= Math.round(averageRating)
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="ml-1 text-muted-foreground text-xs">
                    ({rating.count})
                  </span>
                </div>
              )}
            </div>

            {crew.notes && (
              <p className="mt-2 text-muted-foreground text-sm">{crew.notes}</p>
            )}
          </Link>
        );
      })}
    </div>
  );
}
