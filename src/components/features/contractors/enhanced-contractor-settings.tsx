"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  AlertCircle,
  Award,
  BarChart3,
  Bell,
  Briefcase,
  Building2,
  Calendar,
  Camera,
  CheckCircle,
  Clock,
  Copy,
  CreditCard,
  Crown,
  DollarSign,
  Download,
  Edit,
  ExternalLink,
  Eye,
  EyeOff,
  FileText,
  Globe,
  Hammer,
  Image as ImageIcon,
  Info,
  Lightbulb,
  Lock,
  Mail,
  MapPin,
  MessageSquare,
  Plus,
  RefreshCw,
  Save,
  Search,
  Settings,
  Share2,
  Shield,
  Star,
  Target,
  Trash2,
  Upload,
  UserCheck,
  Users,
  UserX,
  Verified,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useTRPC } from "@/components/integrations/trpc/client";
import { useOrganization } from "@/components/providers/organization-context";
import { TextareaField, TextField } from "@/components/shared/form-fields";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import type { Collaboration } from "@/db/schema";
import { cn } from "@/lib/utils";

interface EnhancedContractorSettingsProps {
  organizationId: string;
}

export function EnhancedContractorSettings({
  organizationId,
}: EnhancedContractorSettingsProps) {
  const trpc = useTRPC();
  const router = useRouter();
  const { organization } = useOrganization();
  const [activeTab, setActiveTab] = useState("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Fetch organization data
  const {
    data: orgData,
    isLoading,
    refetch,
  } = useQuery(trpc.contractor.getById.queryOptions({ id: organizationId }));

  // Fetch additional data
  const { data: stats } = useQuery(trpc.contractor.getStats.queryOptions());

  const { data: reviews } = useQuery(
    trpc.reviews.listForOrganization.queryOptions({
      organizationId: organizationId,
    }),
  );

  const { data: crewMembers } = useQuery(
    trpc.contractor.getCrewMembers.queryOptions({ organizationId }),
  );

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <RefreshCw className="mx-auto h-8 w-8 animate-spin text-tradecrews-blue" />
          <p className="mt-2 text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (!orgData) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto h-8 w-8 text-red-500" />
          <p className="mt-2 text-muted-foreground">Organization not found</p>
        </div>
      </div>
    );
  }

  const profileCompleteness = calculateProfileCompleteness(orgData);

  return (
    <div className="space-y-6">
      {/* Header with Profile Overview */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-tradecrews-blue-50 via-white to-tradecrews-orange-50 p-6 dark:from-tradecrews-blue-950/30 dark:via-background dark:to-tradecrews-orange-950/30">
        <div className="relative z-10">
          <div className="flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between">
            <div className="flex items-start gap-4">
              <div className="relative">
                <Avatar className="h-20 w-20 border-4 border-white shadow-lg">
                  <AvatarImage src={orgData.logoUrl || undefined} />
                  <AvatarFallback className="bg-tradecrews-blue text-white text-xl">
                    {orgData.name.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {orgData.verified && (
                  <div className="-bottom-1 -right-1 absolute flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-green-500">
                    <Verified className="h-3 w-3 text-white" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="mb-2 flex items-center gap-2">
                  <h1 className="font-bold text-2xl">{orgData.name}</h1>
                  {orgData.verified && (
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300">
                      <Verified className="mr-1 h-3 w-3" />
                      Verified
                    </Badge>
                  )}
                </div>

                <div className="space-y-1 text-muted-foreground text-sm">
                  {orgData.trade && (
                    <div className="flex items-center gap-1">
                      <Hammer className="h-4 w-4" />
                      <span>{orgData.trade.name}</span>
                    </div>
                  )}
                  {orgData.address && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>
                        {orgData.address.street}, {orgData.address.city},{" "}
                        {orgData.address.state} {orgData.address.zip}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>
                      Member since {format(orgData.createdAt, "MMMM yyyy")}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-3 sm:items-end">
              <div className="flex items-center gap-2">
                <Button
                  variant={isEditing ? "default" : "outline"}
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                  className={
                    isEditing
                      ? "bg-tradecrews-orange hover:bg-tradecrews-orange-600"
                      : ""
                  }
                >
                  <Edit className="mr-2 h-4 w-4" />
                  {isEditing ? "Save Changes" : "Edit Profile"}
                </Button>

                <Button variant="outline" size="sm">
                  <Share2 className="mr-2 h-4 w-4" />
                  Share Profile
                </Button>
              </div>

              {/* Profile Completeness */}
              <div className="w-full sm:w-64">
                <div className="mb-1 flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Profile Completeness
                  </span>
                  <span className="font-medium">{profileCompleteness}%</span>
                </div>
                <Progress value={profileCompleteness} className="h-2" />
                {profileCompleteness < 100 && (
                  <p className="mt-1 text-muted-foreground text-xs">
                    Complete your profile to attract more clients
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 h-32 w-32 rounded-full bg-tradecrews-orange/10 blur-3xl" />
        <div className="absolute bottom-0 left-0 h-24 w-24 rounded-full bg-tradecrews-blue/10 blur-2xl" />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
        <Card className="border-l-4 border-l-tradecrews-blue">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-tradecrews-blue-100 p-2 dark:bg-tradecrews-blue-900/50">
                <Briefcase className="h-4 w-4 text-tradecrews-blue" />
              </div>
              <div>
                <p className="text-muted-foreground text-xs">Active Jobs</p>
                <p className="font-bold text-lg">{stats?.activeJobs || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-tradecrews-orange">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-tradecrews-orange-100 p-2 dark:bg-tradecrews-orange-900/50">
                <Star className="h-4 w-4 text-tradecrews-orange" />
              </div>
              <div>
                <p className="text-muted-foreground text-xs">Rating</p>
                <p className="font-bold text-lg">
                  {/* orgData.rating?.toFixed(1) || "N/A" */}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-green-100 p-2 dark:bg-green-900/50">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-muted-foreground text-xs">Completed</p>
                <p className="font-bold text-lg">{stats?.completedJobs || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-purple-100 p-2 dark:bg-purple-900/50">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-muted-foreground text-xs">Crew Size</p>
                <p className="font-bold text-lg">{crewMembers?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="crew" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Crew</span>
          </TabsTrigger>
          <TabsTrigger value="portfolio" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            <span className="hidden sm:inline">Portfolio</span>
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="flex items-center gap-2"
          >
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            <span className="hidden sm:inline">Billing</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <ProfileSettings orgData={orgData} isEditing={isEditing} />
        </TabsContent>

        {/* Crew Tab */}
        <TabsContent value="crew" className="space-y-6">
          <CrewManagement
            organizationId={organizationId}
            crewMembers={crewMembers}
          />
        </TabsContent>

        {/* Portfolio Tab */}
        <TabsContent value="portfolio" className="space-y-6">
          <PortfolioManagement organizationId={organizationId} />
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <NotificationSettings organizationId={organizationId} />
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="space-y-6">
          <BillingSettings organizationId={organizationId} />
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <SecuritySettings organizationId={organizationId} />
        </TabsContent>
      </Tabs>

      {/* Danger Zone */}
      <Card className="border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Danger Zone
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Delete Organization</h3>
              <p className="text-muted-foreground text-sm">
                Permanently delete your contractor profile and all associated
                data.
              </p>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    your contractor profile and remove all your data from our
                    servers.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                    Delete Organization
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to calculate profile completeness
function calculateProfileCompleteness(orgData: any): number {
  const fields = [
    orgData.name,
    orgData.description,
    orgData.location,
    orgData.phone,
    orgData.email,
    orgData.website,
    orgData.logoUrl,
    orgData.trade,
    orgData.licenseNumber,
    orgData.insuranceInfo,
  ];

  const completedFields = fields.filter(
    (field) => field && field.trim !== "",
  ).length;
  return Math.round((completedFields / fields.length) * 100);
}

// Sub-components for each tab
function ProfileSettings({
  orgData,
  isEditing,
}: {
  orgData: any;
  isEditing: boolean;
}) {
  const trpc = useTRPC();

  // Create a schema for the profile settings form
  const profileSchema = z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    description: z.string().optional(),
    email: z
      .string()
      .email("Invalid email address")
      .optional()
      .or(z.literal("")),
    phone: z.string().optional(),
    website: z.string().url("Invalid URL").optional().or(z.literal("")),
    licenseNumber: z.string().optional(),
    insuranceInfo: z.string().optional(),
  });

  type ProfileFormData = z.infer<typeof profileSchema>;

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: orgData.name || "",
      description: orgData.description || "",
      email: orgData.email || "",
      phone: orgData.phone || "",
      website: orgData.website || "",
      licenseNumber: orgData.licenseNumber || "",
      insuranceInfo: orgData.insuranceInfo || "",
    },
  });

  const updateMutation = useMutation(
    trpc.contractor.update.mutationOptions({
      onSuccess: () => {
        toast.success("Profile updated successfully");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to update profile");
      },
    }),
  );

  const handleSave = (data: ProfileFormData) => {
    updateMutation.mutate({
      id: orgData.id,
      name: data.name,
      description: data.description,
      email: data.email,
      phone: data.phone,
      address: orgData.address || {
        street: "",
        city: "",
        state: "",
        zip: "",
      },
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSave)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <TextField
                form={form}
                name="name"
                label="Organization Name"
                placeholder="Enter organization name"
                disabled={!isEditing}
                required
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
              <div>
                <Label htmlFor="trade">Trade</Label>
                <Input
                  value={orgData.trade?.name || ""}
                  disabled
                  className="bg-muted"
                  placeholder="Trade category"
                />
                <p className="mt-1 text-muted-foreground text-xs">
                  Contact support to change your trade category
                </p>
              </div>
            </div>

            <TextareaField
              form={form}
              name="description"
              label="Description"
              placeholder="Describe your services and expertise..."
              disabled={!isEditing}
              rows={4}
              className={!isEditing ? "[&_textarea]:bg-muted" : ""}
            />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <TextField
                form={form}
                name="email"
                label="Email"
                type="email"
                placeholder="<EMAIL>"
                disabled={!isEditing}
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
              <TextField
                form={form}
                name="phone"
                label="Phone"
                placeholder="(*************"
                disabled={!isEditing}
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <TextField
                form={form}
                name="website"
                label="Website"
                type="url"
                placeholder="https://www.company.com"
                disabled={!isEditing}
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  value={
                    orgData.address?.city
                      ? `${orgData.address.city}, ${orgData.address.state}`
                      : ""
                  }
                  disabled
                  className="bg-muted"
                  placeholder="City, State"
                />
                <p className="mt-1 text-muted-foreground text-xs">
                  Update your full address in the Address section below
                </p>
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="submit"
                  disabled={updateMutation.isPending}
                  className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
                >
                  {updateMutation.isPending ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Save Changes
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Professional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <TextField
                form={form}
                name="licenseNumber"
                label="License Number"
                placeholder="Enter license number"
                disabled={!isEditing}
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
              <TextField
                form={form}
                name="insuranceInfo"
                label="Insurance Information"
                placeholder="Insurance provider and policy info"
                disabled={!isEditing}
                className={!isEditing ? "[&_input]:bg-muted" : ""}
              />
            </div>

            <div className="flex items-center gap-2 rounded-lg bg-blue-50 p-3 dark:bg-blue-950/20">
              <Info className="h-4 w-4 text-blue-600" />
              <p className="text-blue-700 text-sm dark:text-blue-300">
                Providing license and insurance information helps build trust
                with potential clients.
              </p>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}

function CrewManagement({
  organizationId,
  crewMembers,
}: {
  organizationId: string;
  crewMembers: Omit<Collaboration, "primaryOrg">[] | undefined;
}) {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);

  const addCrewMemberMutation = useMutation(
    trpc.contractor.addCrewMember.mutationOptions({
      onSuccess: () => {
        toast.success("Crew member added successfully");
        setSearchQuery("");
        setSearchResults([]);
        setShowAddDialog(false);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to add crew member");
      },
    }),
  );

  const removeCrewMemberMutation = useMutation(
    trpc.contractor.removeCrewMember.mutationOptions({
      onSuccess: () => {
        toast.success("Crew member removed successfully");
      },
      onError: (error) => {
        toast.error(error.message || "Failed to remove crew member");
      },
    }),
  );

  const searchMutation = useMutation(
    trpc.contractor.search.mutationOptions({
      onSuccess: (data) => {
        setSearchResults(data);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to search contractors");
      },
    }),
  );

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    searchMutation.mutate({
      query: searchQuery,
      excludeIds: [organizationId],
    });
  };

  const handleAddCrewMember = (crewMemberId: string) => {
    addCrewMemberMutation.mutate({ organizationId, crewMemberId });
  };

  const handleRemoveCrewMember = (crewMemberId: string) => {
    removeCrewMemberMutation.mutate({ organizationId, crewMemberId });
  };

  return (
    <div className="space-y-6">
      {/* Current Crew Members */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Current Crew Members ({crewMembers?.length || 0})
            </CardTitle>
            <Button
              onClick={() => setShowAddDialog(true)}
              className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Member
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {!crewMembers || crewMembers.length === 0 ? (
            <div className="py-8 text-center">
              <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
              <h3 className="mb-2 font-medium text-lg">No crew members yet</h3>
              <p className="mb-4 text-muted-foreground">
                Add other contractors to your crew to collaborate on larger
                projects.
              </p>
              <Button onClick={() => setShowAddDialog(true)} variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Crew Member
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {crewMembers.map((crew) => (
                <div
                  key={crew.id}
                  className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={crew.crewMember.logoUrl || undefined} />
                      <AvatarFallback className="bg-tradecrews-blue text-white">
                        {crew.crewMember.name.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{crew.crewMember.name}</p>
                      {crew.crewMember.trade && (
                        <Badge variant="outline" className="mt-1">
                          <Hammer className="mr-1 h-3 w-3" />
                          {crew.crewMember.trade.name}
                        </Badge>
                      )}
                      {crew.crewMember.address && (
                        <p className="mt-1 flex items-center gap-1 text-muted-foreground text-sm">
                          <MapPin className="h-3 w-3" />
                          {crew.crewMember.address.city},{" "}
                          {crew.crewMember.address.state}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        window.open(
                          `/contractors/${crew.crewMember.id}`,
                          "_blank",
                        )
                      }
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View Profile
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleRemoveCrewMember(crew.crewMember.id)}
                      disabled={removeCrewMemberMutation.isPending}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Crew Member Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Crew Member</DialogTitle>
            <DialogDescription>
              Search for contractors to add to your crew. They'll be able to
              collaborate with you on projects.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder="Search for contractors..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="flex-1"
              />
              <Button
                onClick={handleSearch}
                disabled={searchMutation.isPending || !searchQuery.trim()}
              >
                {searchMutation.isPending ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>

            {searchResults.length > 0 && (
              <div className="max-h-64 space-y-2 overflow-y-auto">
                <h4 className="font-medium text-sm">Search Results</h4>
                {searchResults.map((org) => (
                  <div
                    key={org.id}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={org.logoUrl || undefined} />
                        <AvatarFallback className="bg-tradecrews-blue text-white text-xs">
                          {org.name.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-sm">{org.name}</p>
                        {org.trade && (
                          <Badge variant="outline" className="text-xs">
                            {org.trade.name}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleAddCrewMember(org.id)}
                      disabled={addCrewMemberMutation.isPending}
                    >
                      <Plus className="mr-1 h-3 w-3" />
                      Add
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {searchQuery &&
              searchResults.length === 0 &&
              !searchMutation.isPending && (
                <div className="py-4 text-center text-muted-foreground">
                  <Search className="mx-auto mb-2 h-8 w-8" />
                  <p>No contractors found matching "{searchQuery}"</p>
                </div>
              )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Crew Benefits Info */}
      <Card className="border-blue-200 dark:border-blue-800">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/50">
              <Lightbulb className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">
                Benefits of Building a Crew
              </h3>
              <ul className="mt-2 space-y-1 text-blue-700 text-sm dark:text-blue-300">
                <li>• Take on larger, more complex projects</li>
                <li>• Share resources and expertise</li>
                <li>• Increase your bidding capacity</li>
                <li>• Build stronger professional relationships</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function PortfolioManagement({ organizationId }: { organizationId: string }) {
  const [portfolioItems, setPortfolioItems] = useState([
    {
      id: "1",
      title: "Kitchen Renovation",
      description:
        "Complete kitchen remodel with custom cabinets and granite countertops",
      images: ["/images/portfolio/kitchen1.jpg"],
      category: "Kitchen",
      completedDate: "2024-01-15",
      featured: true,
    },
    {
      id: "2",
      title: "Bathroom Upgrade",
      description: "Modern bathroom renovation with walk-in shower",
      images: ["/images/portfolio/bathroom1.jpg"],
      category: "Bathroom",
      completedDate: "2024-02-20",
      featured: false,
    },
  ]);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  const handleAddItem = () => {
    setEditingItem(null);
    setShowAddDialog(true);
  };

  const handleEditItem = (item: any) => {
    setEditingItem(item);
    setShowAddDialog(true);
  };

  const handleDeleteItem = (itemId: string) => {
    setPortfolioItems((prev) => prev.filter((item) => item.id !== itemId));
    toast.success("Portfolio item deleted successfully");
  };

  const handleToggleFeatured = (itemId: string) => {
    setPortfolioItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, featured: !item.featured } : item,
      ),
    );
    toast.success("Portfolio item updated");
  };

  return (
    <div className="space-y-6">
      {/* Portfolio Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Portfolio ({portfolioItems.length} items)
              </CardTitle>
              <p className="mt-1 text-muted-foreground text-sm">
                Showcase your best work to attract more clients
              </p>
            </div>
            <Button
              onClick={handleAddItem}
              className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Project
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Portfolio Grid */}
      {portfolioItems.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <ImageIcon className="mx-auto mb-4 h-16 w-16 text-muted-foreground" />
            <h3 className="mb-2 font-medium text-lg">No portfolio items yet</h3>
            <p className="mb-6 text-muted-foreground">
              Start building your portfolio by adding photos of your completed
              projects.
            </p>
            <Button
              onClick={handleAddItem}
              className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {portfolioItems.map((item) => (
            <Card
              key={item.id}
              className="group overflow-hidden transition-shadow hover:shadow-lg"
            >
              <div className="relative">
                <div className="flex aspect-video items-center justify-center bg-muted">
                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                {item.featured && (
                  <Badge className="absolute top-2 left-2 bg-tradecrews-orange">
                    <Star className="mr-1 h-3 w-3" />
                    Featured
                  </Badge>
                )}
                <div className="absolute top-2 right-2 opacity-0 transition-opacity group-hover:opacity-100">
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => handleEditItem(item)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeleteItem(item.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <CardContent className="p-4">
                <div className="mb-2 flex items-start justify-between">
                  <h3 className="font-medium text-sm">{item.title}</h3>
                  <Badge variant="outline" className="text-xs">
                    {item.category}
                  </Badge>
                </div>
                <p className="mb-3 line-clamp-2 text-muted-foreground text-sm">
                  {item.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-xs">
                    {format(new Date(item.completedDate), "MMM yyyy")}
                  </span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleToggleFeatured(item.id)}
                    className="h-6 px-2"
                  >
                    <Star
                      className={cn(
                        "h-3 w-3",
                        item.featured
                          ? "fill-current text-tradecrews-orange"
                          : "",
                      )}
                    />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Portfolio Item Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? "Edit Portfolio Item" : "Add Portfolio Item"}
            </DialogTitle>
            <DialogDescription>
              Add photos and details of your completed projects to showcase your
              work.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Project Title</Label>
              <Input
                placeholder="e.g., Kitchen Renovation"
                defaultValue={editingItem?.title || ""}
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select defaultValue={editingItem?.category || ""}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Kitchen">Kitchen</SelectItem>
                  <SelectItem value="Bathroom">Bathroom</SelectItem>
                  <SelectItem value="Living Room">Living Room</SelectItem>
                  <SelectItem value="Bedroom">Bedroom</SelectItem>
                  <SelectItem value="Exterior">Exterior</SelectItem>
                  <SelectItem value="Commercial">Commercial</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                placeholder="Describe the project, materials used, challenges overcome..."
                rows={3}
                defaultValue={editingItem?.description || ""}
              />
            </div>

            <div>
              <Label htmlFor="completed-date">Completion Date</Label>
              <Input
                type="date"
                defaultValue={editingItem?.completedDate || ""}
              />
            </div>

            <div>
              <Label>Project Images</Label>
              <div className="rounded-lg border-2 border-muted-foreground/25 border-dashed p-6 text-center">
                <Upload className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
                <p className="mb-2 text-muted-foreground text-sm">
                  Drag and drop images here, or click to browse
                </p>
                <Button variant="outline" size="sm">
                  <Camera className="mr-2 h-4 w-4" />
                  Choose Images
                </Button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch defaultChecked={editingItem?.featured || false} />
              <Label htmlFor="featured">Feature this project</Label>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // Here you would save the portfolio item
                  toast.success(
                    editingItem
                      ? "Portfolio item updated"
                      : "Portfolio item added",
                  );
                  setShowAddDialog(false);
                }}
                className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
              >
                <Save className="mr-2 h-4 w-4" />
                {editingItem ? "Update" : "Add"} Project
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Portfolio Tips */}
      <Card className="border-green-200 dark:border-green-800">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="rounded-full bg-green-100 p-2 dark:bg-green-900/50">
              <Lightbulb className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium text-green-900 dark:text-green-100">
                Portfolio Tips
              </h3>
              <ul className="mt-2 space-y-1 text-green-700 text-sm dark:text-green-300">
                <li>• Use high-quality, well-lit photos</li>
                <li>• Show before and after shots when possible</li>
                <li>• Include detailed descriptions of your work</li>
                <li>• Feature your best and most recent projects</li>
                <li>• Update regularly with new completed work</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function NotificationSettings({ organizationId }: { organizationId: string }) {
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    newJobs: true,
    bidUpdates: true,
    projectMessages: true,
    paymentReminders: true,
    weeklyDigest: true,
    marketingEmails: false,
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Here you would typically save to your backend
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
      toast.success("Notification preferences updated successfully");
    } catch (error) {
      toast.error("Failed to update notification preferences");
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggle = (key: keyof typeof notifications) => {
    setNotifications((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="space-y-6">
      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Channels
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">
                Email Notifications
              </Label>
              <p className="text-muted-foreground text-sm">
                Receive notifications via email
              </p>
            </div>
            <Switch
              checked={notifications.emailNotifications}
              onCheckedChange={() => handleToggle("emailNotifications")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">
                Push Notifications
              </Label>
              <p className="text-muted-foreground text-sm">
                Receive browser and mobile push notifications
              </p>
            </div>
            <Switch
              checked={notifications.pushNotifications}
              onCheckedChange={() => handleToggle("pushNotifications")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">SMS Notifications</Label>
              <p className="text-muted-foreground text-sm">
                Receive important updates via text message
              </p>
            </div>
            <Switch
              checked={notifications.smsNotifications}
              onCheckedChange={() => handleToggle("smsNotifications")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Types
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <Briefcase className="h-4 w-4" />
                New Job Opportunities
              </Label>
              <p className="text-muted-foreground text-sm">
                Get notified when new jobs match your trade and location
              </p>
            </div>
            <Switch
              checked={notifications.newJobs}
              onCheckedChange={() => handleToggle("newJobs")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <Target className="h-4 w-4" />
                Bid Updates
              </Label>
              <p className="text-muted-foreground text-sm">
                Updates on your bids (accepted, rejected, outbid)
              </p>
            </div>
            <Switch
              checked={notifications.bidUpdates}
              onCheckedChange={() => handleToggle("bidUpdates")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <MessageSquare className="h-4 w-4" />
                Project Messages
              </Label>
              <p className="text-muted-foreground text-sm">
                New messages from clients and crew members
              </p>
            </div>
            <Switch
              checked={notifications.projectMessages}
              onCheckedChange={() => handleToggle("projectMessages")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <DollarSign className="h-4 w-4" />
                Payment Reminders
              </Label>
              <p className="text-muted-foreground text-sm">
                Reminders for pending payments and invoices
              </p>
            </div>
            <Switch
              checked={notifications.paymentReminders}
              onCheckedChange={() => handleToggle("paymentReminders")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <BarChart3 className="h-4 w-4" />
                Weekly Digest
              </Label>
              <p className="text-muted-foreground text-sm">
                Weekly summary of your activity and opportunities
              </p>
            </div>
            <Switch
              checked={notifications.weeklyDigest}
              onCheckedChange={() => handleToggle("weeklyDigest")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2 font-medium text-base">
                <Mail className="h-4 w-4" />
                Marketing Emails
              </Label>
              <p className="text-muted-foreground text-sm">
                Tips, feature updates, and promotional content
              </p>
            </div>
            <Switch
              checked={notifications.marketingEmails}
              onCheckedChange={() => handleToggle("marketingEmails")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Quiet Hours
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-sm">
            Set times when you don't want to receive non-urgent notifications.
          </p>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <Label htmlFor="quiet-start">Start Time</Label>
              <Select defaultValue="22:00">
                <SelectTrigger>
                  <SelectValue placeholder="Select start time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="20:00">8:00 PM</SelectItem>
                  <SelectItem value="21:00">9:00 PM</SelectItem>
                  <SelectItem value="22:00">10:00 PM</SelectItem>
                  <SelectItem value="23:00">11:00 PM</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="quiet-end">End Time</Label>
              <Select defaultValue="07:00">
                <SelectTrigger>
                  <SelectValue placeholder="Select end time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="06:00">6:00 AM</SelectItem>
                  <SelectItem value="07:00">7:00 AM</SelectItem>
                  <SelectItem value="08:00">8:00 AM</SelectItem>
                  <SelectItem value="09:00">9:00 AM</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-2 rounded-lg bg-amber-50 p-3 dark:bg-amber-950/20">
            <Info className="h-4 w-4 text-amber-600" />
            <p className="text-amber-700 text-sm dark:text-amber-300">
              Urgent notifications (like emergency project updates) will still
              be delivered during quiet hours.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="bg-tradecrews-orange hover:bg-tradecrews-orange-600"
        >
          {isSaving ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          Save Preferences
        </Button>
      </div>
    </div>
  );
}

function BillingSettings({ organizationId }: { organizationId: string }) {
  const [billingInfo, setBillingInfo] = useState({
    plan: "Professional",
    status: "Active",
    nextBilling: "2024-04-15",
    amount: 29.99,
  });

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Current Plan
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-lg">{billingInfo.plan} Plan</h3>
              <p className="text-muted-foreground text-sm">
                ${billingInfo.amount}/month • Next billing:{" "}
                {format(new Date(billingInfo.nextBilling), "MMM dd, yyyy")}
              </p>
            </div>
            <Badge className="bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300">
              {billingInfo.status}
            </Badge>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="rounded-lg border p-4 text-center">
              <p className="font-bold text-2xl text-tradecrews-blue">50</p>
              <p className="text-muted-foreground text-sm">Bids per month</p>
            </div>
            <div className="rounded-lg border p-4 text-center">
              <p className="font-bold text-2xl text-tradecrews-orange">∞</p>
              <p className="text-muted-foreground text-sm">Projects</p>
            </div>
            <div className="rounded-lg border p-4 text-center">
              <p className="font-bold text-2xl text-green-600">24/7</p>
              <p className="text-muted-foreground text-sm">Support</p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" className="flex-1">
              <Download className="mr-2 h-4 w-4" />
              Download Invoice
            </Button>
            <Button className="flex-1 bg-tradecrews-orange hover:bg-tradecrews-orange-600">
              <Crown className="mr-2 h-4 w-4" />
              Upgrade Plan
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/50">
                <CreditCard className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium">•••• •••• •••• 4242</p>
                <p className="text-muted-foreground text-sm">Expires 12/26</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button size="sm" variant="outline">
                <Trash2 className="mr-2 h-4 w-4" />
                Remove
              </Button>
            </div>
          </div>

          <Button variant="outline" className="w-full">
            <Plus className="mr-2 h-4 w-4" />
            Add Payment Method
          </Button>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Billing History
            </CardTitle>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              {
                date: "2024-03-15",
                amount: 29.99,
                status: "Paid",
                invoice: "INV-001",
              },
              {
                date: "2024-02-15",
                amount: 29.99,
                status: "Paid",
                invoice: "INV-002",
              },
              {
                date: "2024-01-15",
                amount: 29.99,
                status: "Paid",
                invoice: "INV-003",
              },
            ].map((item) => (
              <div
                key={item.date}
                className="flex items-center justify-between rounded-lg border p-3"
              >
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-green-100 p-1 dark:bg-green-900/50">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">${item.amount}</p>
                    <p className="text-muted-foreground text-xs">
                      {format(new Date(item.date), "MMM dd, yyyy")} •{" "}
                      {item.invoice}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600">
                    {item.status}
                  </Badge>
                  <Button size="sm" variant="ghost">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Billing Alerts */}
      <Card className="border-amber-200 dark:border-amber-800">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="rounded-full bg-amber-100 p-2 dark:bg-amber-900/50">
              <Info className="h-4 w-4 text-amber-600" />
            </div>
            <div>
              <h3 className="font-medium text-amber-900 dark:text-amber-100">
                Billing Information
              </h3>
              <ul className="mt-2 space-y-1 text-amber-700 text-sm dark:text-amber-300">
                <li>
                  • Your subscription will auto-renew on{" "}
                  {format(new Date(billingInfo.nextBilling), "MMMM dd, yyyy")}
                </li>
                <li>• You can cancel or modify your plan at any time</li>
                <li>• All prices are in USD and exclude applicable taxes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function SecuritySettings({ organizationId }: { organizationId: string }) {
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [loginAlerts, setLoginAlerts] = useState(true);
  const [showApiKeys, setShowApiKeys] = useState(false);

  return (
    <div className="space-y-6">
      {/* Account Security */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Account Security
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">
                Two-Factor Authentication
              </Label>
              <p className="text-muted-foreground text-sm">
                Add an extra layer of security to your account
              </p>
            </div>
            <Switch
              checked={twoFactorEnabled}
              onCheckedChange={setTwoFactorEnabled}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">Login Alerts</Label>
              <p className="text-muted-foreground text-sm">
                Get notified when someone logs into your account
              </p>
            </div>
            <Switch checked={loginAlerts} onCheckedChange={setLoginAlerts} />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="font-medium text-base">Password</Label>
            <p className="text-muted-foreground text-sm">
              Last changed 3 months ago
            </p>
            <Button variant="outline">
              <Lock className="mr-2 h-4 w-4" />
              Change Password
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API Access */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            API Access
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-sm">
            Manage API keys for integrating with third-party applications.
          </p>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-medium text-base">API Keys</Label>
              <p className="text-muted-foreground text-sm">
                {showApiKeys ? "Hide" : "Show"} your API keys
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowApiKeys(!showApiKeys)}
            >
              {showApiKeys ? (
                <EyeOff className="mr-2 h-4 w-4" />
              ) : (
                <Eye className="mr-2 h-4 w-4" />
              )}
              {showApiKeys ? "Hide" : "Show"} Keys
            </Button>
          </div>

          {showApiKeys && (
            <div className="space-y-3 rounded-lg bg-muted p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-sm">Production API Key</p>
                  <p className="font-mono text-muted-foreground text-xs">
                    tc_prod_••••••••••••••••••••••••••••••••
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-sm">Test API Key</p>
                  <p className="font-mono text-muted-foreground text-xs">
                    tc_test_••••••••••••••••••••••••••••••••
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <Button variant="outline">
            <Plus className="mr-2 h-4 w-4" />
            Generate New API Key
          </Button>
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Active Sessions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-sm">
            Manage your active login sessions across different devices.
          </p>

          <div className="space-y-3">
            <div className="flex items-center justify-between rounded-lg border p-3">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-green-100 p-2 dark:bg-green-900/50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Current Session</p>
                  <p className="text-muted-foreground text-xs">
                    Chrome on Windows • Last active: Now
                  </p>
                </div>
              </div>
              <Badge variant="outline" className="text-green-600">
                Current
              </Badge>
            </div>

            <div className="flex items-center justify-between rounded-lg border p-3">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/50">
                  <Globe className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Mobile Session</p>
                  <p className="text-muted-foreground text-xs">
                    Safari on iPhone • Last active: 2 hours ago
                  </p>
                </div>
              </div>
              <Button size="sm" variant="outline">
                <UserX className="mr-2 h-4 w-4" />
                Revoke
              </Button>
            </div>
          </div>

          <Button variant="destructive" className="w-full">
            <UserX className="mr-2 h-4 w-4" />
            Revoke All Other Sessions
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
