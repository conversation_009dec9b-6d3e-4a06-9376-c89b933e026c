import { useState } from "react";
import { completeOnboarding } from "@/app/onboarding/_actions";
import { But<PERSON> } from "@/components/ui/button";
import { ContractorForm } from "./contractor-form";

export function ContractorOnboarding() {
  const [step, setStep] = useState<"intro" | "organization">("intro");

  const handleNext = () => {
    setStep("organization");
  };

  const handleOrganizationCreated = async () => {
    await completeOnboarding();
  };

  if (step === "intro") {
    return (
      <div className="space-y-4">
        <p>
          As a contractor, you can find projects and bid on them to grow your
          business.
        </p>
        <p>Let's start by creating your organization profile.</p>
        <Button onClick={handleNext}>Get Started</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ContractorForm onboarding onSuccess={handleOrganizationCreated} />
    </div>
  );
}
