import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs } from "@/components/ui/tabs";

export function DashboardLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Main Dashboard Card Skeleton - matches both contractor and homeowner structure */}
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </div>
            <Skeleton className="h-11 w-32" />
          </div>
          <div className="mt-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-2">
            <Skeleton className="h-11 flex-1" />
            <div className="flex gap-2">
              <Skeleton className="h-11 w-32" />
              <Skeleton className="h-11 w-11" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Tabs Skeleton */}
          <Tabs defaultValue="all" className="w-full">
            {/* Mobile: Dropdown Skeleton */}
            <div className="px-6 sm:hidden">
              <Skeleton className="h-11 w-full" />
            </div>

            {/* Desktop: Tabs Skeleton */}
            <div className="hidden px-6 sm:block">
              <div className="h-auto w-full justify-start rounded-none border-b bg-transparent p-0">
                <div className="flex gap-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
                    <div key={i} className="flex items-center gap-2 px-4 py-3">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-5 w-8 rounded-full" />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Tab Content Skeleton */}
            <div className="space-y-4 p-6">
              {/* Job Cards Skeleton */}
              <div className="grid gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
                  <div key={i} className="space-y-3 rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-5 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex gap-2">
                      <Skeleton className="h-9 w-24" />
                      <Skeleton className="h-9 w-20" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Tabs>
        </CardContent>

        {/* Footer Skeleton */}
        <div className="flex justify-between border-t px-6 py-4">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-24" />
        </div>
      </Card>
    </div>
  );
}
