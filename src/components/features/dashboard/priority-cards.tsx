"use client";

import { AlertCircleIcon } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface PriorityItem {
  id: string;
  title: string;
  description: string;
  urgency: "high" | "medium" | "low";
  action: {
    label: string;
    href: string;
  };
  icon: React.ReactNode;
  value?: string | number;
}

interface PriorityCardsProps {
  items: PriorityItem[];
  title: string;
  className?: string;
}

export function PriorityCards({ items, title, className }: PriorityCardsProps) {
  if (items.length === 0) return null;

  return (
    <Card className={cn("shadow-sm", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <AlertCircleIcon className="h-5 w-5 text-orange-500" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {items.map((item) => (
          <div
            key={item.id}
            className={cn(
              "flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50",
              item.urgency === "high" &&
                "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20",
              item.urgency === "medium" &&
                "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/20",
              item.urgency === "low" &&
                "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20",
            )}
          >
            <div className="flex min-w-0 flex-1 items-center gap-3">
              <div
                className={cn(
                  "flex-shrink-0",
                  item.urgency === "high" && "text-red-600 dark:text-red-400",
                  item.urgency === "medium" &&
                    "text-yellow-600 dark:text-yellow-400",
                  item.urgency === "low" && "text-blue-600 dark:text-blue-400",
                )}
              >
                {item.icon}
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <p className="truncate font-medium text-sm">{item.title}</p>
                  {item.value && (
                    <Badge variant="secondary" className="text-xs">
                      {item.value}
                    </Badge>
                  )}
                </div>
                <p className="truncate text-muted-foreground text-xs">
                  {item.description}
                </p>
              </div>
            </div>
            <Button
              size="sm"
              variant={item.urgency === "high" ? "default" : "outline"}
              className="flex-shrink-0 text-xs"
              asChild
            >
              <Link href={item.action.href}>{item.action.label}</Link>
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
