"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

interface MobileDashboardProps {
  quickActions: React.ReactNode;
  alerts: React.ReactNode;
  stats: React.ReactNode;
  mainContent: React.ReactNode;
  alertCount?: number;
  className?: string;
}

export function MobileDashboard({
  quickActions,
  alerts,
  stats,
  mainContent,
  alertCount = 0,
  className,
}: MobileDashboardProps) {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState("overview");

  if (!isMobile) {
    // Desktop layout - traditional dashboard
    return (
      <div className={cn("space-y-6", className)}>
        {/* Quick actions at top */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">{quickActions}</div>
          <div>{alerts}</div>
        </div>

        {/* Stats overview */}
        <div>{stats}</div>

        {/* Main content */}
        <div>{mainContent}</div>
      </div>
    );
  }

  // Mobile layout - tabbed interface
  return (
    <div className={cn("space-y-4", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="sticky top-0 z-10 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <TabsList className="grid h-12 w-full grid-cols-4">
            <TabsTrigger value="overview" className="text-xs">
              Overview
            </TabsTrigger>
            <TabsTrigger value="actions" className="text-xs">
              Actions
            </TabsTrigger>
            <TabsTrigger value="alerts" className="relative text-xs">
              Alerts
              {alertCount > 0 && (
                <Badge
                  variant="destructive"
                  className="-top-1 -right-1 absolute flex h-4 w-4 items-center justify-center p-0 text-xs"
                >
                  {alertCount > 9 ? "9+" : alertCount}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="details" className="text-xs">
              Details
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="px-4">
          <TabsContent value="overview" className="mt-4 space-y-4">
            {/* Mobile overview - most important info first */}
            <div className="space-y-4">
              {stats}
              {alertCount > 0 && (
                <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">
                          You have {alertCount} notification
                          {alertCount > 1 ? "s" : ""}
                        </p>
                        <p className="text-muted-foreground text-xs">
                          Tap Alerts tab to view
                        </p>
                      </div>
                      <Badge variant="secondary">{alertCount}</Badge>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="actions" className="mt-4">
            {quickActions}
          </TabsContent>

          <TabsContent value="alerts" className="mt-4">
            {alerts}
          </TabsContent>

          <TabsContent value="details" className="mt-4 space-y-4">
            {mainContent}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

// Mobile-specific quick stats component
export function MobileQuickStats({
  stats,
  className,
}: {
  stats: Array<{
    label: string;
    value: string | number;
    change?: string;
    trend?: "up" | "down" | "neutral";
  }>;
  className?: string;
}) {
  return (
    <div className={cn("grid grid-cols-3 gap-3", className)}>
      {stats.map((stat) => (
        <Card key={stat.label} className="p-3">
          <div className="text-center">
            <div className="font-bold text-lg">{stat.value}</div>
            <div className="truncate text-muted-foreground text-xs">
              {stat.label}
            </div>
            {stat.change && (
              <div
                className={cn(
                  "mt-1 text-xs",
                  stat.trend === "up" && "text-green-600",
                  stat.trend === "down" && "text-red-600",
                  stat.trend === "neutral" && "text-muted-foreground",
                )}
              >
                {stat.change}
              </div>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
}
