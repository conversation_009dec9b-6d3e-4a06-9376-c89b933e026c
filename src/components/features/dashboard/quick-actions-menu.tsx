"use client";

import {
  BuildingIcon,
  CalendarIcon,
  MessageSquareIcon,
  MoreHorizontalIcon,
  PlusIcon,
  SearchIcon,
  SettingsIcon,
  ZapIcon,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface QuickAction {
  id: string;
  label: string;
  href: string;
  icon: React.ReactNode;
  description?: string;
  badge?: string;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  shortcut?: string;
}

interface QuickActionsMenuProps {
  actions: QuickAction[];
  title?: string;
  primaryActions?: QuickAction[]; // Actions to show outside the menu
  className?: string;
}

export function QuickActionsMenu({
  actions,
  title = "Quick Actions",
  primaryActions = [],
  className,
}: QuickActionsMenuProps) {
  const [open, setOpen] = useState(false);

  // Separate primary actions from menu actions
  const menuActions = actions.filter(
    (action) => !primaryActions.some((primary) => primary.id === action.id),
  );

  const handleActionClick = () => {
    setOpen(false);
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Primary actions shown as buttons */}
      {primaryActions.map((action) => (
        <Button
          key={action.id}
          variant={action.variant || "default"}
          size="sm"
          className="gap-2"
          asChild
        >
          <Link href={action.href}>
            {action.icon}
            <span className="hidden sm:inline">{action.label}</span>
            {action.badge && (
              <Badge variant="secondary" className="ml-1">
                {action.badge}
              </Badge>
            )}
          </Link>
        </Button>
      ))}

      {/* Dropdown menu for additional actions */}
      {menuActions.length > 0 && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <MoreHorizontalIcon className="h-4 w-4" />
              <span className="hidden sm:inline">More</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56" sideOffset={8}>
            <DropdownMenuLabel>{title}</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {menuActions.map((action) => (
              <DropdownMenuItem key={action.id} asChild>
                <Link
                  href={action.href}
                  className="flex cursor-pointer items-center gap-3"
                  onClick={handleActionClick}
                >
                  <div className="flex-shrink-0">{action.icon}</div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{action.label}</span>
                      {action.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {action.badge}
                        </Badge>
                      )}
                    </div>
                    {action.description && (
                      <p className="mt-0.5 text-muted-foreground text-xs">
                        {action.description}
                      </p>
                    )}
                  </div>
                  {action.shortcut && (
                    <div className="flex-shrink-0">
                      <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-medium font-mono text-[10px] text-muted-foreground opacity-100">
                        {action.shortcut}
                      </kbd>
                    </div>
                  )}
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}

// Predefined action sets with primary/secondary separation
export const homeownerActionConfig = {
  primary: [
    {
      id: "new-project",
      label: "New Project",
      href: "/projects/new",
      icon: <PlusIcon className="h-4 w-4" />,
      description: "Start a new project",
      variant: "default" as const,
    },
  ],
  secondary: [
    {
      id: "browse-contractors",
      label: "Find Contractors",
      href: "/contractors",
      icon: <SearchIcon className="h-4 w-4" />,
      description: "Browse professionals",
      shortcut: "⌘K",
    },
    {
      id: "calendar",
      label: "Calendar",
      href: "/calendar",
      icon: <CalendarIcon className="h-4 w-4" />,
      description: "View schedule",
    },
    {
      id: "messages",
      label: "Messages",
      href: "/messages",
      icon: <MessageSquareIcon className="h-4 w-4" />,
      description: "Chat with contractors",
    },
    {
      id: "properties",
      label: "Properties",
      href: "/properties",
      icon: <BuildingIcon className="h-4 w-4" />,
      description: "Manage properties",
    },
    {
      id: "add-property",
      label: "Add Property",
      href: "/properties#new-property-trigger",
      icon: <PlusIcon className="h-4 w-4" />,
      description: "Add a new property",
    },
    {
      id: "settings",
      label: "Settings",
      href: "/profile",
      icon: <SettingsIcon className="h-4 w-4" />,
      description: "Account settings",
    },
  ],
};

export const contractorActionConfig = {
  primary: [
    {
      id: "browse-jobs",
      label: "Browse Jobs",
      href: "/projects",
      icon: <ZapIcon className="h-4 w-4" />,
      description: "Find new projects",
      variant: "default" as const,
    },
  ],
  secondary: [
    {
      id: "calendar",
      label: "Calendar",
      href: "/calendar",
      icon: <CalendarIcon className="h-4 w-4" />,
      description: "View schedule",
    },
    {
      id: "messages",
      label: "Messages",
      href: "/messages",
      icon: <MessageSquareIcon className="h-4 w-4" />,
      description: "Chat with clients",
    },
    {
      id: "organization",
      label: "My Company",
      href: "/contractors/settings",
      icon: <BuildingIcon className="h-4 w-4" />,
      description: "Company profile",
    },
    {
      id: "settings",
      label: "Settings",
      href: "/profile",
      icon: <SettingsIcon className="h-4 w-4" />,
      description: "Account settings",
    },
  ],
};
