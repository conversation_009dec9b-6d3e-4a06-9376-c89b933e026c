"use client";

import { useState } from "react";
import { PropertyWizard } from "./property-wizard";

interface PropertyWizardTriggerProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function PropertyWizardTrigger({
  children,
  onSuccess,
}: PropertyWizardTriggerProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    onSuccess?.();
  };

  return (
    <>
      <div onClick={() => setOpen(true)}>{children}</div>

      <PropertyWizard
        open={open}
        onOpenChange={setOpen}
        onSuccess={handleSuccess}
      />
    </>
  );
}
