"use client";

import { PlusIcon } from "lucide-react";
import { useState } from "react";
import { PropertyWizard } from "@/components/features/properties/property-wizard";
import { Card, CardContent } from "@/components/ui/card";

export function NewProperty() {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };

  return (
    <>
      <Card
        className="h-full max-w-80 cursor-pointer border-2 border-tradecrews-orange/30 border-dashed bg-tradecrews-orange/5 transition-colors hover:border-tradecrews-orange/50 hover:bg-tradecrews-orange/10"
        onClick={() => setOpen(true)}
      >
        <CardContent className="flex h-full flex-col items-center justify-center p-6">
          <div className="rounded-full bg-tradecrews-orange/20 p-3">
            <PlusIcon className="h-6 w-6 text-tradecrews-orange" />
          </div>
          <h3 className="mt-4 font-medium">Add Property</h3>
          <p className="mt-1 text-center text-muted-foreground text-sm">
            Create a new property to manage your projects
          </p>
        </CardContent>
      </Card>

      <PropertyWizard
        open={open}
        onOpenChange={setOpen}
        onSuccess={handleSuccess}
      />
    </>
  );
}
