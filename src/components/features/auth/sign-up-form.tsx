"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FaMicrosoft } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";
import { toast } from "sonner";
import { setSocialSignUpType } from "@/app/auth/_actions";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { usePasswordStrength } from "@/hooks/use-password-strength";
import { signIn, signUp } from "@/lib/auth-client";
import { type SignUpFormData, signUpSchema } from "@/lib/schema";

type SignUpFormProps = {
  accountType: "homeowner" | "contractor";
  redirectUrl: string;
};

export function SignUpForm({ accountType, redirectUrl }: SignUpFormProps) {
  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState<string | null>(null);
  const password = form.watch("password");
  const passwordStrength = usePasswordStrength(password);

  async function onSubmit(values: SignUpFormData) {
    setIsLoading(true);
    try {
      await signUp.email(
        {
          email: values.email,
          password: values.password,
          name: values.name,
          role: accountType,
        },
        {
          onSuccess: () => {
            router.push(redirectUrl);
            toast.success(
              "Account created! Please check your email to verify your account.",
            );
          },
          onError: (ctx) => {
            toast.error(ctx.error.message);
          },
        },
      );
    } catch (error) {
      toast.error("An error occurred during sign up");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleSocialSignUp = async (provider: "google" | "microsoft") => {
    setSocialLoading(provider);
    try {
      // First set the account type in a cookie
      await setSocialSignUpType(accountType);

      // Then initiate the social sign-in
      await signIn.social(
        {
          provider,
        },
        {
          onSuccess: () => {
            router.push(redirectUrl);
            toast.success("Account created successfully");
          },
          onError: (ctx) => {
            toast.error(ctx.error.message);
          },
        },
      );
    } catch (error) {
      toast.error(`An error occurred during ${provider} sign up`);
      console.error(error);
    } finally {
      setSocialLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => handleSocialSignUp("google")}
          disabled={socialLoading !== null}
          className="w-full"
        >
          <FcGoogle className="mr-2 h-4 w-4" />
          {socialLoading === "google"
            ? "Signing up..."
            : "Continue with Google"}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => handleSocialSignUp("microsoft")}
          disabled={socialLoading !== null}
          className="w-full"
        >
          <FaMicrosoft className="mr-2 h-4 w-4 text-microsoft-blue" />
          {socialLoading === "microsoft"
            ? "Signing up..."
            : "Continue with Microsoft"}
        </Button>
      </div>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="John Doe" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    {...field}
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type="password" {...field} placeholder="********" />
                </FormControl>
                {password.length > 0 && passwordStrength && (
                  <div className="mt-2 space-y-1">
                    <Progress
                      value={passwordStrength.score * 25}
                      className={`h-1 w-full ${
                        passwordStrength.score < 2
                          ? "bg-primary/20 [&>[data-slot=progress-indicator]]:bg-red-500"
                          : passwordStrength.score < 3
                            ? "bg-primary/20 [&>[data-slot=progress-indicator]]:bg-yellow-500"
                            : "bg-primary/20 [&>[data-slot=progress-indicator]]:bg-green-500"
                      }`}
                    />
                    <p className="text-muted-foreground text-xs">
                      {passwordStrength.score < 2
                        ? "Weak"
                        : passwordStrength.score < 3
                          ? "Fair"
                          : passwordStrength.score < 4
                            ? "Good"
                            : "Strong"}
                      {passwordStrength.feedback.warning &&
                        ` - ${passwordStrength.feedback.warning}`}
                    </p>
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <Input type="password" {...field} placeholder="********" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || socialLoading !== null}
          >
            {isLoading ? "Creating account..." : "Create account with Email"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
