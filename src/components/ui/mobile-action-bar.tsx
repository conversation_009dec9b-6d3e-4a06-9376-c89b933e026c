"use client";

import type { ReactNode } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

export interface MobileActionBarProps {
  children: ReactNode;
  className?: string;
  variant?: "default" | "elevated";
}

export function MobileActionBar({
  children,
  className,
  variant = "default",
}: MobileActionBarProps) {
  const isMobile = useIsMobile();

  // Only show on mobile devices to avoid desktop duplication
  if (!isMobile) {
    return null;
  }

  return (
    <>
      {/* Spacer to prevent content from being hidden behind the actionbar */}
      <div className="h-20" />

      {/* Fixed actionbar */}
      <div
        className={cn(
          "fixed right-0 bottom-0 left-0 z-50 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
          variant === "elevated" && "shadow-lg",
          className,
        )}
      >
        <div className="safe-area-pb flex h-16 items-center justify-center px-4">
          <div className="flex w-full max-w-md items-center justify-between gap-2">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}

export interface MobileActionBarItemProps {
  children: ReactNode;
  className?: string;
  flex?: boolean;
}

export function MobileActionBarItem({
  children,
  className,
  flex = false,
}: MobileActionBarItemProps) {
  return (
    <div
      className={cn(
        "flex items-center justify-center",
        flex && "flex-1",
        className,
      )}
    >
      {children}
    </div>
  );
}
