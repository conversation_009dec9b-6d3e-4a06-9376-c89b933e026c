"use client";

import type { ReactNode } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

export interface MobileFormProps {
  title?: string;
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
  variant?: "default" | "card" | "fullscreen";
  onSubmit?: (e: React.FormEvent) => void;
}

export function MobileForm({
  title,
  children,
  actions,
  className,
  variant = "default",
  onSubmit,
}: MobileFormProps) {
  const isMobile = useIsMobile();

  if (variant === "fullscreen" && isMobile) {
    return (
      <div className="fixed inset-0 z-50 bg-background">
        <form onSubmit={onSubmit} className="flex h-full flex-col">
          {title && (
            <div className="border-b px-4 py-3">
              <h2 className="font-semibold text-lg">{title}</h2>
            </div>
          )}

          <div className="flex-1 overflow-y-auto p-4">{children}</div>

          {actions && <div className="border-t p-4">{actions}</div>}
        </form>
      </div>
    );
  }

  if (variant === "card") {
    return (
      <Card className={cn("w-full", className)}>
        {title && (
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="text-lg sm:text-xl">{title}</CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={onSubmit} className="space-y-4 sm:space-y-6">
            {children}
          </form>
        </CardContent>
        {actions && <CardFooter className="p-4 sm:p-6">{actions}</CardFooter>}
      </Card>
    );
  }

  return (
    <form
      onSubmit={onSubmit}
      className={cn("space-y-4 sm:space-y-6", className)}
    >
      {title && <h2 className="font-semibold text-lg sm:text-xl">{title}</h2>}
      {children}
      {actions && <div className="pt-4">{actions}</div>}
    </form>
  );
}

// Mobile-optimized form field wrapper
export interface MobileFormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  children: ReactNode;
  className?: string;
}

export function MobileFormField({
  label,
  description,
  error,
  required,
  children,
  className,
}: MobileFormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="ml-1 text-destructive">*</span>}
        </label>
      )}
      {description && (
        <p className="text-muted-foreground text-sm">{description}</p>
      )}
      <div className="space-y-2">
        {children}
        {error && <p className="text-destructive text-sm">{error}</p>}
      </div>
    </div>
  );
}

// Mobile-optimized form actions
export interface MobileFormActionsProps {
  primaryAction?: {
    label: string;
    onClick?: () => void;
    type?: "button" | "submit";
    disabled?: boolean;
    loading?: boolean;
  };
  secondaryAction?: {
    label: string;
    onClick?: () => void;
    type?: "button" | "submit";
    disabled?: boolean;
  };
  children?: ReactNode;
  className?: string;
  variant?: "stacked" | "inline";
}

export function MobileFormActions({
  primaryAction,
  secondaryAction,
  children,
  className,
  variant = "stacked",
}: MobileFormActionsProps) {
  const isMobile = useIsMobile();

  // Force stacked layout on mobile for better touch targets
  const isStacked = variant === "stacked" || isMobile;

  return (
    <div
      className={cn(
        "flex gap-3",
        isStacked ? "flex-col" : "flex-row justify-end",
        className,
      )}
    >
      {children}

      {secondaryAction && (
        <Button
          type={secondaryAction.type || "button"}
          variant="outline"
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled}
          className={cn(
            isMobile && "min-h-[44px]", // Better touch target
            isStacked && "order-2",
          )}
        >
          {secondaryAction.label}
        </Button>
      )}

      {primaryAction && (
        <Button
          type={primaryAction.type || "submit"}
          onClick={primaryAction.onClick}
          disabled={primaryAction.disabled || primaryAction.loading}
          className={cn(
            isMobile && "min-h-[44px]", // Better touch target
            isStacked && "order-1",
          )}
        >
          {primaryAction.loading ? "Loading..." : primaryAction.label}
        </Button>
      )}
    </div>
  );
}

// Mobile-optimized input group for related fields
export interface MobileInputGroupProps {
  title?: string;
  description?: string;
  children: ReactNode;
  className?: string;
  variant?: "default" | "card";
}

export function MobileInputGroup({
  title,
  description,
  children,
  className,
  variant = "default",
}: MobileInputGroupProps) {
  if (variant === "card") {
    return (
      <Card className={className}>
        {(title || description) && (
          <CardHeader className="p-4 sm:p-6">
            {title && (
              <CardTitle className="text-base sm:text-lg">{title}</CardTitle>
            )}
            {description && (
              <p className="text-muted-foreground text-sm">{description}</p>
            )}
          </CardHeader>
        )}
        <CardContent className="space-y-4 p-4 sm:space-y-6 sm:p-6">
          {children}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="font-medium text-base sm:text-lg">{title}</h3>
          )}
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4 sm:space-y-6">{children}</div>
    </div>
  );
}

// Mobile-optimized field grid
export interface MobileFieldGridProps {
  children: ReactNode;
  columns?: 1 | 2;
  className?: string;
}

export function MobileFieldGrid({
  children,
  columns = 2,
  className,
}: MobileFieldGridProps) {
  return (
    <div
      className={cn(
        "grid gap-4 sm:gap-6",
        columns === 1 ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-2",
        className,
      )}
    >
      {children}
    </div>
  );
}
