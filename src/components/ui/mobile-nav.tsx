"use client";

import { Menu } from "lucide-react";
import Link from "next/link";
import { type ReactNode, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

export interface MobileNavItem {
  title: string;
  href: string;
  icon?: ReactNode;
  badge?: string | number;
  disabled?: boolean;
  external?: boolean;
}

export interface MobileNavSection {
  title?: string;
  items: MobileNavItem[];
}

export interface MobileNavProps {
  sections: MobileNavSection[];
  trigger?: ReactNode;
  title?: string;
  description?: string;
  className?: string;
  onItemClick?: (item: MobileNavItem) => void;
}

export function MobileNav({
  sections,
  trigger,
  title = "Navigation",
  description,
  className,
  onItemClick,
}: MobileNavProps) {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();

  // Only render on mobile
  if (!isMobile) {
    return null;
  }

  const handleItemClick = (item: MobileNavItem) => {
    setOpen(false);
    onItemClick?.(item);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        )}
      </SheetTrigger>
      <SheetContent side="left" className={cn("w-80", className)}>
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          {description && <SheetDescription>{description}</SheetDescription>}
        </SheetHeader>

        <nav className="mt-6 space-y-6">
          {sections.map((section, sectionIndex) => (
            <div key={`section-${sectionIndex}`}>
              {section.title && (
                <h3 className="mb-3 font-medium text-muted-foreground text-sm uppercase tracking-wider">
                  {section.title}
                </h3>
              )}
              <div className="space-y-1">
                {section.items.map((item, itemIndex) => (
                  <MobileNavLink
                    key={`item-${sectionIndex}-${itemIndex}`}
                    item={item}
                    onClick={() => handleItemClick(item)}
                  />
                ))}
              </div>
            </div>
          ))}
        </nav>
      </SheetContent>
    </Sheet>
  );
}

function MobileNavLink({
  item,
  onClick,
}: {
  item: MobileNavItem;
  onClick: () => void;
}) {
  const content = (
    <div
      className={cn(
        "flex items-center justify-between rounded-lg px-3 py-3 font-medium text-sm transition-colors",
        "hover:bg-accent hover:text-accent-foreground",
        "focus:bg-accent focus:text-accent-foreground focus:outline-none",
        item.disabled && "pointer-events-none opacity-50",
      )}
    >
      <div className="flex items-center gap-3">
        {item.icon && (
          <span className="flex-shrink-0 text-muted-foreground">
            {item.icon}
          </span>
        )}
        <span>{item.title}</span>
      </div>
      {item.badge && (
        <span className="rounded-full bg-primary px-2 py-1 text-primary-foreground text-xs">
          {item.badge}
        </span>
      )}
    </div>
  );

  if (item.external) {
    return (
      <a
        href={item.href}
        target="_blank"
        rel="noopener noreferrer"
        onClick={onClick}
      >
        {content}
      </a>
    );
  }

  return (
    <Link href={item.href} onClick={onClick}>
      {content}
    </Link>
  );
}

// Bottom navigation for mobile
export interface BottomNavItem {
  title: string;
  href: string;
  icon: ReactNode;
  badge?: string | number;
  active?: boolean;
}

export interface BottomNavProps {
  items: BottomNavItem[];
  className?: string;
}

export function BottomNav({ items, className }: BottomNavProps) {
  const isMobile = useIsMobile();

  // Only show on mobile
  if (!isMobile) {
    return null;
  }

  return (
    <nav
      className={cn(
        "fixed right-0 bottom-0 left-0 z-50 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className,
      )}
    >
      <div className="flex h-16 items-center justify-around px-4">
        {items.map((item, index) => (
          <Link
            key={`bottom-nav-${index}`}
            href={item.href}
            className={cn(
              "flex flex-col items-center justify-center gap-1 rounded-lg px-3 py-2 font-medium text-xs transition-colors",
              "hover:bg-accent hover:text-accent-foreground",
              "focus:bg-accent focus:text-accent-foreground focus:outline-none",
              item.active ? "text-primary" : "text-muted-foreground",
            )}
          >
            <div className="relative">
              <span className="flex h-6 w-6 items-center justify-center">
                {item.icon}
              </span>
              {item.badge && (
                <span className="-right-1 -top-1 absolute flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-[10px] text-destructive-foreground">
                  {item.badge}
                </span>
              )}
            </div>
            <span className="truncate">{item.title}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
}

// Mobile-specific spacing component to account for bottom nav
export function MobileBottomSpacer({ className }: { className?: string }) {
  const isMobile = useIsMobile();

  if (!isMobile) {
    return null;
  }

  return <div className={cn("h-16", className)} />;
}
