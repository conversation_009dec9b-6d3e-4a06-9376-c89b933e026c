import type { ReactNode } from "react";
import { cn } from "@/lib/utils";

export interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  // Mobile-first responsive columns
  cols?: {
    default?: number; // Mobile (< 640px)
    sm?: number; // Small (>= 640px)
    md?: number; // Medium (>= 768px)
    lg?: number; // Large (>= 1024px)
    xl?: number; // Extra large (>= 1280px)
    "2xl"?: number; // 2X large (>= 1536px)
  };
  // Gap between items
  gap?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    "2xl"?: number;
  };
  // Auto-fit columns based on minimum width
  autoFit?: {
    minWidth: string; // e.g., "280px", "20rem"
    maxCols?: number; // Maximum columns to prevent too many on large screens
  };
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, lg: 3, xl: 4 },
  gap = { default: 4, sm: 6 },
  autoFit,
}: ResponsiveGridProps) {
  // Build grid column classes
  const getColsClass = () => {
    const classes: string[] = [];

    if (autoFit) {
      // Use CSS Grid auto-fit for responsive columns
      const maxCols = autoFit.maxCols || 6;
      return `grid-cols-[repeat(auto-fit,minmax(${autoFit.minWidth},1fr))] max-grid-cols-${maxCols}`;
    }

    // Use responsive breakpoint classes
    if (cols.default) classes.push(`grid-cols-${cols.default}`);
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
    if (cols["2xl"]) classes.push(`2xl:grid-cols-${cols["2xl"]}`);

    return classes.join(" ");
  };

  // Build gap classes
  const getGapClass = () => {
    const classes: string[] = [];

    if (gap.default) classes.push(`gap-${gap.default}`);
    if (gap.sm) classes.push(`sm:gap-${gap.sm}`);
    if (gap.md) classes.push(`md:gap-${gap.md}`);
    if (gap.lg) classes.push(`lg:gap-${gap.lg}`);
    if (gap.xl) classes.push(`xl:gap-${gap.xl}`);
    if (gap["2xl"]) classes.push(`2xl:gap-${gap["2xl"]}`);

    return classes.join(" ");
  };

  return (
    <div
      className={cn("grid", getColsClass(), getGapClass(), className)}
      style={
        autoFit
          ? {
              gridTemplateColumns: `repeat(auto-fit, minmax(${autoFit.minWidth}, 1fr))`,
            }
          : undefined
      }
    >
      {children}
    </div>
  );
}

// Preset grid configurations for common use cases
export const GridPresets = {
  // Card grids
  cards: {
    cols: { default: 1, sm: 2, lg: 3, xl: 4 },
    gap: { default: 2, sm: 3 },
  },

  // Property/job listings
  listings: {
    cols: { default: 1, md: 2, xl: 3 },
    gap: { default: 4, md: 6 },
  },

  // Dashboard stats
  stats: {
    cols: { default: 1, sm: 2, lg: 4 },
    gap: { default: 4, sm: 4, lg: 6 },
  },

  // Form fields
  formFields: {
    cols: { default: 1, md: 2 },
    gap: { default: 4, md: 6 },
  },

  // Auto-fit responsive
  autoFitCards: {
    autoFit: { minWidth: "280px", maxCols: 4 },
    gap: { default: 4, sm: 6 },
  },

  autoFitListings: {
    autoFit: { minWidth: "320px", maxCols: 3 },
    gap: { default: 4, md: 6 },
  },
} as const;

// Convenience components for common patterns
export function CardGrid({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveGrid {...GridPresets.cards} className={className}>
      {children}
    </ResponsiveGrid>
  );
}

export function ListingGrid({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveGrid {...GridPresets.listings} className={className}>
      {children}
    </ResponsiveGrid>
  );
}

export function StatsGrid({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveGrid {...GridPresets.stats} className={className}>
      {children}
    </ResponsiveGrid>
  );
}

export function AutoFitCardGrid({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveGrid {...GridPresets.autoFitCards} className={className}>
      {children}
    </ResponsiveGrid>
  );
}

export function AutoFitListingGrid({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <ResponsiveGrid {...GridPresets.autoFitListings} className={className}>
      {children}
    </ResponsiveGrid>
  );
}
