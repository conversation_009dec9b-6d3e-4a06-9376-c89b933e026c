import { cn } from "@/lib/utils";

export function ButtonGroup({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="button-group"
      className={cn(
        "inline-flex divide-x divide-primary-foreground/40 shadow-black/5 shadow-sm [&>*]:rounded-none [&>button:first-child]:rounded-l-md [&>button:last-child]:rounded-r-md",
        className
      )}
      {...props}
    />
  );
}
