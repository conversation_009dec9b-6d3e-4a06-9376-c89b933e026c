"use client";

import { useChat } from "@ai-sdk/react";
import { createIdGenerator } from "ai";
import { MessageCircle, Send } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import jackAvatar from "@/assets/images/jack.png";
import { EnhancedJackIntroduction } from "@/components/features/chat/enhanced-jack-introduction";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { cn, formatChatDate } from "@/lib/utils";

interface PopupChatProps {
  title?: string;
  supportName?: string;
  supportAvatar?: string;
  userAvatar?: string | null | undefined;
  userName?: string | null | undefined;
  userRole?: string;
  apiEndpoint?: string;
  className?: string;
}

export function PopupChat({
  title = "Ask Jack",
  supportName = "Jack",
  supportAvatar = "",
  userName = "You",
  userAvatar = "",
  userRole = "homeowner",
  apiEndpoint = "/api/chat",
  className,
}: PopupChatProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [chatId, setChatId] = useLocalStorage<string>("popup-chat-id", "");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initChat = async () => {
      if (!chatId) {
        try {
          const response = await fetch("/api/chat/create", {
            method: "POST",
          });
          if (response.ok) {
            const data = await response.json();
            setChatId(data.chatId);
          }
        } catch (error) {
          console.error("Failed to create chat:", error);
        }
      }
    };

    initChat();
  }, [chatId, setChatId]);

  supportAvatar = jackAvatar.src;

  const { messages, input, handleInputChange, handleSubmit } = useChat({
    api: apiEndpoint,
    id: chatId,
    generateId: createIdGenerator({
      prefix: "msgc",
      size: 16,
    }),
    sendExtraMessageFields: true,
    experimental_prepareRequestBody: ({ messages }) => {
      const lastMessage = messages[messages.length - 1];
      return {
        message: lastMessage,
        chatId,
      };
    },
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  });

  return (
    <>
      {/* Floating button to open chat - hidden on mobile for project pages */}
      <Button
        data-chat-trigger
        onClick={() => setIsOpen(true)}
        className="fixed right-6 bottom-6 z-50 hidden h-14 w-14 rounded-full p-0 shadow-lg lg:block"
      >
        <MessageCircle className="m-auto h-6 w-6" />
      </Button>

      {/* Chat dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen} modal={false}>
        <DialogContent
          className={cn(
            "fixed top-auto right-6 bottom-6 left-auto flex h-[500px] w-96 translate-x-0 translate-y-0 flex-col p-0 sm:w-[400px]",
            className,
          )}
        >
          <DialogHeader className="flex flex-row items-center justify-between border-b bg-muted/50 p-3">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                {supportAvatar && <AvatarImage src={supportAvatar} />}
                <AvatarFallback>{supportName.slice(0, 2)}</AvatarFallback>
              </Avatar>
              <DialogTitle>{title}</DialogTitle>
            </div>
          </DialogHeader>

          {/* Messages */}
          <ScrollArea className="flex-1 overflow-y-scroll p-3">
            <div className="space-y-4">
              {/* Show enhanced introduction when no messages yet */}
              {messages.length === 0 && (
                <EnhancedJackIntroduction
                  userName={userName || "there"}
                  userRole={userRole}
                  onQuickAction={(action) => {
                    // Convert quick actions to chat messages
                    const actionMessages = {
                      "create-project":
                        "Help me create a new home improvement project",
                      "find-contractors":
                        "How do I find qualified contractors for my project?",
                      "project-timeline":
                        "Can you help me plan a timeline for my project?",
                      "find-jobs": "Show me relevant projects I can bid on",
                      "analyze-competition":
                        "Help me analyze the competition for bidding",
                      "check-performance": "How is my contractor performance?",
                      "platform-insights":
                        "Show me platform analytics and insights",
                      "user-stats": "Display user statistics",
                      "manage-users": "Help me manage platform users",
                      "explore-features": "What features are available to me?",
                      "get-help": "I need help getting started",
                    };

                    const message =
                      actionMessages[action as keyof typeof actionMessages];
                    if (message) {
                      handleInputChange({ target: { value: message } } as any);
                    }
                  }}
                />
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.role === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted"
                    }`}
                  >
                    <div className="mb-1 flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage
                          src={
                            message.role === "user"
                              ? (userAvatar as string)
                              : supportAvatar
                          }
                        />
                        <AvatarFallback>
                          {message.role === "user"
                            ? userName?.slice(0, 2)
                            : supportName.slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium text-xs">
                        {message.role === "user" ? userName : supportName}
                      </span>
                    </div>
                    <p>
                      {message.parts.map((part, i) => {
                        switch (part.type) {
                          case "text":
                            return (
                              <div key={`${message.id}-part-${i}`}>
                                {part.text}
                              </div>
                            );
                          case "tool-invocation": {
                            const { toolInvocation } = part;

                            return toolInvocation.state === "result" ? null : (
                              <span className="animate-pulse text-muted-foreground text-xs">
                                Reticulating splines...
                              </span>
                            );
                          }
                        }
                      })}
                    </p>
                    {message.role === "user" && message.createdAt && (
                      <span className="text-muted-foreground text-xs">
                        {formatChatDate(message.createdAt)}
                      </span>
                    )}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Message input */}
          <form onSubmit={handleSubmit} className="border-t p-3">
            <div className="flex gap-2">
              <Input
                value={input}
                onChange={handleInputChange}
                placeholder="Type a message..."
                className="flex-1"
              />
              <Button type="submit" size="icon">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
