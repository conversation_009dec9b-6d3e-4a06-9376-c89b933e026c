"use client";

import { useQuery } from "@tanstack/react-query";
import {
  createContext,
  Suspense,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import type { Account } from "@/db/schema";
import { useSession } from "@/lib/auth-client";

export type AccountContextType = {
  account: Account | null | undefined;
  setAccount: (job: Account) => void;
};

const AccountContext = createContext<AccountContextType>({
  account: null,
  setAccount: () => {},
});

function AccountLoader({
  setAccount,
}: {
  setAccount: (account: Account) => void;
}) {
  const trpc = useTRPC();
  const { data: session } = useSession();

  const account = useQuery(
    trpc.accounts.findForUser.queryOptions({
      userId: session?.user?.id as string,
    }),
  );

  useEffect(() => {
    if (account.data) {
      setAccount(account.data);
    }
  }, [account.data, setAccount]);

  return null;
}

export function AccountProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [account, setAccount] = useState<Account | null | undefined>(null);

  const value = {
    account,
    setAccount,
  };

  return (
    <AccountContext.Provider value={value}>
      <Suspense fallback={null}>
        <AccountLoader setAccount={setAccount} />
      </Suspense>
      {children}
    </AccountContext.Provider>
  );
}

export function useAccount() {
  return useContext(AccountContext);
}
