"use client";

import { useQuery } from "@tanstack/react-query";
import {
  createContext,
  Suspense,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import type { Organization } from "@/db/schema";
import { useSession } from "@/lib/auth-client";

export type OrganizationContextType = {
  organization: Organization | null | undefined;
  setOrganization: (organization: Organization | null | undefined) => void;
  isLoading: boolean;
  error: Error | null;
};

const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  setOrganization: () => {},
  isLoading: false,
  error: null,
});

function OrganizationLoader({
  setOrganization,
  userRole,
}: {
  setOrganization: (org: Organization | null) => void;
  userRole: string | undefined;
}) {
  const trpc = useTRPC();

  // Only fetch organization data for contractors
  const { data: userOrganization, error } = useQuery({
    ...trpc.contractor.getForUser.queryOptions(),
    enabled: userRole === "contractor",
  });

  useEffect(() => {
    if (userRole === "contractor") {
      setOrganization(userOrganization || null);
    } else {
      // For homeowners, set organization to null
      setOrganization(null);
    }
  }, [userOrganization, setOrganization, userRole]);

  // Propagate errors to context
  useEffect(() => {
    if (error) {
      console.error("Failed to load organization:", error);
    }
  }, [error]);

  return null;
}

export function OrganizationProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { data: session, isPending } = useSession();
  const [organization, setOrganization] = useState<
    Organization | null | undefined
  >(undefined);
  const [error, setError] = useState<Error | null>(null);

  const userRole = session?.user?.role;
  const isLoading =
    isPending || (userRole === "contractor" && organization === undefined);

  const value = {
    organization,
    setOrganization: (org: Organization | null | undefined) => {
      setOrganization(org);
      setError(null);
    },
    isLoading,
    error,
  };

  return (
    <OrganizationContext.Provider value={value}>
      <Suspense fallback={null}>
        <OrganizationLoader
          setOrganization={setOrganization}
          userRole={userRole}
        />
      </Suspense>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error(
      "useOrganization must be used within an OrganizationProvider",
    );
  }
  return context;
}
