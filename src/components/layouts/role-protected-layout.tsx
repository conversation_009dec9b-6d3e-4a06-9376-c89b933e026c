"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSession } from "@/lib/auth-client";

interface RoleProtectedLayoutProps {
  children: React.ReactNode;
  allowedRoles: string[];
  fallbackPath?: string;
}

export function RoleProtectedLayout({
  children,
  allowedRoles,
  fallbackPath = "/dashboard",
}: RoleProtectedLayoutProps) {
  const { data, isPending } = useSession();
  const router = useRouter();
  const userRole = data?.user?.role;

  useEffect(() => {
    if (isPending) {
      router.push("/sign-in");
    } else if (!data?.user || !userRole || !allowedRoles.includes(userRole)) {
      router.push(fallbackPath);
    }
  }, [isPending, router, userRole, allowedRoles, fallbackPath, data]);

  if (isPending) {
    return <div>Loading...</div>;
  }

  if (!data?.user || !userRole || !allowedRoles.includes(userRole)) {
    return null;
  }

  return <>{children}</>;
}
