"use client";

import { unauthorized } from "next/navigation";
import { useEffect } from "react";
import { useSession } from "@/lib/auth-client";

interface ProtectedLayoutProps {
  children: React.ReactNode;
}

export function ProtectedLayout({ children }: ProtectedLayoutProps) {
  const { data } = useSession();

  useEffect(() => {
    if (!data?.user) {
      unauthorized();
    }
  }, [data]);

  if (!data?.user) {
    return null;
  }

  return <>{children}</>;
}
