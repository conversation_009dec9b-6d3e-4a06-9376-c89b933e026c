import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface PasswordResetProps {
  resetCode: string;
  resetLink: string;
  userId?: string;
  recipientEmail?: string;
}

const PasswordResetEmail = ({
  resetCode,
  resetLink,
  userId,
  recipientEmail,
}: PasswordResetProps) => {
  return (
    <Html>
      <Head />
      <Preview>Jack here - let's get your password reset quickly</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Password Reset Help 🔐
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - I got your password reset request
              </Text>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                No worries - we all forget passwords sometimes! I'm here to help
                you get back into your account quickly and securely. Let's get
                this sorted out.
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[12px] font-medium text-[16px] text-gray-700">
                  Your password reset code:
                </Text>
                <Text className="mb-[8px] text-center font-bold text-[24px] text-gray-800">
                  {resetCode}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Just click the button below to create a new password. For
                security, this link will expire in 1 hour - but that should be
                plenty of time!
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={resetLink}
                >
                  Reset My Password
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                If you didn't request this password reset, no problem - you can
                safely ignore this email and your account will remain secure.
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Once you're back in, I'll be here to help with your home
                improvement projects!
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                {userId && recipientEmail ? (
                  <a
                    href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                    className="text-gray-500 underline"
                  >
                    Unsubscribe
                  </a>
                ) : null}
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default PasswordResetEmail;
