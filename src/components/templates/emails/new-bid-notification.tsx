import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewBidNotificationProps {
  bidName: string;
  bidId: string;
  jobName: string;
  contractorName: string;
  amount: string;
  estimatedDuration: number;
  userId: string;
  recipientEmail: string;
}

const NewBidNotificationEmail = ({
  bidName,
  bidId,
  jobName,
  contractorName,
  amount,
  estimatedDuration,
  userId,
  recipientEmail,
}: NewBidNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        🎉 Great news! New bid received - ${amount} from {contractorName}
      </Preview>
      <Tailwind>
        <Body className="bg-slate-50 py-10 font-sans">
          <Container className="mx-auto max-w-2xl overflow-hidden rounded-lg bg-white">
            {/* Celebration Header */}
            <Section className="bg-green-500 px-8 py-6 text-center">
              <div className="mb-[16px]">
                <span className="text-[48px]">🎉</span>
              </div>
              <Heading className="m-0 font-bold text-[24px] text-white">
                Fantastic News!
              </Heading>
              <Text className="m-0 mt-[8px] text-[16px] text-green-100">
                You've received a new bid on your project
              </Text>
            </Section>

            {/* Main Content */}
            <Section className="px-[32px] py-[32px]">
              <Text className="mb-[24px] text-center font-medium text-[18px] text-gray-700">
                Hey there! 👋 Jack here with exciting news - a qualified
                professional has submitted a bid for your project!
              </Text>

              {/* Bid Details Card */}
              <Section className="mb-[32px] rounded-[12px] border-2 border-green-100 bg-gradient-to-br from-green-50 to-emerald-50 p-[24px]">
                <Text className="mb-[16px] border-green-200 border-b pb-[12px] font-bold text-[20px] text-gray-800">
                  {bidName}
                </Text>

                <div className="grid grid-cols-1 gap-[12px]">
                  <div className="flex items-center">
                    <span className="inline-block w-[20px] text-[16px]">
                      🏠
                    </span>
                    <Text className="m-0 ml-[8px] text-[16px] text-gray-700">
                      <strong className="text-blue-700">Project:</strong>{" "}
                      {jobName}
                    </Text>
                  </div>

                  <div className="flex items-center">
                    <span className="inline-block w-[20px] text-[16px]">
                      👷‍♂️
                    </span>
                    <Text className="m-0 ml-[8px] text-[16px] text-gray-700">
                      <strong className="text-purple-700">Contractor:</strong>{" "}
                      {contractorName}
                    </Text>
                  </div>

                  <div className="flex items-center">
                    <span className="inline-block w-[20px] text-[16px]">
                      💰
                    </span>
                    <Text className="m-0 ml-[8px] text-[16px] text-gray-700">
                      <strong className="text-green-700">Bid Amount:</strong>{" "}
                      <span className="font-bold text-[18px] text-green-800">
                        ${amount}
                      </span>
                    </Text>
                  </div>

                  <div className="flex items-center">
                    <span className="inline-block w-[20px] text-[16px]">
                      ⏱️
                    </span>
                    <Text className="m-0 ml-[8px] text-[16px] text-gray-700">
                      <strong className="text-orange-700">Timeline:</strong>{" "}
                      {estimatedDuration} days
                    </Text>
                  </div>
                </div>
              </Section>

              {/* Progress Message */}
              <Section className="mb-[32px] rounded-[12px] border-blue-500 border-l-4 bg-blue-50 p-[20px]">
                <Text className="m-0 text-[16px] text-gray-700 leading-relaxed">
                  <strong className="text-blue-700">
                    This is exciting progress!
                  </strong>
                  <br />A qualified contractor has carefully reviewed your
                  project details and submitted their professional proposal.
                  This shows genuine interest in your project and demonstrates
                  their commitment to quality work.
                </Text>
              </Section>

              {/* Call to Action */}
              <Section className="mb-[32px] text-center">
                <Button
                  className="inline-block rounded-[8px] bg-gradient-to-r from-blue-600 to-blue-700 px-[40px] py-[16px] text-center font-bold text-[18px] text-white no-underline shadow-lg transition-all hover:shadow-xl"
                  href={`https://tradecrews.com/bids/${bidId}`}
                >
                  📋 Review This Bid
                </Button>

                <Text className="mt-[12px] text-[14px] text-gray-500">
                  See full details, contractor profile, and proposal
                </Text>
              </Section>

              {/* Evaluation Tips */}
              <Section className="mb-[32px] rounded-[12px] border border-amber-200 bg-amber-50 p-[20px]">
                <Text className="mb-[12px] font-bold text-[16px] text-amber-800">
                  💡 Smart Bid Evaluation Tips:
                </Text>
                <ul className="m-0 space-y-[4px] pl-[20px] text-[14px] text-amber-700">
                  <li>Review the contractor's profile and past work</li>
                  <li>Check their ratings and customer reviews</li>
                  <li>Compare timeline and pricing with other bids</li>
                  <li>Look for detailed project breakdown and materials</li>
                  <li>Consider asking follow-up questions</li>
                </ul>
              </Section>

              {/* Next Steps */}
              <Section className="mb-[24px] rounded-[12px] border border-purple-200 bg-purple-50 p-[20px]">
                <Text className="mb-[12px] font-bold text-[16px] text-purple-800">
                  🎯 What happens next?
                </Text>
                <div className="space-y-[8px] text-[14px] text-purple-700">
                  <div className="flex items-start">
                    <span className="mt-[2px] inline-block w-[20px]">1️⃣</span>
                    <Text className="m-0 ml-[8px]">
                      Review this bid and any others you receive
                    </Text>
                  </div>
                  <div className="flex items-start">
                    <span className="mt-[2px] inline-block w-[20px]">2️⃣</span>
                    <Text className="m-0 ml-[8px]">
                      Ask questions or request clarifications
                    </Text>
                  </div>
                  <div className="flex items-start">
                    <span className="mt-[2px] inline-block w-[20px]">3️⃣</span>
                    <Text className="m-0 ml-[8px]">
                      Accept the bid that feels right for your project
                    </Text>
                  </div>
                  <div className="flex items-start">
                    <span className="mt-[2px] inline-block w-[20px]">4️⃣</span>
                    <Text className="m-0 ml-[8px]">
                      Schedule and start your project!
                    </Text>
                  </div>
                </div>
              </Section>

              {/* Jack's Signature */}
              <Section className="text-center">
                <Text className="mb-[8px] text-[16px] text-gray-700">
                  Need help evaluating this bid or have questions? I'm here to
                  guide you! 🤝
                </Text>
                <Text className="mb-[16px] font-bold text-[16px] text-gray-800">
                  Jack ⚡<br />
                  <span className="font-normal text-[14px] text-blue-600">
                    Your AI Assistant at TradeCrews
                  </span>
                </Text>
              </Section>
            </Section>

            {/* Footer */}
            <Section className="border-gray-200 border-t bg-gray-50 px-[32px] py-[24px] text-center">
              <Text className="mb-[8px] text-[12px] text-gray-600">
                © {new Date().getFullYear()} TradeCrews Inc. All rights
                reserved.
              </Text>
              <Text className="mb-[8px] text-[12px] text-gray-500">
                Connecting homeowners with trusted professionals
              </Text>
              <Text className="m-0 text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Unsubscribe
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/privacy"
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Privacy Policy
                </a>
                {" | "}
                <a
                  href="https://tradecrews.com/help"
                  className="text-gray-500 underline hover:text-gray-700"
                >
                  Help Center
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

NewBidNotificationEmail.PreviewProps = {
  bidName: "New Bid",
  bidId: "123",
  jobName: "New Job",
  contractorName: "John Doe",
  amount: "1000",
  estimatedDuration: 10,
  userId: "123",
  recipientEmail: "<EMAIL>",
};

export default NewBidNotificationEmail;
