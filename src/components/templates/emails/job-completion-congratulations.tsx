import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface JobCompletionCongratulationsProps {
  jobName: string;
  jobId: string;
  recipientRole: "HOMEOWNER" | "PROFESSIONAL";
  counterpartyName: string;
  userId: string;
  recipientEmail: string;
}

const JobCompletionCongratulationsEmail = ({
  jobName,
  jobId,
  recipientRole,
  counterpartyName,
  userId,
  recipientEmail,
}: JobCompletionCongratulationsProps) => {
  const isHomeowner = recipientRole === "HOMEOWNER";

  return (
    <Html>
      <Head />
      <Preview>Jack here - congratulations on completing your project!</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Congratulations! 🎉
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Jack here - your project has been completed successfully!
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-green-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>{isHomeowner ? "Contractor" : "Client"}:</strong>{" "}
                  {counterpartyName}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "What an achievement! I'm so glad I could help connect you with the right professional for your project. I hope you're thrilled with the results!"
                  : "Fantastic work! I'm proud to have helped connect you with this client. Your expertise and professionalism make TradeCrews a better platform for everyone!"}
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}/review`}
                >
                  Share Your Experience
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Your review helps other homeowners find quality professionals like yours. Taking a moment to share your experience makes a real difference!"
                  : "Reviews help build your reputation and attract more great clients. Consider asking your client to share their experience - it really helps your business grow!"}
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Ready for your next project? I'm here to help you find the perfect professional again!"
                  : "Looking for your next opportunity? I'm always working to match skilled professionals like you with great projects!"}
              </Text>

              <Text className="mb-[16px] text-[16px] text-gray-700">
                <strong>Jack</strong>
                <br />
                Your AI Assistant at TradeCrews
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default JobCompletionCongratulationsEmail;
