import { AlertCircle } from "lucide-react";
import type React from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

type QueryRendererProps<TData> = {
  /**
   * The data returned from the query
   */
  data: TData | undefined;

  /**
   * Loading state from the query
   */
  isLoading: boolean;

  /**
   * Error state from the query
   */
  error?: Error | null;

  /**
   * Component to render while loading
   */
  loadingComponent?: React.ReactNode;

  /**
   * Component to render when there's an error
   */
  errorComponent?: React.ReactNode;

  /**
   * Function to render the success state
   */
  children: (data: TData) => React.ReactNode;

  /**
   * Optional empty state when data is empty array or null
   */
  emptyComponent?: React.ReactNode;

  /**
   * Function to check if data is empty (defaults to checking if array is empty)
   */
  isEmpty?: (data: TData) => boolean;
};

/**
 * A component that handles the common loading, error, and success states for queries
 */
export function QueryRenderer<TData>({
  data,
  isLoading,
  error,
  loadingComponent,
  errorComponent,
  emptyComponent,
  isEmpty = (data) => Array.isArray(data) && data.length === 0,
  children,
}: QueryRendererProps<TData>) {
  // Show loading state
  if (isLoading) {
    return loadingComponent || <Skeleton className="h-32 w-full" />;
  }

  // Show error state
  if (error) {
    return (
      errorComponent || (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )
    );
  }

  // Show empty state if data is empty
  if (data && isEmpty(data) && emptyComponent) {
    return emptyComponent;
  }

  // Show success state
  return data ? children(data) : null;
}
