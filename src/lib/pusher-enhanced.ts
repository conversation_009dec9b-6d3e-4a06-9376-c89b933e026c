"use client";

import Pusher from "pusher-js";
import { env } from "@/env";

let pusherInstance: Pusher | null = null;

export interface EnhancedChatCallbacks {
  onMessage?: (data: EnhancedMessageData) => void;
  onTyping?: (data: TypingData) => void;
  onPresence?: (data: PresenceData) => void;
  onMessageStatus?: (data: MessageStatusData) => void;
  onReaction?: (data: ReactionData) => void;
  onEdit?: (data: EditData) => void;
  onDelete?: (data: DeleteData) => void;
  onSubscriptionSucceeded?: () => void;
  onSubscriptionError?: (error: any) => void;
}

export interface EnhancedMessageData {
  id: string;
  content: string;
  senderId: string;
  senderType: string;
  senderName?: string;
  senderAvatar?: string;
  createdAt: Date;
  chatId: string;
  isCommand?: boolean;
  commandData?: string;
  replyToId?: string;
  editedAt?: Date;
  isEdited?: boolean;
  attachments?: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'video';
  url: string;
  name: string;
  size: number;
  mimeType: string;
}

export interface TypingData {
  userId: string;
  userName?: string;
  isTyping: boolean;
  timestamp: string;
}

export interface PresenceData {
  userId: string;
  userName?: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen?: string;
  timestamp: string;
}

export interface MessageStatusData {
  messageId: string;
  status: 'sent' | 'delivered' | 'read';
  userId: string;
  timestamp: string;
}

export interface ReactionData {
  messageId: string;
  userId: string;
  userName?: string;
  emoji: string;
  action: 'add' | 'remove';
  timestamp: string;
}

export interface EditData {
  messageId: string;
  newContent: string;
  editedAt: string;
  userId: string;
}

export interface DeleteData {
  messageId: string;
  userId: string;
  timestamp: string;
}

export function getEnhancedPusherClient() {
  if (!pusherInstance) {
    pusherInstance = new Pusher(env.NEXT_PUBLIC_PUSHER_KEY, {
      cluster: env.NEXT_PUBLIC_PUSHER_CLUSTER,
      authEndpoint: "/api/pusher/auth",
      auth: {
        headers: {
          "Content-Type": "application/json",
        },
      },
      // Enhanced configuration
      enabledTransports: ['ws', 'wss'],
      disabledTransports: [],
      activityTimeout: 120000, // 2 minutes
      pongTimeout: 30000, // 30 seconds
      unavailableTimeout: 10000, // 10 seconds
    });

    // Enable logging in development
    if (process.env.NODE_ENV === 'development') {
      Pusher.logToConsole = true;
    }

    // Enhanced connection event handlers
    pusherInstance.connection.bind('connected', () => {
      console.log('Enhanced Pusher connected');
    });

    pusherInstance.connection.bind('disconnected', () => {
      console.log('Enhanced Pusher disconnected');
    });

    pusherInstance.connection.bind('error', (error: any) => {
      console.error('Enhanced Pusher connection error:', error);
    });
  }

  return pusherInstance;
}

// Enhanced chat channel subscription
export function subscribeToEnhancedChatChannel(
  chatId: string,
  callbacks: EnhancedChatCallbacks,
) {
  const pusher = getEnhancedPusherClient();
  const channelName = `private-chat-${chatId}`;

  // Check if already subscribed
  const existingChannel = pusher.channel(channelName);
  if (existingChannel) {
    return existingChannel;
  }

  const channel = pusher.subscribe(channelName);

  // Bind enhanced event handlers
  if (callbacks.onMessage) {
    channel.bind("message", callbacks.onMessage);
    channel.bind("message:new", callbacks.onMessage);
  }

  if (callbacks.onTyping) {
    channel.bind("typing", callbacks.onTyping);
    channel.bind("typing:start", callbacks.onTyping);
    channel.bind("typing:stop", callbacks.onTyping);
  }

  if (callbacks.onPresence) {
    channel.bind("presence", callbacks.onPresence);
    channel.bind("presence:online", callbacks.onPresence);
    channel.bind("presence:offline", callbacks.onPresence);
    channel.bind("presence:away", callbacks.onPresence);
  }

  if (callbacks.onMessageStatus) {
    channel.bind("message:status", callbacks.onMessageStatus);
    channel.bind("message:delivered", callbacks.onMessageStatus);
    channel.bind("message:read", callbacks.onMessageStatus);
  }

  if (callbacks.onReaction) {
    channel.bind("reaction:add", callbacks.onReaction);
    channel.bind("reaction:remove", callbacks.onReaction);
  }

  if (callbacks.onEdit) {
    channel.bind("message:edit", callbacks.onEdit);
  }

  if (callbacks.onDelete) {
    channel.bind("message:delete", callbacks.onDelete);
  }

  if (callbacks.onSubscriptionSucceeded) {
    channel.bind("pusher:subscription_succeeded", callbacks.onSubscriptionSucceeded);
  }

  if (callbacks.onSubscriptionError) {
    channel.bind("pusher:subscription_error", callbacks.onSubscriptionError);
  }

  return channel;
}

// Enhanced helper functions
export function sendEnhancedTypingIndicator(
  chatId: string,
  userId: string,
  isTyping: boolean,
  userName?: string,
) {
  return fetch("/api/pusher/typing", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      userId,
      isTyping,
      userName,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error sending enhanced typing indicator:", error);
  });
}

export function updateEnhancedPresence(
  chatId: string,
  userId: string,
  status: PresenceData['status'],
  userName?: string,
) {
  return fetch("/api/pusher/presence", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      userId,
      status,
      userName,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error updating enhanced presence:", error);
  });
}

export function sendMessageReaction(
  chatId: string,
  messageId: string,
  userId: string,
  emoji: string,
  action: 'add' | 'remove',
  userName?: string,
) {
  return fetch("/api/pusher/reaction", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      messageId,
      userId,
      emoji,
      action,
      userName,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error sending message reaction:", error);
  });
}

export function markMessageAsRead(
  chatId: string,
  messageId: string,
  userId: string,
) {
  return fetch("/api/pusher/message-status", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      messageId,
      userId,
      status: 'read',
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error marking message as read:", error);
  });
}

export function editMessage(
  chatId: string,
  messageId: string,
  userId: string,
  newContent: string,
) {
  return fetch("/api/pusher/edit-message", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      messageId,
      userId,
      newContent,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error editing message:", error);
  });
}

export function deleteMessage(
  chatId: string,
  messageId: string,
  userId: string,
) {
  return fetch("/api/pusher/delete-message", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      messageId,
      userId,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.error("Error deleting message:", error);
  });
}

// Enhanced presence management
export class EnhancedPresenceManager {
  private chatId: string;
  private userId: string;
  private userName?: string;
  private status: PresenceData['status'] = 'online';
  private heartbeatInterval?: NodeJS.Timeout;
  private awayTimeout?: NodeJS.Timeout;

  constructor(chatId: string, userId: string, userName?: string) {
    this.chatId = chatId;
    this.userId = userId;
    this.userName = userName;
    this.startHeartbeat();
    this.setupActivityListeners();
  }

  private startHeartbeat() {
    // Send presence update every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      updateEnhancedPresence(this.chatId, this.userId, this.status, this.userName);
    }, 30000);
  }

  private setupActivityListeners() {
    const resetAwayTimer = () => {
      if (this.status === 'away') {
        this.setStatus('online');
      }
      
      if (this.awayTimeout) {
        clearTimeout(this.awayTimeout);
      }
      
      // Set to away after 5 minutes of inactivity
      this.awayTimeout = setTimeout(() => {
        this.setStatus('away');
      }, 5 * 60 * 1000);
    };

    // Listen for user activity
    document.addEventListener('mousemove', resetAwayTimer);
    document.addEventListener('keypress', resetAwayTimer);
    document.addEventListener('click', resetAwayTimer);
    document.addEventListener('scroll', resetAwayTimer);

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.setStatus('away');
      } else {
        this.setStatus('online');
        resetAwayTimer();
      }
    });

    // Initial timer
    resetAwayTimer();
  }

  setStatus(status: PresenceData['status']) {
    if (this.status !== status) {
      this.status = status;
      updateEnhancedPresence(this.chatId, this.userId, status, this.userName);
    }
  }

  destroy() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.awayTimeout) {
      clearTimeout(this.awayTimeout);
    }
    
    // Set offline status
    updateEnhancedPresence(this.chatId, this.userId, 'offline', this.userName);
  }
}

// Enhanced typing manager
export class EnhancedTypingManager {
  private chatId: string;
  private userId: string;
  private userName?: string;
  private isTyping = false;
  private typingTimeout?: NodeJS.Timeout;

  constructor(chatId: string, userId: string, userName?: string) {
    this.chatId = chatId;
    this.userId = userId;
    this.userName = userName;
  }

  startTyping() {
    if (!this.isTyping) {
      this.isTyping = true;
      sendEnhancedTypingIndicator(this.chatId, this.userId, true, this.userName);
    }

    // Clear existing timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    // Stop typing after 3 seconds of inactivity
    this.typingTimeout = setTimeout(() => {
      this.stopTyping();
    }, 3000);
  }

  stopTyping() {
    if (this.isTyping) {
      this.isTyping = false;
      sendEnhancedTypingIndicator(this.chatId, this.userId, false, this.userName);
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }

  destroy() {
    this.stopTyping();
  }
}

// Disconnect enhanced Pusher
export function disconnectEnhancedPusher() {
  if (pusherInstance) {
    pusherInstance.disconnect();
    pusherInstance = null;
  }
}

// Unsubscribe from enhanced chat channel
export function unsubscribeFromEnhancedChatChannel(chatId: string) {
  const pusher = getEnhancedPusherClient();
  const channelName = `private-chat-${chatId}`;
  pusher.unsubscribe(channelName);
}