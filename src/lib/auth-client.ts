import {
	adminClient,
	inferAdditionalFields,
	twoFactorClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
import type { auth } from "./auth";
import { ac, admin, contractor, homeowner } from "./permissions";

export const authClient = createAuthClient({
	plugins: [
		inferAdditionalFields<typeof auth>(),
		adminClient({
			ac,
			roles: {
				admin,
				homeowner,
				contractor,
			},
		}),
		twoFactorClient(),
	],
});

export const { signIn, signUp, signOut, useSession } = authClient;
