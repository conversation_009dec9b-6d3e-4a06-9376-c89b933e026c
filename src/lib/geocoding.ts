import { env } from "@/env";

export async function geocodeAddress(address: {
  street: string;
  city: string;
  state: string;
  zip: string;
}): Promise<{ x: number; y: number } | null> {
  try {
    const query = encodeURIComponent(
      `${address.street}, ${address.city}, ${address.state} ${address.zip}`,
    );
    const response = await fetch(
      `${env.GEOCODING_API_URL}/search?q=${query}&format=json&limit=1`,
      {
        headers: {
          "User-Agent": "TradeCrew",
        },
      },
    );

    if (!response.ok) {
      console.error("Geocoding API error:", response.statusText);
      return null;
    }

    const data = await response.json();

    if (data.length === 0) {
      console.error("No geocoding results found for address");
      return null;
    }

    return {
      x: Number.parseFloat(data[0].lat),
      y: Number.parseFloat(data[0].lon),
    };
  } catch (error) {
    console.error("Geocoding error:", error);
    return null;
  }
}
