import { z } from "zod";

// ============================================================================
// COMMON BASE SCHEMAS
// ============================================================================

/**
 * Common ID validation schema
 */
export const idSchema = z.string().min(1, "ID is required");

/**
 * Optional ID schema
 */
export const optionalIdSchema = z.string().optional();

/**
 * Array of IDs schema
 */
export const idsSchema = z.array(z.string()).optional();

/**
 * Exclude IDs schema (commonly used in search)
 */
export const excludeIdsSchema = z.array(z.string()).optional();

// ============================================================================
// PAGINATION & FILTERING SCHEMAS
// ============================================================================

/**
 * Basic pagination schema
 */
export const paginationSchema = z.object({
  limit: z.number().min(1).max(100).optional().default(10),
  offset: z.number().min(0).optional().default(0),
});

/**
 * Cursor-based pagination schema
 */
export const cursorPaginationSchema = z.object({
  limit: z.number().min(1).max(100).optional().default(10),
  cursor: z.string().optional(),
});

/**
 * Search query schema
 */
export const searchSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  excludeIds: excludeIdsSchema,
});

/**
 * Optional search schema
 */
export const optionalSearchSchema = z.object({
  query: z.string().optional(),
  excludeIds: excludeIdsSchema,
});

/**
 * Sorting schema
 */
export const sortSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

// ============================================================================
// DATE & TIME SCHEMAS
// ============================================================================

/**
 * Future date validation
 */
export const futureDateSchema = z.date().refine((date) => date > new Date(), {
  message: "Date must be in the future",
});

/**
 * Date range schema
 */
export const dateRangeSchema = z
  .object({
    startDate: z.date(),
    endDate: z.date(),
  })
  .refine((data) => data.startDate < data.endDate, {
    message: "Start date must be before end date",
    path: ["startDate"],
  });

/**
 * Optional date range schema
 */
export const optionalDateRangeSchema = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

/**
 * Schedule date schema with validation
 */
export const scheduleDateSchema = z
  .object({
    proposedStartDate: futureDateSchema,
    proposedEndDate: futureDateSchema,
  })
  .refine((data) => data.proposedStartDate < data.proposedEndDate, {
    message: "Start date must be before end date",
    path: ["proposedStartDate"],
  });

// ============================================================================
// ADDRESS SCHEMAS
// ============================================================================

/**
 * Complete address schema
 */
export const addressSchema = z.object({
  street: z.string().min(1, "Street is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  zip: z.string().min(1, "ZIP code is required"),
});

/**
 * Optional address schema
 */
export const optionalAddressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
});

// ============================================================================
// CONTACT INFORMATION SCHEMAS
// ============================================================================

/**
 * Email validation schema
 */
export const emailSchema = z.string().email("Invalid email address");

/**
 * Optional email schema
 */
export const optionalEmailSchema = z
  .string()
  .email("Invalid email address")
  .optional();

/**
 * Phone number schema
 */
export const phoneSchema = z.string().optional();

/**
 * Contact information schema
 */
export const contactSchema = z.object({
  email: optionalEmailSchema,
  phone: phoneSchema,
});

// ============================================================================
// FINANCIAL SCHEMAS
// ============================================================================

/**
 * Positive amount schema
 */
export const positiveAmountSchema = z
  .number()
  .min(0.01, "Amount must be greater than 0");

/**
 * Budget schema
 */
export const budgetSchema = z.number().min(1, "Budget must be greater than 0");

/**
 * Optional budget schema
 */
export const optionalBudgetSchema = z
  .number()
  .min(1, "Budget must be greater than 0")
  .optional();

/**
 * Rating schema (1-5 stars)
 */
export const ratingSchema = z
  .number()
  .min(1, "Please select a rating")
  .max(5, "Rating cannot exceed 5");

// ============================================================================
// TEXT & CONTENT SCHEMAS
// ============================================================================

/**
 * Required name schema
 */
export const nameSchema = z.string().min(1, "Name is required");

/**
 * Optional name schema
 */
export const optionalNameSchema = z.string().optional();

/**
 * Long name schema (for organizations, etc.)
 */
export const longNameSchema = z
  .string()
  .min(2, "Name must be at least 2 characters");

/**
 * Description schema
 */
export const descriptionSchema = z.string().min(1, "Description is required");

/**
 * Long description schema
 */
export const longDescriptionSchema = z
  .string()
  .min(10, "Please provide a detailed description");

/**
 * Optional description schema
 */
export const optionalDescriptionSchema = z.string().optional();

/**
 * Notes schema
 */
export const notesSchema = z.string().optional();

/**
 * Comment schema
 */
export const commentSchema = z.string().min(1, "Please provide a comment");

// ============================================================================
// DURATION & TIME SCHEMAS
// ============================================================================

/**
 * Duration in days schema
 */
export const durationDaysSchema = z
  .number()
  .min(1, "Duration must be at least 1 day");

/**
 * Estimated duration schema
 */
export const estimatedDurationSchema = z
  .number()
  .min(1, "Estimated duration is required");

/**
 * Optional duration schema
 */
export const optionalDurationSchema = z.number().min(1).optional();

/**
 * Distance schema (for search radius)
 */
export const distanceSchema = z.number().min(1).max(500).optional().default(50);

// ============================================================================
// BOOLEAN FLAGS SCHEMAS
// ============================================================================

/**
 * Optional boolean schema
 */
export const optionalBooleanSchema = z.boolean().optional();

/**
 * Default false boolean schema
 */
export const defaultFalseBooleanSchema = z.boolean().default(false);

/**
 * Default true boolean schema
 */
export const defaultTrueBooleanSchema = z.boolean().default(true);

// ============================================================================
// ENUM SCHEMAS
// ============================================================================

/**
 * Job type schema
 */
export const jobTypeSchema = z.enum(["STANDARD", "QUICK_HIRE"]);

/**
 * Job status schema
 */
export const jobStatusSchema = z.enum([
  "DRAFT",
  "PUBLISHED",
  "CLOSED",
  "CANCELED",
  "AWARDED",
  "COMPLETED",
]);

/**
 * Bid status schema
 */
export const bidStatusSchema = z.enum([
  "PROPOSED",
  "ACCEPTED",
  "REJECTED",
  "CANCELED",
  "WITHDRAWN",
]);

/**
 * Review type schema
 */
export const reviewTypeSchema = z.enum([
  "HOMEOWNER_REVIEW",
  "CONTRACTOR_REVIEW",
]);

/**
 * User role schema
 */
export const userRoleSchema = z.enum(["homeowner", "contractor", "admin"]);

/**
 * Theme schema
 */
export const themeSchema = z.enum(["light", "dark", "system"]);

// ============================================================================
// COMPLEX OBJECT SCHEMAS
// ============================================================================

/**
 * Image schema
 */
export const imageSchema = z.object({
  url: z.string().url("Invalid image URL"),
  description: z.string().optional().nullable(),
});

/**
 * Images array schema
 */
export const imagesSchema = z.array(imageSchema).optional();

/**
 * Task schema
 */
export const taskSchema = z.object({
  name: nameSchema,
  tradeId: idSchema,
});

/**
 * Tasks array schema
 */
export const tasksSchema = z.array(taskSchema);

/**
 * Single task array schema (for quick hire)
 */
export const singleTaskSchema = tasksSchema.refine(
  (tasks) => tasks.length === 1,
  {
    message: "Quick hire jobs must have exactly one task",
    path: ["tasks"],
  },
);

/**
 * Trade selection schema
 */
export const tradeSelectionSchema = z.object({
  id: idSchema,
});

// ============================================================================
// NOTIFICATION SCHEMAS
// ============================================================================

/**
 * Email notification settings schema
 */
export const emailNotificationSchema = z.object({
  marketing: optionalBooleanSchema,
  jobUpdates: optionalBooleanSchema,
  messages: optionalBooleanSchema,
  bids: optionalBooleanSchema,
});

/**
 * Push notification settings schema
 */
export const pushNotificationSchema = z.object({
  enabled: optionalBooleanSchema,
  jobUpdates: optionalBooleanSchema,
  messages: optionalBooleanSchema,
  bids: optionalBooleanSchema,
});

/**
 * Notification settings schema
 */
export const notificationSettingsSchema = z
  .object({
    email: emailNotificationSchema.optional(),
    push: pushNotificationSchema.optional(),
  })
  .optional();

// ============================================================================
// UTILITY SCHEMAS
// ============================================================================

/**
 * Success response schema
 */
export const successResponseSchema = z.object({
  success: z.literal(true),
});

/**
 * Either/or validation helper
 */
export const eitherOrSchema = <T extends Record<string, unknown>>(
  schema: z.ZodType<T>,
  field1: keyof T,
  field2: keyof T,
  message = `Either ${String(field1)} or ${String(field2)} must be provided`,
) => {
  return schema.refine((data) => !!data[field1] || !!data[field2], { message });
};

/**
 * Password validation schema
 */
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters");

/**
 * Confirm password schema helper
 */
export const confirmPasswordSchema = <T extends { password: string }>(
  schema: z.ZodType<T & { confirmPassword: string }>,
) => {
  return schema.refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });
};

// ============================================================================
// COMPOSITE SCHEMAS (Common Combinations)
// ============================================================================

/**
 * Basic entity input schema (ID only)
 */
export const entityInputSchema = z.object({
  id: idSchema,
});

/**
 * List input schema with optional pagination and search
 */
export const listInputSchema = z.object({
  ...paginationSchema.shape,
  ...optionalSearchSchema.shape,
  ...sortSchema.shape,
});

/**
 * Search with pagination schema
 */
export const searchWithPaginationSchema = z.object({
  ...searchSchema.shape,
  ...paginationSchema.shape,
});

/**
 * Basic create input schema
 */
export const createInputSchema = z.object({
  name: nameSchema,
  description: optionalDescriptionSchema,
});

/**
 * Basic update input schema
 */
export const updateInputSchema = z.object({
  id: idSchema,
  name: nameSchema.optional(),
  description: optionalDescriptionSchema,
});

/**
 * Job creation base schema
 */
export const jobCreateBaseSchema = z
  .object({
    name: nameSchema,
    budget: budgetSchema,
    propertyId: idSchema,
    startsAt: futureDateSchema,
    deadline: futureDateSchema,
    jobType: jobTypeSchema.default("STANDARD"),
    isRecurring: defaultFalseBooleanSchema,
    recurringFrequency: z.string().nullable().optional(),
    images: imagesSchema,
    templateId: optionalIdSchema,
  })
  .refine((data) => data.startsAt < data.deadline, {
    message: "Start date must be before bid deadline",
    path: ["startsAt"],
  });

/**
 * Bid creation schema
 */
export const bidCreateSchema = z.object({
  jobId: idSchema,
  organizationId: idSchema,
  name: nameSchema,
  amount: positiveAmountSchema,
  description: longDescriptionSchema,
  estimatedDuration: estimatedDurationSchema,
});

/**
 * Bid update schema
 */
export const bidUpdateSchema = z.object({
  id: idSchema,
  name: optionalNameSchema,
  amount: positiveAmountSchema.optional(),
  description: optionalDescriptionSchema,
  estimatedDuration: optionalDurationSchema,
});

/**
 * Review creation schema
 */
export const reviewCreateSchema = z.object({
  jobId: idSchema,
  rating: ratingSchema,
  comment: commentSchema,
  reviewType: reviewTypeSchema,
});

/**
 * Property creation schema
 */
export const propertyCreateSchema = z.object({
  name: nameSchema,
  imageUrl: z.string().url("Invalid image URL"),
  address: addressSchema,
});

/**
 * Schedule proposal schema
 */
export const scheduleProposalSchema = z
  .object({
    jobId: idSchema,
    proposedStartDate: futureDateSchema,
    proposedEndDate: futureDateSchema,
    notes: notesSchema,
  })
  .refine((data) => data.proposedStartDate < data.proposedEndDate, {
    message: "Start date must be before end date",
    path: ["proposedStartDate"],
  });

/**
 * User settings update schema
 */
export const userSettingsUpdateSchema = z.object({
  notifications: notificationSettingsSchema,
  theme: themeSchema.optional(),
  display: z
    .object({
      compactView: optionalBooleanSchema,
      showTutorials: optionalBooleanSchema,
    })
    .optional(),
});

/**
 * Chat creation schema (either bid or job)
 */
export const chatCreateSchema = eitherOrSchema(
  z.object({
    bidId: optionalIdSchema,
    jobId: optionalIdSchema,
  }),
  "bidId",
  "jobId",
);

/**
 * Message creation schema
 */
export const messageCreateSchema = z.object({
  chatId: idSchema,
  content: z.string().min(1, "Message content is required"),
});

/**
 * Template creation schema
 */
export const templateCreateSchema = z.object({
  name: nameSchema,
  description: descriptionSchema,
  budget: budgetSchema,
  tasks: tasksSchema.min(1, "At least one task is required"),
  estimatedDuration: optionalDurationSchema,
});

/**
 * Organization creation schema
 */
export const organizationCreateSchema = z.object({
  name: longNameSchema,
  trade: tradeSelectionSchema,
  description: optionalDescriptionSchema,
  ...contactSchema.shape,
  address: addressSchema,
  acceptsQuickHire: defaultFalseBooleanSchema,
});

export const tradeCreateSchema = z.object({
  name: nameSchema,
  availableForQuickHire: defaultFalseBooleanSchema,
});

export const tradeUpdateSchema = z.object({
  id: idSchema,
  name: optionalNameSchema,
  availableForQuickHire: optionalBooleanSchema,
});
