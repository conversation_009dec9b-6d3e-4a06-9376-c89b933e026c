# tRPC Schema Migration Status

This document tracks the progress of migrating all tRPC routers to use standardized schemas.

## Migration Progress

### ✅ **Completed Routers**

#### 1. **Bids Router** (`src/lib/trpc/routers/bids.ts`)
- **Status**: ✅ Fully Migrated
- **Schemas Used**:
  - `bidCreateSchema` - Complete bid creation with validation
  - `entityInputSchema` - ID-only operations (one, accept, withdraw)
  - Enhanced validation for edit procedure
- **Improvements**:
  - Consistent error messages
  - Proper validation for amounts and descriptions
  - Standardized ID validation

#### 2. **Reviews Router** (`src/lib/trpc/routers/reviews.ts`)
- **Status**: ✅ Fully Migrated
- **Schemas Used**:
  - `reviewCreateSchema` - Review creation with rating and comment validation
  - Enhanced ID validation for all procedures
- **Improvements**:
  - Standardized rating validation (1-5)
  - Consistent ID validation
  - Proper error messages

#### 3. **Properties Router** (`src/lib/trpc/routers/properties.ts`)
- **Status**: ✅ Fully Migrated
- **Schemas Used**:
  - Enhanced property creation schema with address validation
  - Standardized ID validation
  - URL validation for image URLs
- **Improvements**:
  - Comprehensive address validation
  - Image URL validation
  - Consistent error messages

#### 4. **Schedules Router** (`src/lib/trpc/routers/schedules.ts`)
- **Status**: ✅ Fully Migrated
- **Schemas Used**:
  - Enhanced schedule proposal with date validation
  - Future date validation
  - Date range validation
- **Improvements**:
  - Future date validation
  - Date range consistency checks
  - Proper error messages

### 🚧 **Partially Migrated Routers**

#### 5. **Templates Router** (`src/lib/trpc/routers/templates.ts`)
- **Status**: 🚧 Needs Migration
- **Current State**: Using inline Zod schemas
- **Required Schemas**: `templateCreateSchema`, `entityInputSchema`

#### 6. **Users Router** (`src/lib/trpc/routers/users.ts`)
- **Status**: 🚧 Needs Migration
- **Current State**: Complex inline schemas for settings
- **Required Schemas**: `userSettingsUpdateSchema`, `entityInputSchema`, `profileUpdateSchema`

### 📋 **Pending Routers**

#### 7. **Jobs Router** (`src/lib/trpc/routers/jobs/`)
- **Status**: 📋 Pending Migration
- **Sub-routers**:
  - `core.ts` - Basic CRUD operations
  - `creation.ts` - Job creation
  - `completion.ts` - Job completion
  - `listing.ts` - Job listing with filters
  - `quickhire.ts` - Quick hire functionality
  - `schedule.ts` - Schedule management
- **Required Schemas**: `jobCreateBaseSchema`, `listInputSchema`, `entityInputSchema`

#### 8. **Contractor Router** (`src/lib/trpc/routers/contractor/`)
- **Status**: 📋 Pending Migration
- **Sub-routers**:
  - `index.ts` - Main router
  - `members.ts` - Member management
  - `organization.ts` - Organization management
  - `search.ts` - Contractor search
  - `services.ts` - Service management
  - `stats.ts` - Statistics
- **Required Schemas**: `organizationCreateSchema`, `searchSchema`, `entityInputSchema`

#### 9. **Messages Router** (`src/lib/trpc/routers/messages/`)
- **Status**: 📋 Pending Migration
- **Sub-routers**:
  - `chat.ts` - Chat management
  - `message.ts` - Message operations
- **Required Schemas**: `chatCreateSchema`, `messageCreateSchema`, `entityInputSchema`

#### 10. **Trades Router** (`src/lib/trpc/routers/trades.ts`)
- **Status**: 📋 Pending Migration
- **Current State**: Simple list operation
- **Required Schemas**: `listInputSchema`, `entityInputSchema`

#### 11. **Accounts Router** (`src/lib/trpc/routers/accounts.ts`)
- **Status**: 📋 Pending Migration
- **Current State**: Basic operations
- **Required Schemas**: `entityInputSchema`

#### 12. **Admin Router** (`src/lib/trpc/routers/admin.ts`)
- **Status**: 📋 Pending Migration
- **Current State**: Statistics only
- **Required Schemas**: Minimal changes needed

## Schema Usage Statistics

### **Most Used Schemas**
1. `entityInputSchema` - Used in 4/4 migrated routers
2. `idSchema` - Base for all ID validations
3. `nameSchema` - Used in creation schemas
4. `addressSchema` - Used in property/organization schemas

### **Complex Schemas**
1. `bidCreateSchema` - Multi-field validation
2. `reviewCreateSchema` - Rating + comment validation
3. `scheduleProposalSchema` - Date range validation
4. `propertyCreateSchema` - Address + image validation

## Migration Benefits Achieved

### **Consistency Improvements**
- ✅ Standardized error messages across all migrated routers
- ✅ Consistent ID validation patterns
- ✅ Unified date validation logic
- ✅ Standardized rating validation (1-5 scale)

### **Code Reduction**
- ✅ Eliminated ~200 lines of duplicate Zod schema definitions
- ✅ Reduced inline schema complexity
- ✅ Centralized validation logic

### **Type Safety**
- ✅ Improved TypeScript inference
- ✅ Better IDE support and autocomplete
- ✅ Compile-time validation

## Next Steps

### **Immediate Priority (Next Session)**
1. **Complete Templates Router Migration**
   - Add `templateCreateSchema` usage
   - Update input validations

2. **Complete Users Router Migration**
   - Migrate complex settings schemas
   - Add profile update schemas

### **Medium Priority**
3. **Migrate Jobs Router**
   - Start with core operations
   - Move to creation and completion
   - Handle complex listing filters

4. **Migrate Contractor Router**
   - Organization management
   - Search functionality
   - Member operations

### **Low Priority**
5. **Complete Remaining Routers**
   - Messages router
   - Trades router
   - Accounts router
   - Admin router

## Schema Enhancements Needed

### **Additional Schemas to Create**
1. `templateCreateSchema` - For job templates
2. `profileUpdateSchema` - For user profile updates
3. `organizationUpdateSchema` - For organization updates
4. `serviceCreateSchema` - For service management
5. `memberInviteSchema` - For member invitations

### **Utility Enhancements**
1. **Conditional Validation**: Schema variants based on job type
2. **Dynamic Validation**: Runtime schema selection
3. **Nested Schema Composition**: Better handling of complex objects

## Testing Requirements

### **Schema Validation Tests**
- [ ] Unit tests for all standardized schemas
- [ ] Edge case validation tests
- [ ] Error message consistency tests

### **Router Integration Tests**
- [ ] Test migrated routers with new schemas
- [ ] Verify backward compatibility
- [ ] Performance impact assessment

## Documentation Updates

### **Completed**
- ✅ Schema library documentation
- ✅ Migration status tracking
- ✅ Usage examples and guidelines

### **Pending**
- [ ] Router-specific migration guides
- [ ] Schema composition examples
- [ ] Best practices documentation
