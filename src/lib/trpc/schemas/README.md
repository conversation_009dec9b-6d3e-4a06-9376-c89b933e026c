# tRPC Schema Standardization

This document outlines the standardized Zod schemas created to improve consistency and reduce duplication across tRPC routers.

## Overview

The schema standardization provides:
- **Reusable validation schemas** for common patterns
- **Consistent error messages** across the application
- **Type safety** with proper TypeScript inference
- **Reduced code duplication** in router definitions
- **Easier maintenance** and updates to validation rules

## Schema Categories

### 1. Base Schemas
Common building blocks used throughout the application:

```typescript
// ID validation
idSchema                 // Required string ID
optionalIdSchema        // Optional string ID
idsSchema              // Array of IDs
excludeIdsSchema       // Array of IDs to exclude (for search)

// Text validation
nameSchema             // Required name
optionalNameSchema     // Optional name
longNameSchema         // Name with minimum 2 characters
descriptionSchema      // Required description
longDescriptionSchema  // Description with minimum 10 characters
optionalDescriptionSchema // Optional description
notesSchema           // Optional notes
commentSchema         // Required comment
```

### 2. Pagination & Filtering
Standardized schemas for data querying:

```typescript
paginationSchema          // Basic offset/limit pagination
cursorPaginationSchema    // Cursor-based pagination
searchSchema             // Search with query and excludeIds
optionalSearchSchema     // Optional search parameters
sortSchema              // Sorting with sortBy and sortOrder
```

### 3. Date & Time
Date validation with business logic:

```typescript
futureDateSchema         // Date must be in future
dateRangeSchema         // Start/end date with validation
optionalDateRangeSchema // Optional date range
scheduleDateSchema      // Schedule dates with validation
```

### 4. Financial
Money and rating validation:

```typescript
positiveAmountSchema    // Amount > 0
budgetSchema           // Budget > 0
optionalBudgetSchema   // Optional budget
ratingSchema          // 1-5 star rating
```

### 5. Contact & Address
Location and contact information:

```typescript
addressSchema          // Complete address (street, city, state, zip)
optionalAddressSchema  // Optional address fields
emailSchema           // Email validation
optionalEmailSchema   // Optional email
phoneSchema          // Optional phone
contactSchema        // Email + phone combination
```

### 6. Enums
Application-specific enumerations:

```typescript
jobTypeSchema         // "STANDARD" | "QUICK_HIRE"
jobStatusSchema       // Job lifecycle states
bidStatusSchema       // Bid lifecycle states
reviewTypeSchema      // "HOMEOWNER_REVIEW" | "CONTRACTOR_REVIEW"
userRoleSchema        // User roles
themeSchema          // UI theme options
```

### 7. Complex Objects
Composite schemas for complex data structures:

```typescript
imageSchema           // URL + optional description
imagesSchema         // Array of images
taskSchema           // Name + tradeId
tasksSchema          // Array of tasks
tradeSelectionSchema // Trade selection object
```

### 8. Composite Schemas
Pre-built combinations for common use cases:

```typescript
entityInputSchema        // Basic { id: string }
listInputSchema         // Pagination + search + sort
bidCreateSchema         // Complete bid creation
bidUpdateSchema         // Bid update with optional fields
reviewCreateSchema      // Review creation
propertyCreateSchema    // Property creation
scheduleProposalSchema  // Schedule proposal
```

## Usage Examples

### Before (Duplicated Schema)
```typescript
// In multiple routers
.input(z.object({ id: z.string() }))
.input(z.object({ 
  name: z.string(),
  amount: z.number(),
  description: z.string(),
  estimatedDuration: z.number()
}))
```

### After (Standardized Schema)
```typescript
// Import once, use everywhere
import { entityInputSchema, bidCreateSchema } from "../schemas";

.input(entityInputSchema)
.input(bidCreateSchema)
```

## Benefits Achieved

### 1. **Consistency**
- All ID validations use the same schema
- Error messages are consistent across the app
- Validation rules are centralized

### 2. **Maintainability**
- Change validation rules in one place
- Easy to add new common patterns
- Reduced code duplication

### 3. **Type Safety**
- Full TypeScript inference
- Compile-time validation
- Better IDE support

### 4. **Developer Experience**
- Clear, descriptive schema names
- Comprehensive documentation
- Easy to discover available schemas

## Implementation Status

### ✅ Completed
- [x] Base schema library created (`src/lib/trpc/schemas/index.ts`)
- [x] Comprehensive schema categories defined
- [x] Utility functions and helpers added
- [x] Bids router partially updated as demonstration

### 🚧 In Progress
- [ ] Complete bids router migration
- [ ] Update remaining routers to use standardized schemas
- [ ] Add validation for edge cases

### 📋 Next Steps
1. **Complete Router Migration**: Update all remaining routers
2. **Query Optimization**: Add common query builders
3. **Permission Enhancement**: Standardize authorization patterns
4. **Error Handling**: Create consistent error response schemas
5. **CRUD Builders**: Generic procedure generators

## Usage Guidelines

### 1. **Import What You Need**
```typescript
import { 
  entityInputSchema, 
  bidCreateSchema, 
  listInputSchema 
} from "../schemas";
```

### 2. **Prefer Composite Schemas**
Use pre-built combinations when available:
```typescript
// Good
.input(bidCreateSchema)

// Avoid
.input(z.object({
  jobId: z.string(),
  organizationId: z.string(),
  // ... rest of fields
}))
```

### 3. **Extend When Needed**
```typescript
const customBidSchema = bidCreateSchema.extend({
  customField: z.string().optional()
});
```

### 4. **Use Utility Functions**
```typescript
const chatSchema = eitherOrSchema(
  z.object({ bidId: optionalIdSchema, jobId: optionalIdSchema }),
  "bidId",
  "jobId"
);
```

## Contributing

When adding new schemas:
1. Follow existing naming conventions
2. Add comprehensive JSDoc comments
3. Group related schemas together
4. Create composite schemas for common combinations
5. Update this documentation

## Future Enhancements

- **Schema Versioning**: Handle schema evolution
- **Custom Validators**: Domain-specific validation functions
- **Schema Testing**: Automated validation testing
- **Performance Optimization**: Schema compilation optimization
- **Documentation Generation**: Auto-generate schema docs
