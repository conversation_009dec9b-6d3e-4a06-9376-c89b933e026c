import type { QueryClient } from "@tanstack/react-query";
import { queryStateUtils } from "./query-optimization";

/**
 * Parallel query execution utilities
 */
export const parallelQueries = {
  /**
   * Execute multiple queries in parallel and return combined state
   */
  executeParallel: async <T extends Record<string, any>>(
    queryClient: QueryClient,
    queries: { [K in keyof T]: () => Promise<T[K]> },
  ): Promise<{
    data: T | null;
    errors: Record<keyof T, Error | null>;
    isLoading: boolean;
  }> => {
    const queryEntries = Object.entries(queries) as Array<
      [keyof T, () => Promise<T[keyof T]>]
    >;

    const results = await Promise.allSettled(
      queryEntries.map(([key, queryFn]) =>
        queryFn()
          .then((data) => ({ key, data }))
          .catch((error) => ({ key, error })),
      ),
    );

    const data = {} as T;
    const errors = {} as Record<keyof T, Error | null>;
    let hasErrors = false;

    for (const result of results) {
      if (result.status === "fulfilled") {
        const resultValue = result.value;
        if ("error" in resultValue) {
          // This is an error result: { key, error }
          errors[resultValue.key] = resultValue.error;
          hasErrors = true;
        } else {
          // This is a success result: { key, data }
          data[resultValue.key] = resultValue.data;
          errors[resultValue.key] = null;
        }
      } else {
        // This shouldn't happen with our error handling above, but just in case
        const resultIndex = results.indexOf(result);
        const key = queryEntries[resultIndex]?.[0];
        if (key) {
          errors[key] = new Error(result.reason);
          hasErrors = true;
        }
      }
    }

    return {
      data: hasErrors ? null : data,
      errors,
      isLoading: false,
    };
  },

  /**
   * Prefetch multiple queries in parallel
   */
  prefetchParallel: async (
    queryClient: QueryClient,
    queries: Array<{
      queryKey: any[];
      queryFn: () => Promise<any>;
      staleTime?: number;
    }>,
  ): Promise<void> => {
    const prefetchPromises = queries.map(({ queryKey, queryFn, staleTime }) =>
      queryClient.prefetchQuery({
        queryKey,
        queryFn,
        staleTime,
      }),
    );

    await Promise.allSettled(prefetchPromises);
  },

  /**
   * Batch invalidate multiple query keys
   */
  batchInvalidate: async (
    queryClient: QueryClient,
    queryKeys: Array<any[]>,
  ): Promise<void> => {
    const invalidatePromises = queryKeys.map((queryKey) =>
      queryClient.invalidateQueries({ queryKey }),
    );

    await Promise.allSettled(invalidatePromises);
  },
};

/**
 * Query dependency management
 */
export const queryDependencies = {
  /**
   * Create a dependency chain where queries depend on previous results
   */
  createDependencyChain: <T extends Record<string, any>>(
    dependencies: {
      [K in keyof T]: {
        queryFn: (deps: Partial<T>) => Promise<T[K]>;
        dependsOn?: Array<keyof T>;
      };
    },
  ) => {
    return async (): Promise<T> => {
      const result = {} as T;
      const completed = new Set<keyof T>();
      const pending = new Map<keyof T, Promise<T[keyof T]>>();

      const executeQuery = async (key: keyof T): Promise<T[keyof T]> => {
        if (completed.has(key)) {
          return result[key];
        }

        if (pending.has(key)) {
          return pending.get(key)!;
        }

        const config = dependencies[key];

        // Wait for dependencies
        if (config.dependsOn) {
          await Promise.all(
            config.dependsOn.map((depKey) => executeQuery(depKey)),
          );
        }

        const promise = config.queryFn(result);
        pending.set(key, promise);

        try {
          const data = await promise;
          result[key] = data;
          completed.add(key);
          pending.delete(key);
          return data;
        } catch (error) {
          pending.delete(key);
          throw error;
        }
      };

      // Execute all queries
      await Promise.all(
        Object.keys(dependencies).map((key) => executeQuery(key as keyof T)),
      );

      return result;
    };
  },

  /**
   * Create conditional query execution based on user role/permissions
   */
  createConditionalQueries: <T extends Record<string, any>>(
    conditions: {
      [K in keyof T]: {
        queryFn: () => Promise<T[K]>;
        condition: () => boolean;
        fallback?: T[K];
      };
    },
  ) => {
    return async (): Promise<T> => {
      const result = {} as T;
      const promises: Promise<void>[] = [];

      for (const [key, config] of Object.entries(conditions) as Array<
        [keyof T, (typeof conditions)[keyof T]]
      >) {
        if (config.condition()) {
          promises.push(
            config
              .queryFn()
              .then((data) => {
                result[key] = data;
              })
              .catch(() => {
                if (config.fallback !== undefined) {
                  result[key] = config.fallback;
                }
              }),
          );
        } else if (config.fallback !== undefined) {
          result[key] = config.fallback;
        }
      }

      await Promise.allSettled(promises);
      return result;
    };
  },
};

/**
 * Query batching utilities
 */
export const queryBatching = {
  /**
   * Batch similar queries together
   */
  batchSimilarQueries: <TInput, TOutput>(
    inputs: TInput[],
    batchQueryFn: (inputs: TInput[]) => Promise<TOutput[]>,
    batchSize = 10,
  ) => {
    return async (): Promise<TOutput[]> => {
      const batches: TInput[][] = [];

      // Split inputs into batches
      for (let i = 0; i < inputs.length; i += batchSize) {
        batches.push(inputs.slice(i, i + batchSize));
      }

      // Execute batches in parallel
      const batchPromises = batches.map((batch) => batchQueryFn(batch));
      const batchResults = await Promise.all(batchPromises);

      // Flatten results
      return batchResults.flat();
    };
  },

  /**
   * Debounce query execution to batch rapid requests
   */
  createDebouncedBatch: <TInput, TOutput>(
    batchQueryFn: (inputs: TInput[]) => Promise<TOutput[]>,
    debounceMs = 100,
  ) => {
    let pendingInputs: TInput[] = [];
    let pendingResolvers: Array<(results: TOutput[]) => void> = [];
    let timeoutId: NodeJS.Timeout | null = null;

    const executeBatch = async () => {
      const inputs = [...pendingInputs];
      const resolvers = [...pendingResolvers];

      pendingInputs = [];
      pendingResolvers = [];
      timeoutId = null;

      try {
        const results = await batchQueryFn(inputs);
        resolvers.forEach((resolve) => resolve(results));
      } catch (error) {
        // Handle error - could reject all pending promises
        console.error("Batch query failed:", error);
      }
    };

    return (input: TInput): Promise<TOutput[]> => {
      return new Promise((resolve) => {
        pendingInputs.push(input);
        pendingResolvers.push(resolve);

        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        timeoutId = setTimeout(executeBatch, debounceMs);
      });
    };
  },
};

/**
 * Performance monitoring utilities
 */
export const queryPerformance = {
  /**
   * Measure query execution time
   */
  measureQueryTime: async <T>(
    queryName: string,
    queryFn: () => Promise<T>,
  ): Promise<{ data: T; duration: number }> => {
    const startTime = performance.now();

    try {
      const data = await queryFn();
      const duration = performance.now() - startTime;

      console.log(`Query ${queryName} completed in ${duration.toFixed(2)}ms`);

      return { data, duration };
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `Query ${queryName} failed after ${duration.toFixed(2)}ms:`,
        error,
      );
      throw error;
    }
  },

  /**
   * Track slow queries
   */
  trackSlowQueries: (threshold = 1000) => {
    const slowQueries: Array<{
      name: string;
      duration: number;
      timestamp: Date;
    }> = [];

    return {
      measureQuery: async <T>(
        name: string,
        queryFn: () => Promise<T>,
      ): Promise<T> => {
        const { data, duration } = await queryPerformance.measureQueryTime(
          name,
          queryFn,
        );

        if (duration > threshold) {
          slowQueries.push({
            name,
            duration,
            timestamp: new Date(),
          });
        }

        return data;
      },

      getSlowQueries: () => [...slowQueries],

      clearSlowQueries: () => {
        slowQueries.length = 0;
      },
    };
  },
};

/**
 * Query result transformation utilities
 */
export const queryTransforms = {
  /**
   * Transform and combine multiple query results
   */
  combineAndTransform: <TInputs extends Record<string, any>, TOutput>(
    queries: {
      [K in keyof TInputs]: {
        data: TInputs[K] | undefined;
        isLoading: boolean;
        error: Error | null;
      };
    },
    transformer: (data: TInputs) => TOutput,
  ): {
    data: TOutput | null;
    isLoading: boolean;
    error: Error | null;
  } => {
    const isLoading = queryStateUtils.aggregateLoadingStates(
      ...Object.values(queries),
    );
    const error = queryStateUtils.aggregateErrorStates(
      ...Object.values(queries),
    );
    // Filter out queries with undefined data for combineQueryData
    const queriesWithData = Object.fromEntries(
      Object.entries(queries).filter(([, query]) => query.data !== undefined),
    ) as { [K in keyof TInputs]: { data: TInputs[K] } };

    const combinedData =
      Object.keys(queries).length === Object.keys(queriesWithData).length
        ? queryStateUtils.combineQueryData(queriesWithData)
        : null;

    return {
      data: combinedData ? transformer(combinedData) : null,
      isLoading,
      error,
    };
  },

  /**
   * Create a derived query that depends on multiple sources
   */
  createDerivedQuery: <TInputs extends Record<string, any>, TOutput>(
    sourceQueries: {
      [K in keyof TInputs]: () => {
        data: TInputs[K] | undefined;
        isLoading: boolean;
        error: Error | null;
      };
    },
    transformer: (data: TInputs) => TOutput,
    options?: {
      enabled?: boolean;
      refetchInterval?: number;
    },
  ) => {
    return () => {
      const queries = Object.fromEntries(
        Object.entries(sourceQueries).map(([key, queryFn]) => [key, queryFn()]),
      ) as {
        [K in keyof TInputs]: {
          data: TInputs[K] | undefined;
          isLoading: boolean;
          error: Error | null;
        };
      };

      return queryTransforms.combineAndTransform(queries, transformer);
    };
  },
};
