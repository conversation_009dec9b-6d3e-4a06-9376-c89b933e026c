
import { ExternalErrors, } from "../core/errors";

// ============================================================================
// RETRY MECHANISMS
// ============================================================================

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors?: string[];
  onRetry?: (attempt: number, error: Error) => void;
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
  retryableErrors: [
    "INTERNAL_SERVER_ERROR",
    "SERVICE_UNAVAILABLE",
    "TIMEOUT",
    "ECONNREFUSED",
    "ENOTFOUND",
    "NETWORK_ERROR",
  ],
};

/**
 * Retry an operation with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: Error;

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if error is retryable
      if (!isRetryableError(lastError, finalConfig.retryableErrors)) {
        throw lastError;
      }

      // Don't retry on last attempt
      if (attempt === finalConfig.maxAttempts) {
        throw lastError;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        finalConfig.baseDelay * finalConfig.backoffMultiplier ** (attempt - 1),
        finalConfig.maxDelay
      );

      // Call retry callback if provided
      finalConfig.onRetry?.(attempt, lastError);

      // Wait before retrying
      await sleep(delay);
    }
  }

  // biome-ignore lint/style/noNonNullAssertion: Augment generated
  throw lastError!;
}

/**
 * Check if an error is retryable
 */
function isRetryableError(error: Error, retryableErrors?: string[]): boolean {
  if (!retryableErrors) return false;

  const errorMessage = error.message.toLowerCase();
  const errorName = error.name.toLowerCase();

  return retryableErrors.some(retryableError =>
    errorMessage.includes(retryableError.toLowerCase()) ||
    errorName.includes(retryableError.toLowerCase())
  );
}

/**
 * Sleep utility
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// CIRCUIT BREAKER PATTERN
// ============================================================================

/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = "CLOSED",
  OPEN = "OPEN",
  HALF_OPEN = "HALF_OPEN",
}

/**
 * Circuit breaker configuration
 */
export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
}

/**
 * Circuit breaker implementation
 */
export class CircuitBreaker {
  private state = CircuitBreakerState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private halfOpenCalls = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.config.recoveryTimeout) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.halfOpenCalls = 0;
      } else {
        throw ExternalErrors.serviceUnavailable("Circuit breaker is OPEN");
      }
    }

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      if (this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
        throw ExternalErrors.serviceUnavailable("Circuit breaker is HALF_OPEN with max calls reached");
      }
      this.halfOpenCalls++;
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.state = CircuitBreakerState.CLOSED;
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getStats() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      halfOpenCalls: this.halfOpenCalls,
    };
  }
}

// ============================================================================
// FALLBACK STRATEGIES
// ============================================================================

/**
 * Fallback configuration
 */
export interface FallbackConfig<T> {
  fallbackValue?: T;
  fallbackFunction?: () => Promise<T>;
  shouldFallback?: (error: Error) => boolean;
  onFallback?: (error: Error) => void;
}

/**
 * Execute operation with fallback
 */
export async function withFallback<T>(
  operation: () => Promise<T>,
  config: FallbackConfig<T>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));

    // Check if we should use fallback
    if (config.shouldFallback && !config.shouldFallback(err)) {
      throw error;
    }

    // Call fallback callback
    config.onFallback?.(err);

    // Use fallback function if provided
    if (config.fallbackFunction) {
      try {
        return await config.fallbackFunction();
      } catch {
        // If fallback also fails, throw original error
        throw error;
      }
    }

    // Use fallback value if provided
    if (config.fallbackValue !== undefined) {
      return config.fallbackValue;
    }

    // No fallback available, re-throw original error
    throw error;
  }
}

// ============================================================================
// GRACEFUL DEGRADATION
// ============================================================================

/**
 * Degradation levels
 */
export enum DegradationLevel {
  FULL = "FULL",
  PARTIAL = "PARTIAL",
  MINIMAL = "MINIMAL",
  NONE = "NONE",
}

/**
 * Service degradation manager
 */
export class ServiceDegradationManager {
  private degradationLevel = DegradationLevel.FULL;
  private degradedServices = new Set<string>();

  setDegradationLevel(level: DegradationLevel): void {
    this.degradationLevel = level;
  }

  degradeService(serviceName: string): void {
    this.degradedServices.add(serviceName);
  }

  restoreService(serviceName: string): void {
    this.degradedServices.delete(serviceName);
  }

  isServiceDegraded(serviceName: string): boolean {
    return this.degradedServices.has(serviceName);
  }

  getDegradationLevel(): DegradationLevel {
    return this.degradationLevel;
  }

  shouldSkipNonEssential(): boolean {
    return this.degradationLevel === DegradationLevel.MINIMAL ||
           this.degradationLevel === DegradationLevel.NONE;
  }

  shouldUseCache(): boolean {
    return this.degradationLevel !== DegradationLevel.FULL;
  }
}

// ============================================================================
// COMPOSITE ERROR RECOVERY
// ============================================================================

/**
 * Complete error recovery configuration
 */
export interface ErrorRecoveryConfig<T> extends RetryConfig, FallbackConfig<T> {
  circuitBreaker?: CircuitBreakerConfig;
  enableDegradation?: boolean;
}

/**
 * Execute operation with complete error recovery
 */
export async function withErrorRecovery<T>(
  operation: () => Promise<T>,
  config: ErrorRecoveryConfig<T> = {
    maxAttempts: 0,
    baseDelay: 0,
    maxDelay: 0,
    backoffMultiplier: 0
  }
): Promise<T> {
  let finalOperation = operation;

  // Wrap with circuit breaker if configured
  if (config.circuitBreaker) {
    const circuitBreaker = new CircuitBreaker(config.circuitBreaker);
    finalOperation = () => circuitBreaker.execute(operation);
  }

  // Wrap with retry logic
  const retryOperation = () => withRetry(finalOperation, config);

  // Wrap with fallback
  return withFallback(retryOperation, config);
}

// Global service degradation manager instance
export const serviceDegradationManager = new ServiceDegradationManager();
