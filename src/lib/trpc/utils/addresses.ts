import { eq, type InferInsertModel } from "drizzle-orm";
import type { Db, Tx } from "@/db";
import { address } from "@/db/schema";

export async function createAddress(
  addressData: InferInsertModel<typeof address>,
  db: Db | Tx,
) {
  const [result] = await db.insert(address).values(addressData).returning();

  return result;
}

export async function updateAddress(
  id: string,
  addressData: InferInsertModel<typeof address>,
  db: Db | Tx,
) {
  const [result] = await db
    .update(address)
    .set(addressData)
    .where(eq(address.id, id))
    .returning();

  return result;
}
