import { sql } from "drizzle-orm";
import { db } from "@/db";

export type Location = {
  x: number;
  y: number;
};

export async function calculateDistance(
  from: Location,
  to: Location,
): Promise<number | undefined> {
  try {
    // Calculate distance using PostGIS
    const query = sql`
      SELECT
        ST_Distance(
          ST_SetSRID(ST_MakePoint(${from.y}, ${from.x}), 4326)::geography,
          ST_SetSRID(ST_MakePoint(${to.y}, ${to.x}), 4326)::geography
        ) / 1609.34 as distance
    `;

    const distanceResult = await db.execute(query);

    if (Array.isArray(distanceResult) && distanceResult[0]?.distance) {
      return distanceResult[0].distance;
    }

    return undefined;
  } catch (error) {
    console.error("Error calculating distance:", error);
    return undefined;
  }
}
