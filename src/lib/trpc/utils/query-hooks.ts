"use client";

import { useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useTRPC } from "@/components/integrations/trpc/client";
import { cacheInvalidation } from "./query-optimization";

/**
 * Enhanced query hooks with optimizations
 */
export const useOptimizedQueries = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  return {
    /**
     * Aggregate loading states from multiple queries
     */
    aggregateLoadingStates: (...queries: Array<{ isLoading: boolean }>) => {
      return queries.some((query) => query.isLoading);
    },

    /**
     * Aggregate error states from multiple queries
     */
    aggregateErrorStates: (...queries: Array<{ error: Error | null }>) => {
      return queries.find((query) => query.error)?.error || null;
    },

    /**
     * Check if all queries have data
     */
    allQueriesHaveData: (...queries: Array<{ data: unknown }>) => {
      return queries.every((query) => query.data !== undefined);
    },

    /**
     * Invalidate related queries for a job
     */
    invalidateJobRelated: (jobId: string, propertyId?: string) => {
      return cacheInvalidation.invalidateJobRelated(
        queryClient,
        jobId,
        propertyId,
      );
    },

    /**
     * Invalidate related queries for a bid
     */
    invalidateBidRelated: (bidId: string, jobId?: string) => {
      return cacheInvalidation.invalidateBidRelated(queryClient, bidId, jobId);
    },

    /**
     * Invalidate organization-related queries
     */
    invalidateOrganizationRelated: (organizationId: string) => {
      return cacheInvalidation.invalidateOrganizationRelated(
        queryClient,
        organizationId,
      );
    },

    /**
     * Invalidate message-related queries
     */
    invalidateMessageRelated: ({
      bidId,
      jobId,
    }: {
      bidId?: string;
      jobId?: string;
    }) => {
      return cacheInvalidation.invalidateMessageRelated(queryClient, {
        bidId,
        jobId,
      });
    },
  };
};

/**
 * Performance monitoring hooks
 */
export const useQueryPerformance = () => {
  const [slowQueries, setSlowQueries] = React.useState<
    Array<{
      name: string;
      duration: number;
      timestamp: Date;
    }>
  >([]);

  const trackQuery = React.useCallback((name: string, duration: number) => {
    if (duration > 1000) {
      // Track queries slower than 1 second
      setSlowQueries((prev) => [
        ...prev,
        { name, duration, timestamp: new Date() },
      ]);
    }
  }, []);

  const clearSlowQueries = React.useCallback(() => {
    setSlowQueries([]);
  }, []);

  return {
    slowQueries,
    trackQuery,
    clearSlowQueries,
  };
};

/**
 * Utility hooks for common query patterns
 */
export const useQueryUtils = () => {
  const queryClient = useQueryClient();

  return {
    /**
     * Invalidate multiple query keys at once
     */
    invalidateMultiple: async (queryKeys: Array<unknown[]>) => {
      const promises = queryKeys.map((queryKey) =>
        queryClient.invalidateQueries({ queryKey }),
      );
      await Promise.allSettled(promises);
    },

    /**
     * Prefetch multiple queries
     */
    prefetchMultiple: async (
      queries: Array<{ queryKey: unknown[]; queryFn: () => Promise<unknown> }>,
    ) => {
      const promises = queries.map(({ queryKey, queryFn }) =>
        queryClient.prefetchQuery({ queryKey, queryFn }),
      );
      await Promise.allSettled(promises);
    },

    /**
     * Get cached data for a query
     */
    getCachedData: <T>(queryKey: unknown[]): T | undefined => {
      return queryClient.getQueryData<T>(queryKey);
    },

    /**
     * Set cached data for a query
     */
    setCachedData: <T>(queryKey: unknown[], data: T) => {
      queryClient.setQueryData(queryKey, data);
    },

    /**
     * Remove cached data for a query
     */
    removeCachedData: (queryKey: unknown[]) => {
      queryClient.removeQueries({ queryKey });
    },
  };
};
