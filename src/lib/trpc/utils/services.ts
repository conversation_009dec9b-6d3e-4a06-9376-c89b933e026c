import { type InferInsertModel, sql } from "drizzle-orm";
import type { Db, Tx } from "@/db";
import { service } from "@/db/schema";

export async function createServices(
  organizationId: string,
  serviceList: Omit<InferInsertModel<typeof service>, "organizationId">[],
  db: Db | Tx,
) {
  const createdServices = await db
    .insert(service)
    .values(
      serviceList.map((service) => ({
        ...service,
        organizationId,
      })),
    )
    .onConflictDoUpdate({
      target: [service.name, service.organizationId],
      set: {
        description: sql`excluded.description`,
        price: sql`excluded.price`,
        duration: sql`excluded.duration`,
      },
    })
    .returning();

  return createdServices;
}
