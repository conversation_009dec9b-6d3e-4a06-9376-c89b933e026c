import { sql } from "drizzle-orm";
import type { Db } from "@/db";

/**
 * Query Performance Monitoring and Analysis
 *
 * This module provides utilities to monitor and analyze database query performance.
 * It helps identify slow queries, unused indexes, and optimization opportunities.
 */

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Monitor query execution time and log slow queries
 */
export function withQueryPerformanceMonitoring<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  slowQueryThreshold = 1000, // ms
): Promise<T> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    try {
      const result = queryFn();
      const executionTime = Date.now() - startTime;

      if (executionTime > slowQueryThreshold) {
        console.warn(`🐌 Slow Query Detected: ${queryName}`, {
          executionTime: `${executionTime}ms`,
          threshold: `${slowQueryThreshold}ms`,
          timestamp: new Date().toISOString(),
        });
      } else {
        console.log(`⚡ Query Executed: ${queryName} (${executionTime}ms)`);
      }

      resolve(result);
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ Query Failed: ${queryName}`, {
        executionTime: `${executionTime}ms`,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
      reject(error);
    }
  });
}

/**
 * Batch query performance monitoring
 */
export async function withBatchQueryMonitoring<T>(
  queries: Array<{ name: string; fn: () => Promise<any> }>,
  options: {
    parallel?: boolean;
    slowQueryThreshold?: number;
  } = {},
): Promise<T[]> {
  const { parallel = false, slowQueryThreshold = 1000 } = options;

  if (parallel) {
    return Promise.all(
      queries.map(({ name, fn }) =>
        withQueryPerformanceMonitoring(name, fn, slowQueryThreshold),
      ),
    );
  }
  const results = [];
  for (const { name, fn } of queries) {
    const result = await withQueryPerformanceMonitoring(
      name,
      fn,
      slowQueryThreshold,
    );
    results.push(result);
  }
  return results;
}

// ============================================================================
// DATABASE PERFORMANCE ANALYSIS
// ============================================================================

/**
 * Get database performance statistics
 */
export async function getDatabasePerformanceStats(db: Db) {
  const result = await db.execute(sql`
    SELECT
      -- Connection stats
      (SELECT count(*) FROM pg_stat_activity) as active_connections,
      (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_queries,

      -- Cache hit ratio
      (SELECT
        round(
          (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 2
        ) as cache_hit_ratio
       FROM pg_statio_user_tables
      ) as cache_hit_ratio,

      -- Database size
      (SELECT pg_size_pretty(pg_database_size(current_database()))) as database_size,

      -- Index usage
      (SELECT
        round(
          (sum(idx_blks_hit) / (sum(idx_blks_hit) + sum(idx_blks_read))) * 100, 2
        ) as index_hit_ratio
       FROM pg_statio_user_indexes
      ) as index_hit_ratio
  `);

  return result.rows?.[0] || result;
}

/**
 * Get slow query statistics
 */
export async function getSlowQueryStats(db: Db) {
  return db.execute(sql`
    SELECT
      query,
      calls,
      total_time,
      mean_time,
      rows,
      100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements
    WHERE query NOT LIKE '%pg_stat_statements%'
    ORDER BY total_time DESC
    LIMIT 10
  `);
}

/**
 * Get index usage statistics
 */
export async function getIndexUsageStats(db: Db) {
  return db.execute(sql`
    SELECT
      schemaname,
      tablename,
      indexname,
      idx_scan,
      idx_tup_read,
      idx_tup_fetch,
      pg_size_pretty(pg_relation_size(indexrelid)) as index_size
    FROM pg_stat_user_indexes
    ORDER BY idx_scan DESC
  `);
}

/**
 * Find unused indexes
 */
export async function getUnusedIndexes(db: Db) {
  return db.execute(sql`
    SELECT
      schemaname,
      tablename,
      indexname,
      pg_size_pretty(pg_relation_size(indexrelid)) as index_size
    FROM pg_stat_user_indexes
    WHERE idx_scan = 0
      AND indexname NOT LIKE '%_pkey'  -- Exclude primary keys
      AND indexname NOT LIKE '%_key'   -- Exclude unique constraints
    ORDER BY pg_relation_size(indexrelid) DESC
  `);
}

/**
 * Get table statistics
 */
export async function getTableStats(db: Db) {
  return db.execute(sql`
    SELECT
      schemaname,
      tablename,
      n_tup_ins as inserts,
      n_tup_upd as updates,
      n_tup_del as deletes,
      n_live_tup as live_tuples,
      n_dead_tup as dead_tuples,
      pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
      pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
      last_vacuum,
      last_autovacuum,
      last_analyze,
      last_autoanalyze
    FROM pg_stat_user_tables
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
  `);
}

// ============================================================================
// QUERY OPTIMIZATION HELPERS
// ============================================================================

/**
 * Analyze query execution plan
 */
export async function analyzeQuery(db: Db, query: string) {
  const plan = await db.execute(
    sql.raw(`EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`),
  );
  return plan;
}

/**
 * Get query execution plan without executing
 */
export async function explainQuery(db: Db, query: string) {
  const plan = await db.execute(sql.raw(`EXPLAIN (FORMAT JSON) ${query}`));
  return plan;
}

/**
 * Check if indexes are being used effectively
 */
export async function checkIndexEffectiveness(db: Db, tableName: string) {
  return db.execute(sql`
    SELECT
      indexname,
      idx_scan,
      idx_tup_read,
      idx_tup_fetch,
      CASE
        WHEN idx_scan = 0 THEN 'Never used'
        WHEN idx_tup_read = 0 THEN 'Index only scans'
        ELSE 'Used effectively'
      END as usage_status,
      pg_size_pretty(pg_relation_size(indexrelid)) as size
    FROM pg_stat_user_indexes
    WHERE tablename = ${tableName}
    ORDER BY idx_scan DESC
  `);
}

// ============================================================================
// PERFORMANCE RECOMMENDATIONS
// ============================================================================

/**
 * Generate performance recommendations based on database statistics
 */
export async function generatePerformanceRecommendations(db: Db) {
  const recommendations = [];

  // Check cache hit ratio
  const cacheResult = await db.execute(sql`
    SELECT
      round((sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 2) as cache_hit_ratio
    FROM pg_statio_user_tables
  `);
  const cacheStats = (cacheResult.rows?.[0] || cacheResult) as any;

  if (cacheStats.cache_hit_ratio < 95) {
    recommendations.push({
      type: "cache",
      severity: "high",
      message: `Cache hit ratio is ${cacheStats.cache_hit_ratio}%. Consider increasing shared_buffers.`,
    });
  }

  // Check for unused indexes
  const unusedIndexesResult = await getUnusedIndexes(db);
  const unusedIndexes = (unusedIndexesResult.rows ||
    unusedIndexesResult) as any[];
  if (unusedIndexes.length > 0) {
    recommendations.push({
      type: "indexes",
      severity: "medium",
      message: `Found ${unusedIndexes.length} unused indexes. Consider dropping them to save space.`,
      details: unusedIndexes,
    });
  }

  // Check for tables that need vacuuming
  const vacuumResult = await db.execute(sql`
    SELECT count(*) as tables_needing_vacuum
    FROM pg_stat_user_tables
    WHERE n_dead_tup > n_live_tup * 0.1
      AND n_dead_tup > 1000
  `);
  const vacuumStats = (vacuumResult.rows?.[0] || vacuumResult) as any;

  if (vacuumStats.tables_needing_vacuum > 0) {
    recommendations.push({
      type: "maintenance",
      severity: "medium",
      message: `${vacuumStats.tables_needing_vacuum} tables have high dead tuple ratios. Consider running VACUUM.`,
    });
  }

  return recommendations;
}

// ============================================================================
// QUERY CACHING UTILITIES
// ============================================================================

/**
 * Simple in-memory query result cache
 */
class QueryCache {
  private cache = new Map<
    string,
    { data: any; timestamp: number; ttl: number }
  >();

  set(key: string, data: any, ttl = 300000) {
    // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string) {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

export const queryCache = new QueryCache();

/**
 * Cached query execution
 */
export async function withQueryCache<T>(
  cacheKey: string,
  queryFn: () => Promise<T>,
  ttl = 300000, // 5 minutes
): Promise<T> {
  const cached = queryCache.get(cacheKey);
  if (cached) {
    console.log(`📦 Cache Hit: ${cacheKey}`);
    return cached;
  }

  console.log(`🔍 Cache Miss: ${cacheKey}`);
  const result = await queryFn();
  queryCache.set(cacheKey, result, ttl);

  return result;
}
