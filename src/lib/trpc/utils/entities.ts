import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import {
  address,
  bid,
  chat,
  job,
  organization,
  property,
  trade,
} from "@/db/schema";

export async function findOrCreateChat({
  bidId,
  jobId,
}: {
  bidId?: string;
  jobId?: string;
}) {
  if (!bidId && !jobId) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Either bidId or jobId must be provided",
    });
  }

  let result = null;

  if (bidId) {
    result = await db.query.chat.findFirst({
      where: eq(chat.bidId, bidId),
    });

    if (!result) {
      [result] = await db
        .insert(chat)
        .values({
          bidId,
        })
        .returning();
    }
  } else if (jobId) {
    result = await db.query.chat.findFirst({
      where: eq(chat.jobId, jobId),
    });

    if (!result) {
      [result] = await db.insert(chat).values({ jobId }).returning();
    }
  }

  return result;
}

export async function getBidWithRelations(bidId: string) {
  // Use explicit select to avoid PostGIS serialization issues
  const [result] = await db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,
      updatedAt: bid.updatedAt,
      jobId: bid.jobId,
      organizationId: bid.organizationId,
      name: bid.name,
      description: bid.description,
      estimatedDuration: bid.estimatedDuration,

      // Job data
      job: {
        id: job.id,
        name: job.name,
        budget: job.budget,
        status: job.status,
        createdAt: job.createdAt,
        propertyId: job.propertyId,
      },

      // Property data
      property: {
        id: property.id,
        name: property.name,
        imageUrl: property.imageUrl,
        userId: property.userId,
        addressId: property.addressId,
      },

      // Address data (excluding PostGIS location column)
      address: {
        id: address.id,
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zip,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt,
        location: address.location,
      },

      // Organization data
      organization: {
        id: organization.id,
        name: organization.name,
        description: organization.description,
        email: organization.email,
        phone: organization.phone,
      },

      // Organization address data (excluding PostGIS location column)
      organizationAddress: {
        id: address.id,
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zip,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt,
        location: address.location,
      },

      // Trade data
      trade: {
        id: trade.id,
        name: trade.name,
      },
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .innerJoin(organization, eq(bid.organizationId, organization.id))
    .innerJoin(trade, eq(organization.tradeId, trade.id))
    .innerJoin(address, eq(organization.addressId, address.id))
    .where(eq(bid.id, bidId))
    .limit(1);

  if (!result) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Bid not found",
    });
  }

  // Reconstruct the nested structure
  return {
    ...result,
    job: {
      ...result.job,
      property: {
        ...result.property,
        address: result.address,
      },
    },
    organization: {
      ...result.organization,
      trade: result.trade,
      address: result.organizationAddress,
    },
  };
}

export async function getJobWithRelations(jobId: string) {
  // Use explicit select to avoid PostGIS serialization issues
  const [result] = await db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      propertyId: job.propertyId,
      startsAt: job.startsAt,
      deadline: job.deadline,
      completedAt: job.completedAt,
      contractorCompleted: job.contractorCompleted,
      homeownerCompleted: job.homeownerCompleted,
      isRecurring: job.isRecurring,
      jobType: job.jobType,
      recurringFrequency: job.recurringFrequency,
      taskBids: job.taskBids,

      // Property data
      property: {
        id: property.id,
        name: property.name,
        imageUrl: property.imageUrl,
        userId: property.userId,
        addressId: property.addressId,
        createdAt: property.createdAt,
        updatedAt: property.updatedAt,
      },

      // Address data (excluding PostGIS location column)
      address: {
        id: address.id,
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zip,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt,
      },
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(eq(job.id, jobId))
    .limit(1);

  if (!result) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Job not found",
    });
  }

  // Reconstruct the nested structure
  return {
    ...result,
    property: {
      ...result.property,
      address: result.address,
    },
  };
}
