import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import type { Context } from "../core/context";

// Legacy imports for backward compatibility
import {
  bidMiddlewares,
  createPermissionMiddleware,
  jobMiddlewares,
  messageMiddlewares,
  organizationMiddlewares,
  propertyMiddlewares,
  requireAdmin,
  requireContractor,
  requireHomeowner,
  requireOrganizationAdmin,
  requireOrganizationOwner,
  requireOrganizationRole,
  requireRole,
  reviewMiddlewares,
  scheduleMiddlewares,
  templateMiddlewares,
} from "../middleware/permissions";
import type {
  OrganizationRole,
  PermissionAction,
  PermissionContext,
  ResourceType,
  UserRole,
} from "../types/permissions";

// ============================================================================
// TRPC INITIALIZATION
// ============================================================================

const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// BASE MIDDLEWARES
// ============================================================================

/**
 * Authentication middleware
 */
const isAuthed = t.middleware(({ next, ctx }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

// ============================================================================
// BASE PROCEDURES
// ============================================================================

// Export better-auth procedures as primary exports
export { protectedProcedure, publicProcedure, router } from "./better-auth";

// Legacy exports for backward compatibility
export const legacyRouter = t.router;
export const legacyPublicProcedure = t.procedure;
export const legacyProtectedProcedure = t.procedure.use(isAuthed);

// ============================================================================
// ROLE-BASED PROCEDURES
// ============================================================================

// Export better-auth role procedures as primary exports
export {
  adminProcedure,
  contractorProcedure,
  homeownerProcedure,
} from "./better-auth";

// Legacy role procedures for backward compatibility
export const legacyAdminProcedure = legacyProtectedProcedure.use(requireAdmin);
export const legacyContractorProcedure =
  legacyProtectedProcedure.use(requireContractor);
export const legacyHomeownerProcedure =
  legacyProtectedProcedure.use(requireHomeowner);

// ============================================================================
// ORGANIZATION ROLE PROCEDURES
// ============================================================================

export const organizationOwnerProcedure = legacyProtectedProcedure.use(
  requireOrganizationOwner,
);
export const organizationAdminProcedure = legacyProtectedProcedure.use(
  requireOrganizationAdmin,
);

// ============================================================================
// PERMISSION-BASED PROCEDURE BUILDERS
// ============================================================================

/**
 * Create a procedure with specific permission requirements (legacy)
 */
export function createLegacyPermissionProcedureOld(
  resource: ResourceType,
  action: PermissionAction,
  options?: {
    requireOwnership?: boolean;
    allowSameOrganization?: boolean;
    customCheck?: (ctx: PermissionContext) => Promise<boolean> | boolean;
  },
) {
  return legacyProtectedProcedure.use(
    createPermissionMiddleware({
      resource,
      action,
      ...options,
    }),
  );
}

/**
 * Create a procedure that requires specific user role(s)
 */
export function createRoleProcedure(requiredRole: UserRole | UserRole[]) {
  return legacyProtectedProcedure.use(requireRole(requiredRole));
}

/**
 * Create a procedure that requires specific organization role(s)
 */
export function createOrganizationRoleProcedure(
  requiredRole: OrganizationRole | OrganizationRole[],
) {
  return legacyProtectedProcedure.use(requireOrganizationRole(requiredRole));
}

// ============================================================================
// RESOURCE-SPECIFIC PROCEDURES
// ============================================================================

/**
 * Job-related procedures with better-auth permissions
 */
export { jobProcedures } from "./better-auth";

/**
 * Legacy job-related procedures
 */
export const legacyJobProcedures = {
  create: legacyProtectedProcedure.use(jobMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(jobMiddlewares.canRead),
  update: legacyProtectedProcedure.use(jobMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(jobMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(jobMiddlewares.canList),
  manage: legacyProtectedProcedure.use(jobMiddlewares.canManage),
};

/**
 * Bid-related procedures with better-auth permissions
 */
export { bidProcedures } from "./better-auth";

/**
 * Legacy bid-related procedures
 */
export const legacyBidProcedures = {
  create: legacyProtectedProcedure.use(bidMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(bidMiddlewares.canRead),
  update: legacyProtectedProcedure.use(bidMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(bidMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(bidMiddlewares.canList),
  approve: legacyProtectedProcedure.use(bidMiddlewares.canApprove),
  reject: legacyProtectedProcedure.use(bidMiddlewares.canReject),
};

/**
 * Property-related procedures with better-auth permissions
 */
export { propertyProcedures } from "./better-auth";

/**
 * Legacy property-related procedures
 */
export const legacyPropertyProcedures = {
  create: legacyProtectedProcedure.use(propertyMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(propertyMiddlewares.canRead),
  update: legacyProtectedProcedure.use(propertyMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(propertyMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(propertyMiddlewares.canList),
};

/**
 * Organization-related procedures with better-auth permissions
 */
export { organizationProcedures } from "./better-auth";

/**
 * Legacy organization-related procedures
 */
export const legacyOrganizationProcedures = {
  create: legacyProtectedProcedure.use(organizationMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(organizationMiddlewares.canRead),
  update: legacyProtectedProcedure.use(organizationMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(organizationMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(organizationMiddlewares.canList),
  manage: legacyProtectedProcedure.use(organizationMiddlewares.canManage),
  invite: legacyProtectedProcedure.use(organizationMiddlewares.canInvite),
};

/**
 * Message-related procedures with better-auth permissions
 */
export { messageProcedures } from "./better-auth";

/**
 * Legacy message-related procedures
 */
export const legacyMessageProcedures = {
  create: legacyProtectedProcedure.use(messageMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(messageMiddlewares.canRead),
  list: legacyProtectedProcedure.use(messageMiddlewares.canList),
};

/**
 * Review-related procedures with better-auth permissions
 */
export { reviewProcedures } from "./better-auth";

/**
 * Legacy review-related procedures
 */
export const legacyReviewProcedures = {
  create: legacyProtectedProcedure.use(reviewMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(reviewMiddlewares.canRead),
  list: legacyProtectedProcedure.use(reviewMiddlewares.canList),
};

/**
 * Schedule-related procedures with better-auth permissions
 */
export { scheduleProcedures } from "./better-auth";

/**
 * Legacy schedule-related procedures
 */
export const legacyScheduleProcedures = {
  create: legacyProtectedProcedure.use(scheduleMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(scheduleMiddlewares.canRead),
  update: legacyProtectedProcedure.use(scheduleMiddlewares.canUpdate),
  list: legacyProtectedProcedure.use(scheduleMiddlewares.canList),
  approve: legacyProtectedProcedure.use(scheduleMiddlewares.canApprove),
};

/**
 * Template-related procedures with better-auth permissions
 */
export { templateProcedures } from "./better-auth";

/**
 * Legacy template-related procedures
 */
export const legacyTemplateProcedures = {
  create: legacyProtectedProcedure.use(templateMiddlewares.canCreate),
  read: legacyProtectedProcedure.use(templateMiddlewares.canRead),
  update: legacyProtectedProcedure.use(templateMiddlewares.canUpdate),
  delete: legacyProtectedProcedure.use(templateMiddlewares.canDelete),
  list: legacyProtectedProcedure.use(templateMiddlewares.canList),
};

/**
 * Trade-related procedures with better-auth permissions
 */
export { tradeProcedures } from "./better-auth";

/**
 * Legacy trade-related procedures
 */
export const legacyTradeProcedures = {
  create: legacyAdminProcedure,
  read: legacyProtectedProcedure,
  update: legacyAdminProcedure,
  delete: legacyAdminProcedure,
  list: legacyProtectedProcedure,
};

// ============================================================================
// UTILITY PROCEDURES WITH BETTER-AUTH
// ============================================================================

/**
 * Export better-auth utility procedures as primary exports
 */
export {
  createCRUDProcedures,
  createPermissionProcedure,
  createReadOnlyProcedures,
} from "./better-auth";

/**
 * Legacy CRUD procedure set for a resource
 */
export function createLegacyCRUDProcedures(resource: ResourceType) {
  return {
    create: createLegacyPermissionProcedure(resource, "create"),
    read: createLegacyPermissionProcedure(resource, "read"),
    update: createLegacyPermissionProcedure(resource, "update", {
      requireOwnership: true,
    }),
    delete: createLegacyPermissionProcedure(resource, "delete", {
      requireOwnership: true,
    }),
    list: createLegacyPermissionProcedure(resource, "list"),
  };
}

/**
 * Legacy read-only procedure set for a resource
 */
export function createLegacyReadOnlyProcedures(resource: ResourceType) {
  return {
    read: createLegacyPermissionProcedure(resource, "read"),
    list: createLegacyPermissionProcedure(resource, "list"),
  };
}

/**
 * Legacy permission procedure
 */
export function createLegacyPermissionProcedure(
  resource: ResourceType,
  action: PermissionAction,
  options?: {
    requireOwnership?: boolean;
    customCheck?: (ctx: PermissionContext) => Promise<boolean> | boolean;
  },
) {
  return legacyProtectedProcedure.use(
    createPermissionMiddleware({
      resource,
      action,
      ...options,
    }),
  );
}

/**
 * Create an admin-only procedure set for a resource
 */
export function createAdminProcedures() {
  return {
    create: legacyAdminProcedure,
    read: legacyAdminProcedure,
    update: legacyAdminProcedure,
    delete: legacyAdminProcedure,
    list: legacyAdminProcedure,
    manage: legacyAdminProcedure,
  };
}

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================

/**
 * Legacy admin procedure (for backward compatibility)
 */
export const adminProcedureLegacy = legacyAdminProcedure;

/**
 * Legacy protected procedure (for backward compatibility)
 */
export const protectedProcedureLegacy = legacyProtectedProcedure;
