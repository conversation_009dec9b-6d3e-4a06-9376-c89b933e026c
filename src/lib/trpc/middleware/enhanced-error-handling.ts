import { initTR<PERSON>, TRPCError } from "@trpc/server";
import superjson from "superjson";
import { ZodError } from "zod";
import type { Context } from "../core/context";
import {
  AuthErrors,
  ERROR_SEVERITY,
  handleDatabaseError,
  handleExternalServiceError,
  handleValidationError,
  safeErrorHandler,
} from "../core/errors";
import { serviceDegradationManager, withErrorRecovery } from "../utils/error-recovery";

// Initialize tRPC for middleware creation
const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// ENHANCED ERROR HANDLING MIDDLEWARE
// ============================================================================

/**
 * Global error handling middleware with recovery capabilities
 */
export const errorHandlingMiddleware = t.middleware(async ({ next, path, type }) => {
  try {
    return await next();
  } catch (error) {
    // Handle different types of errors with appropriate transformations
    if (error instanceof ZodError) {
      throw handleValidationError(error);
    }

    if (error instanceof TRPCError) {
      // Already a TRPC error, just re-throw
      throw error;
    }

    if (error instanceof Error) {
      // Check for database errors
      if (isDatabaseError(error)) {
        throw handleDatabaseError(error, `${type} ${path}`);
      }

      // Check for external service errors
      if (isExternalServiceError(error)) {
        throw handleExternalServiceError(error, "external service");
      }

      // Generic error handling
      const standardError = safeErrorHandler(error, `${type} ${path}`);
      throw new TRPCError({
        code: standardError.code as any,
        message: standardError.message,
        cause: error,
      });
    }

    // Unknown error type
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "An unexpected error occurred",
    });
  }
});

/**
 * Database operation error handling middleware with retry
 */
export const databaseErrorMiddleware = t.middleware(async ({ next, path, type }) => {
  return withErrorRecovery(
    async () => {
      try {
        return await next();
      } catch (error) {
        if (error instanceof Error && isDatabaseError(error)) {
          throw handleDatabaseError(error, `${type} ${path}`);
        }
        throw error;
      }
    },
    {
      maxAttempts: 2,
      baseDelay: 500,
      retryableErrors: ["DATABASE_ERROR", "CONSTRAINT_VIOLATION"],
      onRetry: (attempt, error) => {
        console.warn(`Database operation retry ${attempt} for ${type} ${path}:`, error.message);
      },
      maxDelay: 0,
      backoffMultiplier: 0
    }
  );
});

/**
 * External service error handling middleware with circuit breaker
 */
export const externalServiceErrorMiddleware = (serviceName: string) =>
  t.middleware(async ({ next, path, type }) => {
    // Check if service is degraded
    if (serviceDegradationManager.isServiceDegraded(serviceName)) {
      throw new TRPCError({
        code: "SERVICE_UNAVAILABLE",
        message: `Service ${serviceName} is currently degraded`,
      });
    }

    return withErrorRecovery(
      async () => {
        try {
          return await next();
        } catch (error) {
          if (error instanceof Error && isExternalServiceError(error)) {
            // Degrade service on repeated failures
            serviceDegradationManager.degradeService(serviceName);
            throw handleExternalServiceError(error, serviceName);
          }
          throw error;
        }
      },
      {
        maxAttempts: 3,
        baseDelay: 1000,
        retryableErrors: ["EXTERNAL_SERVICE_ERROR", "SERVICE_UNAVAILABLE", "RATE_LIMIT_EXCEEDED"],
        circuitBreaker: {
          failureThreshold: 5,
          recoveryTimeout: 30000,
          monitoringPeriod: 60000,
          halfOpenMaxCalls: 3,
        },
        onRetry: (attempt, error) => {
          console.warn(`External service retry ${attempt} for ${serviceName} in ${type} ${path}:`, error.message);
        },
        maxDelay: 0,
        backoffMultiplier: 0
      }
    );
  });

/**
 * Authentication middleware with standardized errors
 */
export const authMiddleware = t.middleware(({ next, ctx }) => {
  if (!ctx.userId) {
    throw AuthErrors.unauthorized();
  }

  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

/**
 * Error logging middleware with structured logging
 */
export const errorLoggingMiddleware = t.middleware(async ({ next, path, type }) => {
  const startTime = Date.now();
  const traceId = generateTraceId();

  try {
    const result = await next();

    // Log successful operations (optional)
    const duration = Date.now() - startTime;
    if (duration > 1000) { // Log slow operations
      console.warn(`Slow operation: ${type} ${path} took ${duration}ms`, {
        traceId,
        type,
        path,
        duration,
      });
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const standardError = safeErrorHandler(error, `${type} ${path}`);

    // Enhanced error logging with structured data
    const logMessage = {
      traceId,
      type,
      path,
      duration,
      error: {
        code: standardError.code,
        message: standardError.message,
        category: standardError.category,
        severity: standardError.severity,
        details: standardError.details,
      },
      timestamp: standardError.timestamp,
      degradationLevel: serviceDegradationManager.getDegradationLevel(),
    };

    const logLevel = getLogLevel(standardError.severity);
    switch (logLevel) {
      case "error":
        console.error("TRPC Error:", logMessage);
        break;
      case "warn":
        console.warn("TRPC Warning:", logMessage);
        break;
      default:
        console.log("TRPC Info:", logMessage);
    }

    throw error;
  }
});

// ============================================================================
// ERROR DETECTION UTILITIES
// ============================================================================

function isDatabaseError(error: Error): boolean {
  const message = error.message.toLowerCase();
  const stack = error.stack?.toLowerCase() || "";

  return (
    message.includes("database") ||
    message.includes("sql") ||
    message.includes("constraint") ||
    message.includes("foreign key") ||
    message.includes("unique") ||
    message.includes("not null") ||
    stack.includes("drizzle") ||
    stack.includes("postgres") ||
    stack.includes("sqlite") ||
    error.name.includes("Database") ||
    error.name.includes("SQL")
  );
}

function isExternalServiceError(error: Error): boolean {
  const message = error.message.toLowerCase();
  const stack = error.stack?.toLowerCase() || "";

  return (
    message.includes("fetch") ||
    message.includes("network") ||
    message.includes("timeout") ||
    message.includes("econnrefused") ||
    message.includes("enotfound") ||
    message.includes("rate limit") ||
    message.includes("429") ||
    message.includes("503") ||
    stack.includes("fetch") ||
    stack.includes("axios") ||
    stack.includes("http") ||
    error.name.includes("Fetch") ||
    error.name.includes("Network") ||
    error.name.includes("Timeout")
  );
}

function getLogLevel(severity: string): "error" | "warn" | "info" {
  switch (severity) {
    case ERROR_SEVERITY.CRITICAL:
    case ERROR_SEVERITY.HIGH:
      return "error";
    case ERROR_SEVERITY.MEDIUM:
      return "warn";
    default:
      return "info";
  }
}

function generateTraceId(): string {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// ============================================================================
// ENHANCED PROCEDURES
// ============================================================================

/**
 * Public procedure with comprehensive error handling
 */
export const publicProcedureWithErrorHandling = t.procedure
  .use(errorLoggingMiddleware)
  .use(errorHandlingMiddleware);

/**
 * Protected procedure with authentication and error handling
 */
export const protectedProcedureWithErrorHandling = t.procedure
  .use(errorLoggingMiddleware)
  .use(authMiddleware)
  .use(errorHandlingMiddleware);

/**
 * Database procedure with specialized database error handling and retry
 */
export const databaseProcedure = t.procedure
  .use(errorLoggingMiddleware)
  .use(authMiddleware)
  .use(databaseErrorMiddleware);

/**
 * External service procedure with circuit breaker and retry
 */
export const externalServiceProcedure = (serviceName: string) =>
  t.procedure
    .use(errorLoggingMiddleware)
    .use(authMiddleware)
    .use(externalServiceErrorMiddleware(serviceName));

/**
 * Complete procedure with all error handling features
 */
export const completeProcedure = t.procedure
  .use(errorLoggingMiddleware)
  .use(authMiddleware)
  .use(databaseErrorMiddleware)
  .use(errorHandlingMiddleware);

export const completePublicProcedure = t.procedure
  .use(errorLoggingMiddleware)
  .use(errorHandlingMiddleware);
