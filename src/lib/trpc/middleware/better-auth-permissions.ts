import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import type { auth } from "@/lib/auth";
import type { Context } from "../core/context";

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// BETTER-AUTH INTEGRATION TYPES
// ============================================================================

type UserRole = "admin" | "homeowner" | "contractor";

export interface BetterAuthContext {
  userId: string;
  userRole: UserRole;
  session: typeof auth.$Infer.Session | null;
}

// ============================================================================
// BETTER-AUTH SESSION MIDDLEWARE
// ============================================================================

/**
 * Enhanced authentication middleware that integrates with better-auth
 */
export const betterAuthMiddleware = t.middleware(async ({ next, ctx }) => {
  // Get session from context (should be set by the context provider)
  if (!ctx.userId || !ctx.role) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Authentication required",
    });
  }

  const userRole = ctx.role as UserRole;

  return next({
    ctx: {
      ...ctx,
      userId: ctx.userId,
      userRole,
      session: ctx.session || null,
    },
  });
});

// ============================================================================
// BETTER-AUTH PERMISSION MIDDLEWARE
// ============================================================================

/**
 * Create permission middleware using better-auth access control
 */
export function createBetterAuthPermission(
  resource: string,
  action: string,
  options?: {
    requireOwnership?: boolean;
    customCheck?: (ctx: BetterAuthContext) => Promise<boolean> | boolean;
  },
) {
  return t.middleware(async ({ next, ctx, input }) => {
    // Ensure we have auth context
    if (!ctx.userId || !ctx.role) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required",
      });
    }

    const userRole = ctx.role as UserRole;

    // Check permission using better-auth access control
    // Use a simple permission check based on role definitions
    const hasPermission = checkRolePermission(userRole, resource, action);

    if (!hasPermission) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Role '${userRole}' does not have permission to '${action}' on '${resource}'`,
      });
    }

    // Handle custom checks
    if (options?.customCheck) {
      const betterAuthCtx: BetterAuthContext = {
        userId: ctx.userId,
        userRole,
        session: null,
      };
      const allowed = await options.customCheck(betterAuthCtx);
      if (!allowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Custom permission check failed",
        });
      }
    }

    // Handle ownership checks
    if (options?.requireOwnership && input && typeof input === "object") {
      const inputObj = input as Record<string, unknown>;
      const resourceId = inputObj.id as string;

      if (resourceId) {
        const ownsResource = await checkResourceOwnership(
          ctx.userId,
          resource,
          resourceId,
        );

        if (!ownsResource && userRole !== "admin") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to access this resource",
          });
        }
      }
    }

    return next({
      ctx: {
        ...ctx,
        userId: ctx.userId,
        userRole,
      },
    });
  });
}

// ============================================================================
// ROLE-BASED MIDDLEWARE
// ============================================================================

/**
 * Require specific role(s) using better-auth
 */
export function requireBetterAuthRole(requiredRole: UserRole | UserRole[]) {
  return t.middleware(async ({ next, ctx }) => {
    if (!ctx.userId || !ctx.role) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required",
      });
    }

    const userRole = ctx.role as UserRole;
    const allowedRoles = Array.isArray(requiredRole)
      ? requiredRole
      : [requiredRole];

    if (!allowedRoles.includes(userRole)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Required role: ${allowedRoles.join(" or ")}. Current role: ${userRole}`,
      });
    }

    return next();
  });
}

// ============================================================================
// PERMISSION CHECKING UTILITIES
// ============================================================================

/**
 * Check if a role has permission for a specific resource and action
 */
function checkRolePermission(
  userRole: UserRole,
  resource: string,
  action: string,
): boolean {
  // Define role permissions based on better-auth access control
  const rolePermissions: Record<UserRole, Record<string, string[]>> = {
    admin: {
      job: [
        "create",
        "read",
        "update",
        "delete",
        "publish",
        "award",
        "complete",
      ],
      property: ["create", "read", "update", "delete"],
      bid: [
        "create",
        "read",
        "update",
        "delete",
        "accept",
        "reject",
        "withdraw",
      ],
      organization: ["create", "read", "update", "delete", "manage"],
      schedule: ["create", "read", "update", "delete", "confirm", "reschedule"],
      message: ["create", "read"],
      review: ["create", "read"],
      service: ["create", "read", "update", "delete"],
      template: ["create", "read", "update", "delete"],
      user: [
        "create",
        "read",
        "update",
        "delete",
        "ban",
        "unban",
        "impersonate",
      ],
      session: ["create", "read", "update", "delete"],
    },
    homeowner: {
      job: [
        "create",
        "read",
        "update",
        "delete",
        "publish",
        "award",
        "complete",
      ],
      property: ["create", "read", "update", "delete"],
      bid: ["read", "accept", "reject"],
      organization: ["read"],
      schedule: ["read", "confirm", "reschedule"],
      message: ["create", "read"],
      review: ["create", "read"],
    },
    contractor: {
      job: ["read"],
      property: ["read"],
      bid: ["create", "read", "update", "withdraw"],
      organization: ["create", "read", "update", "manage"],
      schedule: ["create", "read", "update", "reschedule"],
      message: ["create", "read"],
      review: ["read"],
      service: ["create", "read", "update", "delete"],
    },
  };

  const permissions = rolePermissions[userRole]?.[resource];
  return permissions ? permissions.includes(action) : false;
}

// ============================================================================
// RESOURCE OWNERSHIP UTILITIES
// ============================================================================

/**
 * Check if user owns a specific resource
 */
async function checkResourceOwnership(
  userId: string,
  resource: string,
  resourceId: string,
): Promise<boolean> {
  // Import db here to avoid circular dependencies
  const { db } = await import("@/db");
  const { eq } = await import("drizzle-orm");

  try {
    switch (resource) {
      case "job": {
        const { job } = await import("@/db/schema");
        const result = await db.query.job.findFirst({
          where: eq(job.id, resourceId),
          with: {
            property: {
              with: {
                user: true,
              },
            },
          },
        });
        return result?.property.user.id === userId;
      }

      case "property": {
        const { property } = await import("@/db/schema");
        const result = await db.query.property.findFirst({
          where: eq(property.id, resourceId),
          with: {
            user: true,
          },
        });
        return result?.user.id === userId;
      }

      case "bid": {
        const { bid } = await import("@/db/schema");
        const result = await db.query.bid.findFirst({
          where: eq(bid.id, resourceId),
          with: {
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        });
        return (
          result?.organization.memberships.some((m) => m.userId === userId) ||
          false
        );
      }

      default:
        return false;
    }
  } catch (error) {
    console.error(
      `Error checking ownership for ${resource}:${resourceId}`,
      error,
    );
    return false;
  }
}

// ============================================================================
// CONVENIENCE MIDDLEWARE EXPORTS
// ============================================================================

/**
 * Pre-configured role middleware
 */
export const requireAdmin = requireBetterAuthRole("admin");
export const requireContractor = requireBetterAuthRole("contractor");
export const requireHomeowner = requireBetterAuthRole("homeowner");

/**
 * Resource-specific permission middleware factories
 */
export const jobPermissions = {
  create: () => createBetterAuthPermission("job", "create"),
  read: () => createBetterAuthPermission("job", "read"),
  update: () =>
    createBetterAuthPermission("job", "update", { requireOwnership: true }),
  delete: () =>
    createBetterAuthPermission("job", "delete", { requireOwnership: true }),
  list: () => createBetterAuthPermission("job", "read"),
  publish: () =>
    createBetterAuthPermission("job", "publish", { requireOwnership: true }),
  award: () =>
    createBetterAuthPermission("job", "award", { requireOwnership: true }),
  complete: () =>
    createBetterAuthPermission("job", "complete", { requireOwnership: true }),
};

export const propertyPermissions = {
  create: () => createBetterAuthPermission("property", "create"),
  read: () => createBetterAuthPermission("property", "read"),
  update: () =>
    createBetterAuthPermission("property", "update", {
      requireOwnership: true,
    }),
  delete: () =>
    createBetterAuthPermission("property", "delete", {
      requireOwnership: true,
    }),
  list: () => createBetterAuthPermission("property", "read"),
};

export const bidPermissions = {
  create: () => createBetterAuthPermission("bid", "create"),
  read: () => createBetterAuthPermission("bid", "read"),
  update: () =>
    createBetterAuthPermission("bid", "update", { requireOwnership: true }),
  delete: () =>
    createBetterAuthPermission("bid", "delete", { requireOwnership: true }),
  list: () => createBetterAuthPermission("bid", "read"),
  accept: () => createBetterAuthPermission("bid", "accept"),
  reject: () => createBetterAuthPermission("bid", "reject"),
  withdraw: () =>
    createBetterAuthPermission("bid", "withdraw", { requireOwnership: true }),
};

export const organizationPermissions = {
  create: () => createBetterAuthPermission("organization", "create"),
  read: () => createBetterAuthPermission("organization", "read"),
  update: () =>
    createBetterAuthPermission("organization", "update", {
      requireOwnership: true,
    }),
  delete: () =>
    createBetterAuthPermission("organization", "delete", {
      requireOwnership: true,
    }),
  list: () => createBetterAuthPermission("organization", "read"),
  manage: () =>
    createBetterAuthPermission("organization", "manage", {
      requireOwnership: true,
    }),
};

export const schedulePermissions = {
  create: () => createBetterAuthPermission("schedule", "create"),
  read: () => createBetterAuthPermission("schedule", "read"),
  update: () => createBetterAuthPermission("schedule", "update"),
  delete: () => createBetterAuthPermission("schedule", "delete"),
  list: () => createBetterAuthPermission("schedule", "read"),
  confirm: () => createBetterAuthPermission("schedule", "confirm"),
  reschedule: () => createBetterAuthPermission("schedule", "reschedule"),
};

export const messagePermissions = {
  create: () => createBetterAuthPermission("message", "create"),
  read: () => createBetterAuthPermission("message", "read"),
  list: () => createBetterAuthPermission("message", "read"),
};

export const reviewPermissions = {
  create: () => createBetterAuthPermission("review", "create"),
  read: () => createBetterAuthPermission("review", "read"),
  list: () => createBetterAuthPermission("review", "read"),
};

export const servicePermissions = {
  create: () => createBetterAuthPermission("service", "create"),
  read: () => createBetterAuthPermission("service", "read"),
  update: () => createBetterAuthPermission("service", "update"),
  delete: () => createBetterAuthPermission("service", "delete"),
  list: () => createBetterAuthPermission("service", "read"),
};

export const templatePermissions = {
  create: () => createBetterAuthPermission("template", "create"),
  read: () => createBetterAuthPermission("template", "read"),
  update: () => createBetterAuthPermission("template", "update"),
  delete: () => createBetterAuthPermission("template", "delete"),
  list: () => createBetterAuthPermission("template", "read"),
};
