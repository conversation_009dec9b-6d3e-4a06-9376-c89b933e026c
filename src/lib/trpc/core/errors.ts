import { TRPCError } from "@trpc/server";
import { ZodError } from "zod";

// ============================================================================
// ERROR TYPES AND CODES
// ============================================================================

/**
 * Standardized error codes for the application
 */
export const ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  INVALID_TOKEN: "INVALID_TOKEN",
  SESSION_EXPIRED: "SESSION_EXPIRED",

  // Validation
  VALIDATION_ERROR: "BAD_REQUEST",
  INVALID_INPUT: "BAD_REQUEST",
  MISSING_REQUIRED_FIELD: "BAD_REQUEST",

  // Resources
  NOT_FOUND: "NOT_FOUND",
  ALREADY_EXISTS: "CONFLICT",
  RESOURCE_LOCKED: "CONFLICT",

  // Business Logic
  BUSINESS_RULE_VIOLATION: "BAD_REQUEST",
  INSUFFICIENT_PERMISSIONS: "FORBIDDEN",
  OPERATION_NOT_ALLOWED: "BAD_REQUEST",

  // External Services
  EXTERNAL_SERVICE_ERROR: "INTERNAL_SERVER_ERROR",
  RATE_LIMIT_EXCEEDED: "TOO_MANY_REQUESTS",

  // Database
  DATABASE_ERROR: "INTERNAL_SERVER_ERROR",
  CONSTRAINT_VIOLATION: "CONFLICT",

  // System
  INTERNAL_ERROR: "INTERNAL_SERVER_ERROR",
  SERVICE_UNAVAILABLE: "INTERNAL_SERVER_ERROR",
} as const;

/**
 * Error categories for better error handling
 */
export const ERROR_CATEGORIES = {
  AUTH: "authentication",
  VALIDATION: "validation",
  RESOURCE: "resource",
  BUSINESS: "business",
  EXTERNAL: "external",
  DATABASE: "database",
  SYSTEM: "system",
} as const;

/**
 * Error severity levels
 */
export const ERROR_SEVERITY = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  CRITICAL: "critical",
} as const;

/**
 * Standardized error interface
 */
export interface StandardError {
  code: string;
  message: string;
  category: string;
  severity: string;
  details?: Record<string, any>;
  cause?: Error;
  timestamp: Date;
  traceId?: string;
}

// ============================================================================
// ERROR FACTORIES
// ============================================================================

/**
 * Create a standardized TRPC error
 */
function createTRPCError(
  code: keyof typeof ERROR_CODES,
  message: string,
  options?: {
    category?: string;
    severity?: string;
    details?: Record<string, any>;
    cause?: Error;
    traceId?: string;
  }
): TRPCError {
  const standardError: StandardError = {
    code: ERROR_CODES[code],
    message,
    category: options?.category || ERROR_CATEGORIES.SYSTEM,
    severity: options?.severity || ERROR_SEVERITY.MEDIUM,
    details: options?.details,
    cause: options?.cause,
    timestamp: new Date(),
    traceId: options?.traceId,
  };

  return new TRPCError({
    code: ERROR_CODES[code] as any,
    message,
    cause: options?.cause,
  });
}

/**
 * Authentication and Authorization Errors
 */
export const AuthErrors = {
  unauthorized: (message = "Authentication required") =>
    createTRPCError("UNAUTHORIZED", message, {
      category: ERROR_CATEGORIES.AUTH,
      severity: ERROR_SEVERITY.MEDIUM,
    }),

  forbidden: (message = "Access denied") =>
    createTRPCError("FORBIDDEN", message, {
      category: ERROR_CATEGORIES.AUTH,
      severity: ERROR_SEVERITY.MEDIUM,
    }),

  invalidToken: (message = "Invalid or expired token") =>
    createTRPCError("INVALID_TOKEN", message, {
      category: ERROR_CATEGORIES.AUTH,
      severity: ERROR_SEVERITY.MEDIUM,
    }),

  sessionExpired: (message = "Session has expired") =>
    createTRPCError("SESSION_EXPIRED", message, {
      category: ERROR_CATEGORIES.AUTH,
      severity: ERROR_SEVERITY.LOW,
    }),

  insufficientPermissions: (resource: string, action: string) =>
    createTRPCError("INSUFFICIENT_PERMISSIONS",
      `Insufficient permissions to ${action} ${resource}`, {
      category: ERROR_CATEGORIES.AUTH,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { resource, action },
    }),
};

/**
 * Validation Errors
 */
export const ValidationErrors = {
  invalidInput: (field: string, reason?: string) =>
    createTRPCError("INVALID_INPUT",
      `Invalid input for field '${field}'${reason ? `: ${reason}` : ''}`, {
      category: ERROR_CATEGORIES.VALIDATION,
      severity: ERROR_SEVERITY.LOW,
      details: { field, reason },
    }),

  missingRequired: (field: string) =>
    createTRPCError("MISSING_REQUIRED_FIELD",
      `Required field '${field}' is missing`, {
      category: ERROR_CATEGORIES.VALIDATION,
      severity: ERROR_SEVERITY.LOW,
      details: { field },
    }),

  zodValidation: (error: ZodError) =>
    createTRPCError("VALIDATION_ERROR", "Validation failed", {
      category: ERROR_CATEGORIES.VALIDATION,
      severity: ERROR_SEVERITY.LOW,
      details: {
        issues: error.issues.map(issue => ({
          path: issue.path.join('.'),
          message: issue.message,
          code: issue.code,
        }))
      },
      cause: error,
    }),
};

/**
 * Resource Errors
 */
export const ResourceErrors = {
  notFound: (resource: string, id?: string) =>
    createTRPCError("NOT_FOUND",
      `${resource}${id ? ` with ID '${id}'` : ''} not found`, {
      category: ERROR_CATEGORIES.RESOURCE,
      severity: ERROR_SEVERITY.LOW,
      details: { resource, id },
    }),

  alreadyExists: (resource: string, field?: string, value?: string) =>
    createTRPCError("ALREADY_EXISTS",
      `${resource} already exists${field && value ? ` with ${field} '${value}'` : ''}`, {
      category: ERROR_CATEGORIES.RESOURCE,
      severity: ERROR_SEVERITY.LOW,
      details: { resource, field, value },
    }),

  resourceLocked: (resource: string, reason?: string) =>
    createTRPCError("RESOURCE_LOCKED",
      `${resource} is currently locked${reason ? `: ${reason}` : ''}`, {
      category: ERROR_CATEGORIES.RESOURCE,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { resource, reason },
    }),
};

/**
 * Business Logic Errors
 */
export const BusinessErrors = {
  ruleViolation: (rule: string, details?: Record<string, any>) =>
    createTRPCError("BUSINESS_RULE_VIOLATION",
      `Business rule violation: ${rule}`, {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.MEDIUM,
      details,
    }),

  operationNotAllowed: (operation: string, reason?: string) =>
    createTRPCError("OPERATION_NOT_ALLOWED",
      `Operation '${operation}' is not allowed${reason ? `: ${reason}` : ''}`, {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { operation, reason },
    }),

  // Specific business rules for the application
  jobNotPublished: () =>
    createTRPCError("OPERATION_NOT_ALLOWED",
      "Cannot bid on a job that is not published", {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.LOW,
    }),

  bidAlreadyExists: () =>
    createTRPCError("ALREADY_EXISTS",
      "Your organization has already submitted a bid for this job", {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.LOW,
    }),

  quickHireTradeNotEligible: () =>
    createTRPCError("BUSINESS_RULE_VIOLATION",
      "One or more selected trades are not available for quick hire", {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.LOW,
    }),

  quickHireMultipleTasks: () =>
    createTRPCError("BUSINESS_RULE_VIOLATION",
      "Quick hire jobs must have exactly one task", {
      category: ERROR_CATEGORIES.BUSINESS,
      severity: ERROR_SEVERITY.LOW,
    }),
};

/**
 * Database Errors
 */
export const DatabaseErrors = {
  queryFailed: (operation: string, cause?: Error) =>
    createTRPCError("DATABASE_ERROR",
      `Database ${operation} failed`, {
      category: ERROR_CATEGORIES.DATABASE,
      severity: ERROR_SEVERITY.HIGH,
      details: { operation },
      cause,
    }),

  constraintViolation: (constraint: string, details?: Record<string, any>) =>
    createTRPCError("CONSTRAINT_VIOLATION",
      `Database constraint violation: ${constraint}`, {
      category: ERROR_CATEGORIES.DATABASE,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { constraint, ...details },
    }),

  transactionFailed: (cause?: Error) =>
    createTRPCError("DATABASE_ERROR",
      "Database transaction failed", {
      category: ERROR_CATEGORIES.DATABASE,
      severity: ERROR_SEVERITY.HIGH,
      cause,
    }),
};

/**
 * External Service Errors
 */
export const ExternalErrors = {
  serviceUnavailable: (service: string) =>
    createTRPCError("SERVICE_UNAVAILABLE",
      `External service '${service}' is currently unavailable`, {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.HIGH,
      details: { service },
    }),

  rateLimitExceeded: (service: string, resetTime?: Date) =>
    createTRPCError("RATE_LIMIT_EXCEEDED",
      `Rate limit exceeded for service '${service}'`, {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { service, resetTime },
    }),

  externalServiceError: (service: string, cause?: Error) =>
    createTRPCError("EXTERNAL_SERVICE_ERROR",
      `External service '${service}' returned an error`, {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.HIGH,
      details: { service },
      cause,
    }),

  // Specific external service errors
  geocodingFailed: (address: string) =>
    createTRPCError("EXTERNAL_SERVICE_ERROR",
      "Failed to geocode address", {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { address },
    }),

  emailDeliveryFailed: (recipient: string) =>
    createTRPCError("EXTERNAL_SERVICE_ERROR",
      "Failed to deliver email", {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.MEDIUM,
      details: { recipient },
    }),

  pusherEventFailed: (channel: string, event: string) =>
    createTRPCError("EXTERNAL_SERVICE_ERROR",
      "Failed to trigger real-time event", {
      category: ERROR_CATEGORIES.EXTERNAL,
      severity: ERROR_SEVERITY.LOW,
      details: { channel, event },
    }),
};

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Handle database errors with proper categorization
 */
export function handleDatabaseError(error: unknown, operation: string): never {
  if (error instanceof Error) {
    // Check for common database constraint violations
    if (error.message.includes('unique constraint')) {
      throw DatabaseErrors.constraintViolation('unique constraint', {
        operation,
        originalMessage: error.message,
      });
    }

    if (error.message.includes('foreign key constraint')) {
      throw DatabaseErrors.constraintViolation('foreign key constraint', {
        operation,
        originalMessage: error.message,
      });
    }

    if (error.message.includes('not null constraint')) {
      throw DatabaseErrors.constraintViolation('not null constraint', {
        operation,
        originalMessage: error.message,
      });
    }

    // Generic database error
    throw DatabaseErrors.queryFailed(operation, error);
  }

  // Unknown error type
  throw createTRPCError("DATABASE_ERROR", `Database ${operation} failed`, {
    category: ERROR_CATEGORIES.DATABASE,
    severity: ERROR_SEVERITY.HIGH,
    details: { operation, error: String(error) },
  });
}

/**
 * Handle external service errors with retry logic awareness
 */
export function handleExternalServiceError(
  error: unknown,
  service: string,
  options?: {
    isRetryable?: boolean;
    retryAfter?: number;
  }
): never {
  if (error instanceof Error) {
    // Check for rate limiting
    if (error.message.includes('rate limit') || error.message.includes('429')) {
      throw ExternalErrors.rateLimitExceeded(service,
        options?.retryAfter ? new Date(Date.now() + options.retryAfter * 1000) : undefined
      );
    }

    // Check for service unavailability
    if (error.message.includes('503') || error.message.includes('unavailable')) {
      throw ExternalErrors.serviceUnavailable(service);
    }

    // Generic external service error
    throw ExternalErrors.externalServiceError(service, error);
  }

  throw ExternalErrors.externalServiceError(service);
}

/**
 * Handle validation errors from Zod
 */
export function handleValidationError(error: ZodError): never {
  throw ValidationErrors.zodValidation(error);
}

/**
 * Safe error handler that doesn't throw
 */
export function safeErrorHandler(
  error: unknown,
  context: string,
  logger?: (message: string, error: unknown) => void
): StandardError {
  const timestamp = new Date();

  if (error instanceof TRPCError) {
    return {
      code: error.code,
      message: error.message,
      category: ERROR_CATEGORIES.SYSTEM,
      severity: ERROR_SEVERITY.MEDIUM,
      cause: error.cause instanceof Error ? error.cause : undefined,
      timestamp,
    };
  }

  if (error instanceof ZodError) {
    return {
      code: ERROR_CODES.VALIDATION_ERROR,
      message: "Validation failed",
      category: ERROR_CATEGORIES.VALIDATION,
      severity: ERROR_SEVERITY.LOW,
      details: {
        issues: error.issues.map(issue => ({
          path: issue.path.join('.'),
          message: issue.message,
          code: issue.code,
        }))
      },
      cause: error,
      timestamp,
    };
  }

  if (error instanceof Error) {
    logger?.(context, error);

    return {
      code: ERROR_CODES.INTERNAL_ERROR,
      message: error.message,
      category: ERROR_CATEGORIES.SYSTEM,
      severity: ERROR_SEVERITY.HIGH,
      cause: error,
      timestamp,
    };
  }

  // Unknown error type
  const errorMessage = `Unknown error in ${context}: ${String(error)}`;
  logger?.(errorMessage, error);

  return {
    code: ERROR_CODES.INTERNAL_ERROR,
    message: errorMessage,
    category: ERROR_CATEGORIES.SYSTEM,
    severity: ERROR_SEVERITY.HIGH,
    timestamp,
  };
}

/**
 * Error boundary for async operations
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: string,
  options?: {
    fallback?: T;
    logger?: (message: string, error: unknown) => void;
    rethrow?: boolean;
  }
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    safeErrorHandler(error, context, options?.logger);

    if (options?.rethrow !== false) {
      throw error;
    }

    if (options?.fallback !== undefined) {
      return options.fallback;
    }

    throw error;
  }
}
