"use server";

import { headers } from "next/headers";
import { auth } from "@/lib/auth";

export const createContext = async () => {
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  return { userId: user?.id, role: user?.role, session: session };
};

export type Context = Awaited<ReturnType<typeof createContext>>;

/**
 * Authenticated context type - used after authentication middleware
 * This ensures userId is always defined in protected procedures
 */
export type AuthenticatedContext = Context & {
  userId: string; // Non-nullable userId after authentication
};
