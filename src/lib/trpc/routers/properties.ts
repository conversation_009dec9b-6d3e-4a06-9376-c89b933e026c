import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { address, property } from "@/db/schema";
import { geocodeAddress } from "@/lib/geocoding";
import { propertyProcedures, router } from "@/lib/trpc/procedures";
import {
  entityInputSchema,
  propertyCreateSchema,
  updateInputSchema,
} from "../schemas";
import { createAddress } from "../utils/addresses";
import { getOptimizedPropertiesList } from "../utils/optimized-queries";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "../utils/query-performance";

export const propertiesRouter = router({
  // OPTIMIZED: Use single query with aggregated data instead of N+1 queries
  list: propertyProcedures.list.query(async ({ ctx }) => {
    return withQueryPerformanceMonitoring("properties.list", async () => {
      const cacheKey = `properties-list:${ctx.userId}`;

      return withQueryCache(
        cacheKey,
        () => getOptimizedPropertiesList(db, ctx.userId as string),
        300000, // 5 minutes cache
      );
    });
  }),

  create: propertyProcedures.create
    .input(propertyCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Geocode the address
      const location = await geocodeAddress(input.address);

      const addressRecord = await createAddress(
        {
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        },
        db,
      );

      if (!addressRecord) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create address",
        });
      }

      // Create the property with the address
      const [newProperty] = await db
        .insert(property)
        .values({
          name: input.name,
          imageUrl: input.imageUrl,
          userId: ctx.userId as string,
          addressId: addressRecord.id,
        })
        .returning();

      return newProperty;
    }),

  update: propertyProcedures.update
    .input(
      updateInputSchema.extend({
        address: z.object({
          street: z.string().min(1, "Street is required"),
          city: z.string().min(1, "City is required"),
          state: z.string().min(1, "State is required"),
          zip: z.string().min(1, "ZIP code is required"),
        }),
      }),
    )
    .mutation(async ({ input }) => {
      // Get the property to update (ownership already verified by middleware)
      // Use explicit select to avoid PostGIS serialization issues
      const [result] = await db
        .select({
          property: {
            id: property.id,
            name: property.name,
            imageUrl: property.imageUrl,
            userId: property.userId,
            addressId: property.addressId,
            createdAt: property.createdAt,
            updatedAt: property.updatedAt,
          },
          address: {
            id: address.id,
            street: address.street,
            city: address.city,
            state: address.state,
            zip: address.zip,
            createdAt: address.createdAt,
            updatedAt: address.updatedAt,
          },
        })
        .from(property)
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(property.id, input.id))
        .limit(1);

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      // Geocode the address
      const location = await geocodeAddress(input.address);

      // Update the address
      await db
        .update(address)
        .set({
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        })
        .where(eq(address.id, result.address.id));

      // Update the property
      const [updatedProperty] = await db
        .update(property)
        .set({
          name: input.name,
        })
        .where(eq(property.id, input.id))
        .returning();

      return updatedProperty;
    }),

  delete: propertyProcedures.delete
    .input(entityInputSchema)
    .mutation(async ({ input }) => {
      // Ownership already verified by middleware
      return await db.delete(property).where(eq(property.id, input.id));
    }),

  getById: propertyProcedures.read
    .input(entityInputSchema)
    .query(async ({ input }) => {
      // Ownership already verified by middleware
      // Use explicit select to avoid PostGIS serialization issues
      const [propertyData] = await db
        .select({
          property: {
            id: property.id,
            name: property.name,
            imageUrl: property.imageUrl,
            userId: property.userId,
            addressId: property.addressId,
            createdAt: property.createdAt,
            updatedAt: property.updatedAt,
            description: property.description,
            type: property.type,
          },
          address: {
            id: address.id,
            street: address.street,
            city: address.city,
            state: address.state,
            zip: address.zip,
            createdAt: address.createdAt,
            updatedAt: address.updatedAt,
            location: address.location,
          },
        })
        .from(property)
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(property.id, input.id))
        .limit(1);

      if (!propertyData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      return {
        ...propertyData.property,
        address: propertyData.address,
      };
    }),
});
