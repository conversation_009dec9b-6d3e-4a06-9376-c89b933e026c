import { TRPCError } from "@trpc/server";
import { asc, eq } from "drizzle-orm";
import { z } from "zod/v4";
import { db } from "@/db";
import { job, jobImage } from "@/db/schema";
import { jobProcedures } from "@/lib/trpc/procedures";
import { batchLoadJobsWithRelations } from "@/lib/trpc/utils/query-optimizations";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "@/lib/trpc/utils/query-performance";

export const coreProjectsRouter = {
  list: jobProcedures.list
    .input(z.object({ limit: z.number().optional() }).optional())
    .query(async ({ input }) => {
      const result = await db.query.job.findMany({
        orderBy: [asc(job.name)],
        with: {
          property: true,
          bids: {
            with: {
              organization: true,
            },
          },
        },
        limit: input?.limit,
      });

      // Add bidsCount manually to avoid Drizzle SQL generation bug
      return result.map((project) => ({
        ...project,
        bidsCount: project.bids?.length ?? 0,
      }));
    }),
  getById: jobProcedures.read
    .input(
      z.object({
        id: z.string(),
        includeReviews: z.boolean().default(false),
        includeSchedules: z.boolean().default(false),
      }),
    )
    .query(async ({ input }) => {
      const { id, includeReviews, includeSchedules } = input;

      return withQueryPerformanceMonitoring("projects.getById", async () => {
        const cacheKey = `project:${id}:${includeReviews}:${includeSchedules}`;

        return withQueryCache(
          cacheKey,
          async () => {
            const projectsMap = await batchLoadJobsWithRelations(db, [id]);
            const projectData = projectsMap.get(id);

            if (!projectData) {
              throw new TRPCError({
                code: "NOT_FOUND",
                message: "Project not found",
              });
            }

            return projectData;
          },
          120000, // 2 minutes cache
        );
      });
    }),

  update: jobProcedures.update
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        propertyId: z.string(),
        images: z
          .object({
            url: z.string(),
            description: z.string().optional().nullable(),
          })
          .array()
          .optional(),
      }),
    )
    .mutation(async ({ input }) => {
      // First, delete existing images if we're updating them
      if (input.images) {
        await db.delete(jobImage).where(eq(jobImage.jobId, input.id));
      }

      // Update the job
      const [result] = await db
        .update(job)
        .set({
          name: input.name,
          propertyId: input.propertyId,
        })
        .where(eq(job.id, input.id))
        .returning();

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        await db.insert(jobImage).values(
          input.images.map((image) => ({
            jobId: input.id,
            url: image.url,
            description: image.description,
          })),
        );
      }

      return result;
    }),

  delete: jobProcedures.delete
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      // Get the job before deletion
      const result = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Delete the job
      await db.delete(job).where(eq(job.id, input.id));

      return result;
    }),

  publish: jobProcedures.update
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      // Update the job status to PUBLISHED
      const [result] = await db
        .update(job)
        .set({ status: "PUBLISHED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),

  cancel: jobProcedures.update
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      // Check if job is in a state that can be cancelled
      const projectData = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!projectData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Only allow cancellation of DRAFT or PUBLISHED jobs
      if (projectData.status !== "DRAFT" && projectData.status !== "PUBLISHED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only draft or published projects can be cancelled",
        });
      }

      // Update the job status to CANCELLED
      const [result] = await db
        .update(job)
        .set({ status: "CANCELED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),
};
