import { TRPCError } from "@trpc/server";
import { and, asc, desc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import {
  address,
  bid,
  job,
  type Property,
  property,
  schedule,
} from "@/db/schema";
import { jobProcedures } from "@/lib/trpc/procedures";
import { calculateDistance, type Location } from "@/lib/trpc/utils/geo";
import {
  getOptimizedPublishedJobs,
  getOptimizedUserJobs,
} from "@/lib/trpc/utils/optimized-queries";
import { getUserOrganization } from "@/lib/trpc/utils/permissions";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "@/lib/trpc/utils/query-performance";

export const listingRouter = {
  listForUser: jobProcedures.list
    .input(
      z
        .object({
          limit: z.number().min(1).max(100).default(20),
          cursor: z.string().optional(),
          includeCompleted: z.boolean().default(true),
        })
        .optional(),
    )
    .query(async ({ input, ctx }) => {
      // Explicit userId check to ensure it's always defined
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User ID is required for this operation",
        });
      }

      const userId = ctx.userId; // Store in variable for type safety
      const { limit = 20, cursor, includeCompleted = true } = input || {};

      return withQueryPerformanceMonitoring("projects.listForUser", async () => {
        const cacheKey = `user-projects:${userId}:${limit}:${cursor}:${includeCompleted}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedUserJobs(db, userId),
          60000, // 1 minute cache
        );
      });
    }),

  listPublished: jobProcedures.list
    .input(
      z
        .object({
          limit: z.number().min(1).max(50).default(20),
          cursor: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ input }) => {
      const { limit = 20, cursor } = input || {};

      return withQueryPerformanceMonitoring("projects.listPublished", async () => {
        const cacheKey = `published-projects:${limit}:${cursor || "first"}`;

        return withQueryCache(
          cacheKey,
          () => getOptimizedPublishedJobs(db, limit, cursor),
          30000, // 30 seconds cache for published projects
        );
      });
    }),

  listActiveForOrganization: jobProcedures.list
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const activeProjects = await db
        .select({
          job,
          property,
          address,
          bidsCount: db.$count(bid, eq(job.id, bid.jobId)),
        })
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .leftJoin(bid, eq(job.id, bid.jobId))
        .where(
          and(
            eq(job.status, "PUBLISHED"),
            eq(bid.organizationId, input.organizationId),
            eq(bid.status, "ACCEPTED"),
          ),
        )
        .orderBy(desc(job.createdAt));

      const projects = activeProjects.map((project) => ({
        ...project.job,
        property: {
          ...(project.property as Property),
          address: project.address,
        },
        bidsCount: project.bidsCount,
      }));

      return projects;
    }),

  listPublishedByDistance: jobProcedures.list
    .input(
      z.object({
        maxDistance: z.number().optional().default(50), // Default 50 miles
      }),
    )
    .query(async ({ input, ctx }) => {
      // Explicit userId check to ensure it's always defined
      if (!ctx.userId) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "User ID is required for this operation",
        });
      }

      // Find the user's organization
      const userOrg = await getUserOrganization(ctx.userId);

      if (!userOrg || !userOrg.address?.location) {
        console.log("No organization or location found, returning no projects");
        return [];
      }

      const orgLocation = userOrg.address.location as Location;

      // Get all published projects
      const publishedProjects = await db
        .select({
          job,
          property,
          address,
          bidsCount: db.$count(bid, eq(job.id, bid.jobId)),
        })
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(job.status, "PUBLISHED"))
        .orderBy(desc(job.createdAt));

      if (!publishedProjects) {
        throw new TRPCError({
          code: "NOT_FOUND",
        });
      }

      const projects = publishedProjects.map((project) => ({
        ...project.job,
        property: {
          ...(project.property as Property),
          address: project.address,
        },
        bidsCount: project.bidsCount,
      }));

      // Filter out projects without location data
      const projectsWithLocation = projects.filter(
        (project) => project.property.address?.location,
      );

      // Calculate distance for each project and filter by max distance
      const projectsWithDistance = await Promise.all(
        projectsWithLocation.map(async (project) => {
          const projectLocation = project.property.address.location as Location;
          const distance = await calculateDistance(orgLocation, projectLocation);

          if (distance !== undefined) {
            return {
              ...project,
              distance,
            };
          }

          return null;
        }),
      );

      // Filter out null results and projects beyond max distance
      return projectsWithDistance
        .filter(
          (project): project is typeof project & { distance: number } =>
            project !== null &&
            typeof project.distance === "number" &&
            project.distance <= input.maxDistance,
        )
        .sort((a, b) => a.distance - b.distance);
    }),

  listByProperty: jobProcedures.list
    .input(
      z.object({
        propertyId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(job)
        .leftJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(job.propertyId, input.propertyId))
        .orderBy(desc(job.completedAt), desc(job.createdAt));

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      return result.map((result) => {
        return {
          ...result.job,
          property: {
            ...(result.property as Property),
            address: result.address,
          },
        };
      });
    }),

  listActiveForUser: jobProcedures.list.query(async ({ ctx }) => {
    // Explicit userId check to ensure it's always defined
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User ID is required for this operation",
      });
    }

    // Find the user's organization
    const userOrg = await getUserOrganization(ctx.userId);

    if (!userOrg) {
      return [];
    }

    // Get active projects for this organization
    return db.query.job.findMany({
      where: and(
        eq(job.status, "PUBLISHED"),
        eq(bid.organizationId, userOrg.id as string),
        eq(bid.status, "ACCEPTED"),
      ),
      with: {
        property: {
          with: {
            address: true,
          },
        },
      },
      orderBy: [desc(job.createdAt)],
    });
  }),

  listScheduledForOrganization: jobProcedures.list
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const scheduledProjects = await db.query.job.findMany({
        where: and(
          eq(job.status, "AWARDED"),
          eq(bid.organizationId, input.organizationId),
          eq(bid.status, "ACCEPTED"),
        ),
        with: {
          property: {
            with: {
              address: true,
            },
          },
          schedules: true,
        },
        orderBy: [asc(schedule.proposedStartDate)],
        extras: {
          bidsCount: db.$count(bid, eq(bid.jobId, job.id)).as("bidsCount"),
        },
      });

      return scheduledProjects;
    }),
};
