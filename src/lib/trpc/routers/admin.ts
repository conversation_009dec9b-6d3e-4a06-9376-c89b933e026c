import { TRPCError } from "@trpc/server";
import { asc, count, eq, inArray, sql } from "drizzle-orm";
import { z } from "zod/v4";
import { db } from "@/db";
import {
  account,
  address,
  bid,
  job,
  organization,
  property,
  task,
  user,
} from "@/db/schema";
import { calculateJobsTrend, calculateUsersTrend } from "@/lib/utils";
import { adminProcedure, protectedProcedure, router } from "../procedures";

export const adminRouter = router({
  getStats: protectedProcedure.query(async () => {
    // Get total users
    const totalUsers = await db.$count(account);

    // Get jobs by status
    const jobsByStatus = await db
      .select({ count: count(job.id), status: job.status })
      .from(job)
      .groupBy(job.status);

    // Get active and completed jobs
    const activeJobs = await db.$count(job, eq(job.status, "PUBLISHED"));

    const completedJobs = await db.$count(
      job,
      inArray(job.status, ["AWARDED", "CLOSED"]),
    );

    // Calculate trends
    const usersTrend = await calculateUsersTrend(db);
    const jobsTrend = await calculateJobsTrend(db);
    const completedJobsTrend = await calculateJobsTrend(db, [
      "AWARDED",
      "CLOSED",
    ]);

    // Calculate revenue (placeholder - implement actual calculation)
    const totalRevenue = 25000;
    const revenueTrend = 8; // Example value - implement actual calculation

    // Example data for charts
    const jobStatusData = jobsByStatus.map((item) => ({
      name: item.status,
      value: item.count,
    }));

    const revenueByMonth = [
      { month: "Jan", value: 4000 },
      { month: "Feb", value: 3000 },
      { month: "Mar", value: 5000 },
      { month: "Apr", value: 7000 },
      { month: "May", value: 6000 },
      { month: "Jun", value: 8000 },
    ];

    return {
      totalUsers,
      activeJobs,
      completedJobs,
      totalRevenue,
      usersTrend,
      jobsTrend,
      completedJobsTrend,
      revenueTrend,
      jobsByStatus: jobStatusData,
      revenueByMonth,
    };
  }),

  // Admin-specific project queries with homeowner information
  listProjects: adminProcedure
    .input(z.object({ limit: z.number().optional() }).optional())
    .query(async ({ input }) => {
      // Simplified query for projects table - only essential fields
      const result = await db
        .select({
          // Essential project data for table display
          id: job.id,
          name: job.name,
          budget: job.budget,
          status: job.status,
          createdAt: job.createdAt,

          // Property name for table display
          propertyName: property.name,

          // Bid count using aggregation
          bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
        })
        .from(job)
        .innerJoin(property, eq(job.propertyId, property.id))
        .leftJoin(bid, eq(job.id, bid.jobId))
        .groupBy(job.id, property.id)
        .orderBy(asc(job.name))
        .limit(input?.limit || 50);

      // Return simplified structure for table display
      return result.map((project) => ({
        id: project.id,
        name: project.name,
        budget: project.budget,
        status: project.status,
        createdAt: project.createdAt,
        bidsCount: project.bidsCount,
        property: {
          name: project.propertyName,
        },
      }));
    }),

  getProjectById: adminProcedure
    .input(
      z.object({
        id: z.string(),
        includeReviews: z.boolean().default(false),
        includeSchedules: z.boolean().default(false),
      }),
    )
    .query(async ({ input }) => {
      const { id, includeReviews, includeSchedules } = input;

      // Use explicit select to avoid PostGIS serialization issues
      const jobData = await db
        .select({
          // Job data
          id: job.id,
          name: job.name,
          propertyId: job.propertyId,
          budget: job.budget,
          taskBids: job.taskBids,
          startsAt: job.startsAt,
          deadline: job.deadline,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
          status: job.status,
          completedAt: job.completedAt,
          contractorCompleted: job.contractorCompleted,
          homeownerCompleted: job.homeownerCompleted,
          isRecurring: job.isRecurring,
          jobType: job.jobType,
          recurringFrequency: job.recurringFrequency,

          // Property data
          property: {
            id: property.id,
            name: property.name,
            imageUrl: property.imageUrl,
            userId: property.userId,
            addressId: property.addressId,
            createdAt: property.createdAt,
            updatedAt: property.updatedAt,
          },

          // Address data (excluding PostGIS location column)
          address: {
            id: address.id,
            street: address.street,
            city: address.city,
            state: address.state,
            zip: address.zip,
            createdAt: address.createdAt,
            updatedAt: address.updatedAt,
          },

          // User data (homeowner information)
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            image: user.image,
            createdAt: user.createdAt,
          },
        })
        .from(job)
        .innerJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .innerJoin(user, eq(property.userId, user.id))
        .where(eq(job.id, id))
        .limit(1);

      const jobRecord = jobData.at(0);

      if (!jobRecord) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Get bids separately
      const bids = await db
        .select({
          id: bid.id,
          amount: bid.amount,
          status: bid.status,
          organizationId: bid.organizationId,
          name: bid.name,
          createdAt: bid.createdAt,
          updatedAt: bid.updatedAt,
          description: bid.description,
          estimatedDuration: bid.estimatedDuration,
          organization: {
            id: organization.id,
            name: organization.name,
          },
        })
        .from(bid)
        .innerJoin(organization, eq(bid.organizationId, organization.id))
        .where(eq(bid.jobId, id));

      // Get tasks separately
      const tasks = await db
        .select({
          id: task.id,
          name: task.name,
          tradeId: task.tradeId,
          createdAt: task.createdAt,
        })
        .from(task)
        .where(eq(task.jobId, id));

      // TODO: Add reviews and schedules if requested
      // For now, we'll return empty arrays to maintain compatibility
      const reviews = includeReviews ? [] : undefined;
      const schedules = includeSchedules ? [] : undefined;

      return {
        ...jobRecord,
        property: {
          ...jobRecord.property,
          address: jobRecord.address,
          user: jobRecord.user,
        },
        tasks: tasks.map((t) => ({
          id: t.id,
          name: t.name,
          tradeId: t.tradeId,
          createdAt: t.createdAt,
        })),
        bids: bids.map((b) => ({
          id: b.id,
          amount: b.amount,
          status: b.status,
          organizationId: b.organizationId,
          organization: b.organization,
          createdAt: b.createdAt,
          updatedAt: b.updatedAt,
          description: b.description,
          estimatedDuration: b.estimatedDuration,
          name: b.name,
        })),
        bidsCount: bids.length,
        ...(reviews !== undefined && { reviews }),
        ...(schedules !== undefined && { schedules }),
      };
    }),
});
