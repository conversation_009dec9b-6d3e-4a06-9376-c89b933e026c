import { TRPCError } from "@trpc/server";
import { eq, type InferInsertModel } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { address, membership, organization, trade } from "@/db/schema";
import { geocodeAddress } from "@/lib/geocoding";
import { protectedProcedure } from "@/lib/trpc/procedures";
import { createAddress, updateAddress } from "@/lib/trpc/utils/addresses";
import {
  checkOrganizationMembership,
  requireAuth,
} from "@/lib/trpc/utils/permissions";
import { createServices } from "@/lib/trpc/utils/services";

export const organizationRouter = {
  getForUser: protectedProcedure.query(async ({ ctx }) => {
    if (ctx.role === "homeowner") {
      return null;
    }

    const [org] = await db
      .select()
      .from(organization)
      .leftJoin(membership, eq(organization.id, membership.organizationId))
      .innerJoin(trade, eq(organization.tradeId, trade.id))
      .innerJoin(address, eq(organization.addressId, address.id))
      .where(eq(membership.userId, ctx.userId));

    if (!org) {
      return null;
    }

    return {
      ...org.organization,
      trade: org.trade,
      address: org.address,
    };
  }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const org = await db.query.organization.findFirst({
        where: eq(organization.id, input.id),
        with: {
          trade: true,
          address: true,
          services: true,
        },
      });

      if (!org) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      return org;
    }),

  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        trade: z.object({
          id: z.string(),
        }),
        description: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
        acceptsQuickHire: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const location = await geocodeAddress(input.address);

      const address = await createAddress(
        {
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location || { x: 0, y: 0 },
        },
        db,
      );

      if (!address) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create address",
        });
      }

      const query: InferInsertModel<typeof organization> = {
        name: input.name,
        description: input.description,
        email: input.email,
        phone: input.phone,
        acceptsQuickHire: input.acceptsQuickHire,
        tradeId: input.trade.id,
        addressId: address.id,
      };

      const [org] = await db.insert(organization).values(query).returning();

      if (!org) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create organization",
        });
      }

      await db.insert(membership).values({
        userId: ctx.userId,
        organizationId: org.id,
        role: "owner",
      });

      return org;
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        trade: z
          .object({
            id: z.string(),
          })
          .optional(),
        description: z.string().optional(),
        email: z.string().email().optional().or(z.literal("")),
        phone: z.string().optional().or(z.literal("")),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
        acceptsQuickHire: z.boolean().optional(),
        services: z
          .array(
            z.object({
              id: z.string().optional(),
              name: z.string(),
              description: z.string(),
              price: z
                .number()
                .max(200, "Service price cannot exceed $200 for quick hire"),
              duration: z.number(),
            }),
          )
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Find organization users and is the userid
      //  in the context an applicable role to update?

      const user = await checkOrganizationMembership(
        ctx.userId,
        input.id,
        "owner",
      );

      requireAuth(
        user,
        "You don't have permission to update this organization",
      );

      const location = await geocodeAddress(input.address);

      const org = await db.transaction(async (tx) => {
        const address = await updateAddress(
          input.id,
          {
            street: input.address.street,
            city: input.address.city,
            state: input.address.state,
            zip: input.address.zip,
            location: location || { x: 0, y: 0 },
          },
          tx,
        );

        if (!address) {
          tx.rollback();
          return;
        }

        const updateData: InferInsertModel<typeof organization> = {
          name: input.name || "",
          description: input.description,
          email: input.email || null,
          phone: input.phone || null,
          acceptsQuickHire: input.acceptsQuickHire || false,
          tradeId: input.trade?.id || "",
          addressId: address.id,
        };

        const [org] = await db
          .update(organization)
          .set(updateData)
          .where(eq(organization.id, input.id))
          .returning();

        return org;
      });

      // Handle services if provided
      if (input.services) {
        await createServices(input.id, input.services, db);
      }

      return org;
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Find organization users and is the userid in the context an applicable role to update?

      const user = await checkOrganizationMembership(
        ctx.userId,
        input.id,
        "owner",
      );

      requireAuth(
        user,
        "You don't have permission to delete this organization",
      );

      const [org] = await db
        .delete(organization)
        .where(eq(organization.id, input.id))
        .returning();

      return org;
    }),
};
