import { and, asc, eq, ilike, notInArray, or } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { organization, trade } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/procedures";

export const searchRouter = {
  search: protectedProcedure
    .input(
      z.object({
        query: z.string(),
        excludeIds: z.array(z.string()).optional(),
      }),
    )
    .mutation(async ({ input }) => {
      return db.query.organization.findMany({
        where: and(
          or(
            ilike(organization.name, `%${input.query}%`),
            ilike(trade.name, `%${input.query}%`),
          ),
          notInArray(organization.id, input.excludeIds || []),
        ),
        with: {
          trade: true,
        },
        limit: 5,
      });
    }),

  // Add the new listByTrade procedure
  listByTrade: protectedProcedure
    .input(
      z.object({
        tradeId: z.string(),
        acceptsQuickHire: z.boolean().optional().default(false),
      }),
    )
    .query(async ({ input }) => {
      const filters = [eq(organization.tradeId, input.tradeId)];

      if (input.acceptsQuickHire) {
        filters.push(eq(organization.acceptsQuickHire, true));
      }

      return db.query.organization.findMany({
        where: and(...filters),
        with: {
          trade: true,
          services: true,
        },
        orderBy: [asc(organization.name)],
      });
    }),
};
