import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import { jobTemplate, templateTask } from "@/db/schema";
import { router, templateProcedures } from "../procedures";
import { entityInputSchema, templateCreateSchema } from "../schemas";

export const templatesRouter = router({
  list: templateProcedures.list.query(async () => {
    return db.query.jobTemplate.findMany({
      with: {
        tasks: {
          with: {
            trade: true,
          },
        },
      },
    });
  }),

  create: templateProcedures.create
    .input(templateCreateSchema)
    .mutation(async ({ input }) => {
      // First create the template
      const [newTemplate] = await db
        .insert(jobTemplate)
        .values({
          name: input.name,
          description: input.description,
          budget: input.budget,
          estimatedDuration: input.estimatedDuration || 7,
        })
        .returning();

      if (!newTemplate) {
        throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
      }

      // Then create the tasks
      if (input.tasks.length > 0) {
        await db.insert(templateTask).values(
          input.tasks.map((task) => ({
            name: task.name,
            tradeId: task.tradeId,
            templateId: newTemplate.id,
          })),
        );
      }

      return newTemplate;
    }),

  one: templateProcedures.read
    .input(entityInputSchema)
    .query(async ({ input }) => {
      const templateData = await db.query.jobTemplate.findFirst({
        where: eq(jobTemplate.id, input.id),
        with: {
          tasks: {
            with: {
              trade: true,
            },
          },
        },
      });
      return templateData;
    }),

  delete: templateProcedures.delete
    .input(entityInputSchema)
    .mutation(async ({ input }) => {
      const deletedTemplate = await db
        .delete(jobTemplate)
        .where(eq(jobTemplate.id, input.id));
      return deletedTemplate;
    }),
});
