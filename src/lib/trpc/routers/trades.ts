import { asc, eq } from "drizzle-orm";
import { db } from "@/db";
import { organization, trade } from "@/db/schema";
import { protectedProcedure, router, tradeProcedures } from "../procedures";
import {
  entityInputSchema,
  tradeCreateSchema,
  tradeUpdateSchema,
} from "../schemas";

export const tradesRouter = router({
  list: tradeProcedures.list.query(async () => {
    return db.query.trade.findMany({
      orderBy: [asc(trade.name)],
      with: {
        organizations: true,
      },
      extras: {
        organizationCount: db.$count(organization).as("organizationCount"),
      },
    });
  }),

  create: tradeProcedures.create
    .input(tradeCreateSchema)
    .mutation(async ({ input }) => {
      return db
        .insert(trade)
        .values({
          name: input.name,
          availableForQuickHire: input.availableForQuickHire,
        })
        .returning();
    }),

  one: tradeProcedures.read
    .input(entityInputSchema)
    .query(async ({ input }) => {
      return db.query.trade.findFirst({
        where: eq(trade.id, input.id),
      });
    }),

  update: tradeProcedures.update
    .input(tradeUpdateSchema)
    .mutation(async ({ input }) => {
      return db
        .update(trade)
        .set({
          name: input.name,
          availableForQuickHire: input.availableForQuickHire,
        })
        .where(eq(trade.id, input.id))
        .returning();
    }),

  delete: tradeProcedures.delete
    .input(entityInputSchema)
    .mutation(async ({ input }) => {
      return db.delete(trade).where(eq(trade.id, input.id)).returning();
    }),

  listAvailableForQuickHire: protectedProcedure.query(async () => {
    return db.query.trade.findMany({
      where: eq(trade.availableForQuickHire, true),
      orderBy: [asc(trade.name)],
    });
  }),
});
