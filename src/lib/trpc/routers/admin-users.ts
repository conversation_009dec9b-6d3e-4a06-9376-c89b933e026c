import { TRPCError } from "@trpc/server";
import { and, desc, eq, gte, ilike, or } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { account, session, user } from "@/db/schema";
import { authClient } from "@/lib/auth-client";
import { adminProcedure, router } from "../procedures";

// Input schemas
const UserFilterSchema = z.object({
  search: z.string().optional(),
  role: z.enum(["admin", "homeowner", "contractor"]).optional(),
  status: z.enum(["active", "banned", "unverified"]).optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
});

const UserUpdateSchema = z.object({
  userId: z.string(),
  name: z.string().optional(),
  email: z.string().email().optional(),
  role: z.enum(["admin", "homeowner", "contractor"]).optional(),
  emailVerified: z.boolean().optional(),
  onboardingComplete: z.boolean().optional(),
});

const BanUserSchema = z.object({
  userId: z.string(),
  reason: z.string().optional(),
  expiresAt: z.date().optional(),
});

export const adminUsersRouter = router({
  // Get comprehensive user statistics
  getStats: adminProcedure.query(async () => {
    const [
      totalUsers,
      totalAdmins,
      totalHomeowners,
      totalContractors,
      verifiedUsers,
      bannedUsers,
      twoFactorUsers,
      recentUsers,
    ] = await Promise.all([
      db.$count(user),
      db.$count(user, eq(user.role, "admin")),
      db.$count(user, eq(user.role, "homeowner")),
      db.$count(user, eq(user.role, "contractor")),
      db.$count(user, eq(user.emailVerified, true)),
      db.$count(user, eq(user.banned, true)),
      db.$count(user, eq(user.twoFactorEnabled, true)),
      db.$count(
        user,
        gte(user.createdAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
      ),
    ]);

    return {
      total: totalUsers,
      byRole: {
        admin: totalAdmins,
        homeowner: totalHomeowners,
        contractor: totalContractors,
      },
      verified: verifiedUsers,
      banned: bannedUsers,
      twoFactorEnabled: twoFactorUsers,
      recentSignups: recentUsers,
      verificationRate: totalUsers > 0 ? (verifiedUsers / totalUsers) * 100 : 0,
      twoFactorRate: totalUsers > 0 ? (twoFactorUsers / totalUsers) * 100 : 0,
    };
  }),

  // List users with advanced filtering and pagination
  list: adminProcedure.input(UserFilterSchema).query(async ({ input }) => {
    const { search, role, status, limit, offset } = input;

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(ilike(user.name, `%${search}%`), ilike(user.email, `%${search}%`)),
      );
    }

    if (role) {
      conditions.push(eq(user.role, role));
    }

    if (status) {
      switch (status) {
        case "banned":
          conditions.push(eq(user.banned, true));
          break;
        case "unverified":
          conditions.push(eq(user.emailVerified, false));
          break;
        case "active":
          conditions.push(
            and(eq(user.banned, false), eq(user.emailVerified, true)),
          );
          break;
      }
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get users with pagination
    const users = await db
      .select({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        banned: user.banned,
        banReason: user.banReason,
        banExpires: user.banExpires,
        twoFactorEnabled: user.twoFactorEnabled,
        onboardingComplete: user.onboardingComplete,
        image: user.image,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
      .from(user)
      .where(whereClause)
      .orderBy(desc(user.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCount = await db.$count(user, whereClause);

    return {
      users,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    };
  }),

  // Get user by ID with detailed information
  getById: adminProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      const userData = await db.query.user.findFirst({
        where: eq(user.id, input.userId),
      });

      if (!userData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get user's active sessions
      const activeSessions = await db
        .select({
          id: session.id,
          createdAt: session.createdAt,
          ipAddress: session.ipAddress,
          userAgent: session.userAgent,
          impersonatedBy: session.impersonatedBy,
        })
        .from(session)
        .where(
          and(
            eq(session.userId, input.userId),
            // Only active sessions (not expired)
            // Note: You might want to add expiration check here
          ),
        )
        .orderBy(desc(session.createdAt));

      // Get user's accounts (social logins)
      const accounts = await db
        .select({
          id: account.id,
          providerId: account.providerId,
          createdAt: account.createdAt,
        })
        .from(account)
        .where(eq(account.userId, input.userId));

      return {
        ...userData,
        activeSessions,
        accounts,
      };
    }),

  // Update user information
  update: adminProcedure.input(UserUpdateSchema).mutation(async ({ input }) => {
    const { userId, ...updateData } = input;

    // Check if user exists
    const existingUser = await db.query.user.findFirst({
      where: eq(user.id, userId),
    });

    if (!existingUser) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    // Update user
    const [updatedUser] = await db
      .update(user)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(user.id, userId))
      .returning();

    return updatedUser;
  }),

  // Ban user
  ban: adminProcedure.input(BanUserSchema).mutation(async ({ input }) => {
    const { userId, reason, expiresAt } = input;

    const [updatedUser] = await db
      .update(user)
      .set({
        banned: true,
        banReason: reason,
        banExpires: expiresAt,
        updatedAt: new Date(),
      })
      .where(eq(user.id, userId))
      .returning();

    if (!updatedUser) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    // Invalidate all user sessions
    await db.delete(session).where(eq(session.userId, userId));

    return updatedUser;
  }),

  // Unban user
  unban: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ input }) => {
      const [updatedUser] = await db
        .update(user)
        .set({
          banned: false,
          banReason: null,
          banExpires: null,
          updatedAt: new Date(),
        })
        .where(eq(user.id, input.userId))
        .returning();

      if (!updatedUser) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return updatedUser;
    }),

  // Change user role
  changeRole: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        role: z.enum(["admin", "homeowner", "contractor"]),
      }),
    )
    .mutation(async ({ input }) => {
      const { userId, role } = input;

      // Use better-auth admin client to change role
      try {
        const result = await authClient.admin.setRole({
          userId,
          role,
        });

        if (!result.data) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to update user role",
          });
        }

        return result.data;
      } catch (error) {
        console.error("Role change error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user role",
        });
      }
    }),

  // Impersonate user
  impersonate: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ input }) => {
      const { userId } = input;

      // Check if target user exists
      const targetUser = await db.query.user.findFirst({
        where: eq(user.id, userId),
      });

      if (!targetUser) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Check if target user is banned
      if (targetUser.banned) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot impersonate banned user",
        });
      }

      // Use better-auth admin client to impersonate
      try {
        const result = await authClient.admin.impersonateUser({
          userId,
        });

        if (!result.data) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to impersonate user",
          });
        }

        return result.data;
      } catch (error) {
        console.error("Impersonation error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to impersonate user",
        });
      }
    }),

  // Revoke all user sessions
  revokeSessions: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ input }) => {
      const { userId } = input;

      const deletedSessions = await db
        .delete(session)
        .where(eq(session.userId, userId))
        .returning();

      return {
        revokedCount: deletedSessions.length,
      };
    }),

  // Get user activity summary
  getActivity: adminProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      const { userId } = input;

      // Get recent sessions
      const recentSessions = await db
        .select({
          id: session.id,
          createdAt: session.createdAt,
          ipAddress: session.ipAddress,
          userAgent: session.userAgent,
        })
        .from(session)
        .where(eq(session.userId, userId))
        .orderBy(desc(session.createdAt))
        .limit(10);

      // You can add more activity tracking here
      // For example: recent jobs, bids, messages, etc.

      return {
        recentSessions,
        // Add more activity data as needed
      };
    }),
});
