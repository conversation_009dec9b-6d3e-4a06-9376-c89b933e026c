// ============================================================================
// PERMISSION TYPES AND INTERFACES
// ============================================================================

/**
 * Available user roles in the system
 */
export type UserRole = "homeowner" | "contractor" | "admin";

/**
 * Available organization roles
 */
export type OrganizationRole = "owner" | "admin" | "member" | "viewer";

/**
 * Resource types that can have permissions
 */
export type ResourceType = 
  | "job" 
  | "bid" 
  | "property" 
  | "organization" 
  | "user" 
  | "message" 
  | "review" 
  | "schedule" 
  | "template"
  | "trade";

/**
 * Available actions for permissions
 */
export type PermissionAction = 
  | "create" 
  | "read" 
  | "update" 
  | "delete" 
  | "list" 
  | "manage" 
  | "invite" 
  | "approve" 
  | "reject"
  | "assign"
  | "complete";

/**
 * Permission definition
 */
export interface Permission {
  resource: ResourceType;
  action: PermissionAction;
  conditions?: PermissionCondition[];
}

/**
 * Permission condition for dynamic checking
 */
export interface PermissionCondition {
  field: string;
  operator: "eq" | "ne" | "in" | "nin" | "gt" | "gte" | "lt" | "lte" | "exists";
  value: unknown;
}

/**
 * Permission context for evaluation
 */
export interface PermissionContext {
  userId: string;
  userRole: UserRole;
  organizationId?: string;
  organizationRole?: OrganizationRole;
  resourceId?: string;
  resourceData?: Record<string, unknown>;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: UserRole | OrganizationRole;
  requiredConditions?: PermissionCondition[];
}

/**
 * Permission rule definition
 */
export interface PermissionRule {
  roles: (UserRole | OrganizationRole)[];
  permissions: Permission[];
  conditions?: PermissionCondition[];
  description?: string;
}

/**
 * Resource ownership information
 */
export interface ResourceOwnership {
  userId?: string;
  organizationId?: string;
  propertyId?: string;
  jobId?: string;
}

/**
 * Permission cache entry
 */
export interface PermissionCacheEntry {
  key: string;
  result: PermissionResult;
  expiresAt: Date;
}

/**
 * Permission middleware options
 */
export interface PermissionMiddlewareOptions {
  resource: ResourceType;
  action: PermissionAction;
  requireOwnership?: boolean;
  allowSameOrganization?: boolean;
  customCheck?: (ctx: PermissionContext) => Promise<boolean> | boolean;
  cacheKey?: string;
  cacheTTL?: number; // in seconds
}

/**
 * Bulk permission check request
 */
export interface BulkPermissionCheck {
  permissions: Array<{
    resource: ResourceType;
    action: PermissionAction;
    resourceId?: string;
  }>;
  context: PermissionContext;
}

/**
 * Bulk permission check result
 */
export interface BulkPermissionResult {
  results: Array<{
    resource: ResourceType;
    action: PermissionAction;
    resourceId?: string;
    allowed: boolean;
    reason?: string;
  }>;
  allAllowed: boolean;
}
