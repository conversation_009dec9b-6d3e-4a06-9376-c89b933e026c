# Enhanced tRPC System with Permissions

This directory contains the reorganized and enhanced tRPC setup with a comprehensive permission system. This is **Step 3** of the tRPC optimization plan: Enhanced Permission System.

## 📁 Directory Structure

```
src/lib/trpc/
├── core/                    # Core tRPC setup files
│   ├── context.ts          # tRPC context creation
│   ├── trpc.ts             # Original tRPC setup (legacy)
│   └── query-client.ts     # Client-side query client
├── middleware/              # Permission and authentication middleware
│   ├── permissions.ts      # Main permission middleware
│   ├── permission-rules.ts # Permission rules configuration
│   └── permission-utils.ts # Permission checking utilities
├── procedures/              # Enhanced procedure builders
│   └── index.ts            # Permission-based procedures
├── types/                   # TypeScript type definitions
│   └── permissions.ts      # Permission-related types
├── routers/                 # API route handlers (existing)
├── schemas/                 # Reusable Zod schemas (existing)
├── utils/                   # Utility functions (existing)
├── index.ts                # Main tRPC router export
└── README.md               # This file
```

## 🔐 Permission System Overview

The new permission system provides:

### 1. **Role-Based Access Control (RBAC)**

- **Admin**: Full access to all resources
- **Homeowner**: Can manage their properties and jobs
- **Contractor**: Can bid on jobs and manage their organization

### 2. **Organization-Level Permissions**

- **Owner**: Full control over organization
- **Admin**: Can manage organization and members
- **Member**: Can create bids and manage work
- **Viewer**: Read-only access

### 3. **Resource-Based Permissions**

- Automatic ownership checking
- Same-organization access control
- Custom permission logic support

## 🚀 Quick Start

### Using Pre-built Permission Procedures

```typescript
import { router, propertyProcedures } from "../procedures";

export const myRouter = router({
  // Automatically checks if user can create properties
  create: propertyProcedures.create
    .input(propertyCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Permission already verified - implement logic
    }),

  // Automatically checks ownership before allowing updates
  update: propertyProcedures.update
    .input(updateSchema)
    .mutation(async ({ input }) => {
      // User ownership already verified
    }),
});
```

### Creating Custom Permission Procedures

```typescript
import { createPermissionProcedure } from "../procedures";

const customProcedure = createPermissionProcedure("job", "read", {
  requireOwnership: true,
  allowSameOrganization: true,
});
```

### Role-Based Procedures

```typescript
import { adminProcedure, contractorProcedure } from "../procedures";

export const adminRouter = router({
  // Only admins can access
  deleteUser: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ input }) => {
      // Admin-only logic
    }),

  // Only contractors can access
  createBid: contractorProcedure
    .input(bidCreateSchema)
    .mutation(async ({ input }) => {
      // Contractor-only logic
    }),
});
```

## 📋 Available Procedures

### Resource-Specific Procedures

Each resource type has pre-built procedures:

```typescript
// Job procedures
jobProcedures.create    // Can create jobs
jobProcedures.read      // Can read job details
jobProcedures.update    // Can update (with ownership check)
jobProcedures.delete    // Can delete (with ownership check)
jobProcedures.list      // Can list jobs
jobProcedures.manage    // Can manage job (owner only)

// Bid procedures
bidProcedures.create    // Can create bids
bidProcedures.approve   // Can approve bids
bidProcedures.reject    // Can reject bids

// Property procedures
propertyProcedures.create
propertyProcedures.read
propertyProcedures.update
propertyProcedures.delete
propertyProcedures.list

// Organization procedures
organizationProcedures.create
organizationProcedures.manage
organizationProcedures.invite  // Can invite members
```

### Role-Based Procedures

```typescript
adminProcedure           // Admin only
contractorProcedure      // Contractor only
homeownerProcedure       // Homeowner only
organizationOwnerProcedure    // Organization owner only
organizationAdminProcedure    // Organization admin or owner
```

## 🛠 Permission Rules

### User Roles

| Role | Description | Permissions |
|------|-------------|-------------|
| `admin` | System administrator | Full access to everything |
| `homeowner` | Property owner | Manage properties, create jobs, approve bids |
| `contractor` | Service provider | Bid on jobs, manage organization |

### Organization Roles

| Role | Description | Permissions |
|------|-------------|-------------|
| `owner` | Organization owner | Full organization control |
| `admin` | Organization admin | Manage members and bids |
| `member` | Organization member | Create and manage bids |
| `viewer` | Read-only access | View organization data |

## 📊 Permission Checking

### Automatic Ownership Verification

The system automatically checks resource ownership:

```typescript
// This procedure automatically verifies the user owns the property
propertyProcedures.update
  .input(updateSchema)
  .mutation(async ({ input }) => {
    // No manual ownership check needed!
    // Middleware already verified ownership
  });
```

### Custom Permission Logic

```typescript
const customProcedure = createPermissionProcedure("job", "read", {
  customCheck: async (ctx) => {
    // Custom permission logic
    return ctx.userRole === "admin" ||
           (ctx.organizationId && await checkSpecialAccess(ctx.organizationId));
  },
});
```

## 🔄 Migration Guide

### From Old System

**Before:**

```typescript
export const oldRouter = router({
  update: protectedProcedure
    .input(schema)
    .mutation(async ({ input, ctx }) => {
      // Manual permission checking
      if (!ctx.userId) throw new TRPCError({ code: "UNAUTHORIZED" });

      const resource = await db.query.resource.findFirst({
        where: eq(resource.id, input.id)
      });

      if (resource.userId !== ctx.userId) {
        throw new TRPCError({ code: "FORBIDDEN" });
      }

      // Logic here...
    }),
});
```

**After:**

```typescript
export const newRouter = router({
  update: resourceProcedures.update
    .input(schema)
    .mutation(async ({ input }) => {
      // Permission automatically checked!
      // Logic here...
    }),
});
```

## 🎯 Best Practices

### 1. Use Pre-built Procedures

Always prefer pre-built permission procedures over manual checks:

```typescript
// ✅ Good
bidProcedures.create.mutation(async ({ input }) => { ... });

// ❌ Avoid
protectedProcedure.mutation(async ({ input, ctx }) => {
  // Manual permission checking...
});
```

### 2. Leverage Standardized Schemas

Use schemas from the `schemas/` directory:

```typescript
// ✅ Good
.input(propertyCreateSchema)

// ❌ Avoid
.input(z.object({ name: z.string(), ... }))
```

### 3. Handle Permissions at Procedure Level

Don't mix permission logic with business logic:

```typescript
// ✅ Good - Permission in procedure definition
const myProcedure = createPermissionProcedure("job", "update", {
  requireOwnership: true
});

// ❌ Avoid - Permission mixed with business logic
protectedProcedure.mutation(async ({ input, ctx }) => {
  if (!canUpdate(ctx.userId, input.id)) throw error;
  // Business logic...
});
```

## 🚨 Error Handling

The permission system automatically throws appropriate tRPC errors:

- `UNAUTHORIZED` - User not authenticated
- `FORBIDDEN` - User lacks required permissions
- `NOT_FOUND` - Resource doesn't exist or user can't access it

## 📈 Performance

### Permission Caching

The system includes built-in permission caching:

```typescript
// Permissions are cached for 5 minutes by default
const result = await checkPermission(ctx, "job", "read", jobId);
```

### Cache Management

```typescript
// Clear cache when user permissions change
clearUserPermissionCache(userId);
```

## 🔍 Debugging

Enable permission debugging by checking the permission context:

```typescript
.mutation(async ({ ctx }) => {
  console.log("Permission context:", ctx.permissionContext);
  // Shows user role, organization info, etc.
});
```

## 📝 Examples

See `routers/properties-enhanced.ts` for a complete example of how to use the new permission system.

## 🔄 Next Steps

This completes **Step 3** of the tRPC optimization plan. The remaining steps are:

- **Step 4**: Standardized error handling
- **Step 5**: Generic CRUD builders

The permission system is now ready for use across all routers!
