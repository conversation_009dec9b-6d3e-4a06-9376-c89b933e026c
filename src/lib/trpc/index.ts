import { createContext } from "./core/context";
import { router } from "./procedures";
import { accountsRouter } from "./routers/accounts";
import { adminRouter } from "./routers/admin";
import { adminUsersRouter } from "./routers/admin-users";
import { bidsRouter } from "./routers/bids";
import { contractorRouter } from "./routers/contractor";
import { dashboardRouter } from "./routers/dashboard";
import { messagesRouter } from "./routers/messages";
import { projectsRouter } from "./routers/projects";
import { propertiesRouter } from "./routers/properties";
import { reviewsRouter } from "./routers/reviews";
import { schedulesRouter } from "./routers/schedules";
import { templatesRouter } from "./routers/templates";
import { tradesRouter } from "./routers/trades";
import { usersRouter } from "./routers/users";

export const appRouter = router({
  admin: adminRouter,
  adminUsers: adminUsersRouter,
  bids: bidsRouter,
  accounts: accountsRouter,
  trades: tradesRouter,
  contractor: contractorRouter,
  dashboard: dashboardRouter,
  projects: projectsRouter,
  properties: propertiesRouter,
  schedules: schedulesRouter,
  messages: messagesRouter,
  templates: templatesRouter,
  reviews: reviewsRouter,
  users: usersRouter,
});

export type AppRouter = typeof appRouter;

export const caller = appRouter.createCaller(createContext);
