import { tool } from "ai";
import { ilike } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { trade } from "@/db/schema";

export const createTrade = () =>
  tool({
    description: "Create a new contractor trade if one does not already exist.",
    parameters: z.object({
      name: z.string().describe("The name of the trade"),
    }),
    execute: async ({ name }) => {
      const [newTrade] = await db.insert(trade).values({ name }).returning();

      if (!newTrade) {
        return "Failed to create trade";
      }

      return "Trade created";
    },
  });

export const findTradeId = () =>
  tool({
    description: "Find the ID of a trade by name",
    parameters: z.object({
      name: z.string().describe("The name of the trade"),
    }),
    execute: async ({ name }) => {
      const tradeData = await db.query.trade.findFirst({
        where: ilike(trade.name, name),
      });

      if (!tradeData) {
        return "Trade not found";
      }

      return tradeData.id;
    },
  });
