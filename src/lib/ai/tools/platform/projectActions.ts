import { tool } from "ai";
import { and, eq } from "drizzle-orm";
import { headers } from "next/headers";
import { z } from "zod";
import { db } from "@/db";
import { bid, job } from "@/db/schema";
import { auth } from "@/lib/auth";

// Tool to publish a draft project
export function publishProject() {
  return tool({
    description:
      "Publish a draft project to make it visible to contractors for bidding",
    parameters: z.object({
      projectId: z.string().describe("The ID of the draft project to publish"),
      reason: z.string().optional().describe("Optional reason for publishing"),
    }),
    execute: async ({ projectId, reason }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Verify the project belongs to the user and is in draft status
        const project = await db.query.job.findFirst({
          where: and(eq(job.id, projectId), eq(job.status, "DRAFT")),
          with: {
            property: true,
          },
        });

        if (!project || project.property.userId !== session.user.id) {
          return {
            success: false,
            error: "Project not found or you don't have permission",
          };
        }

        // Update project status to published
        await db
          .update(job)
          .set({
            status: "PUBLISHED",
            updatedAt: new Date(),
          })
          .where(eq(job.id, projectId));

        return {
          success: true,
          message: `Project "${project.name}" has been successfully published! Contractors can now submit bids.`,
          projectId,
          projectName: project.name,
        };
      } catch (error) {
        console.error("Error publishing project:", error);
        return {
          success: false,
          error: "Failed to publish project. Please try again.",
        };
      }
    },
  });
}

// Tool to accept a bid
export function acceptBid() {
  return tool({
    description: "Accept a contractor's bid for a project",
    parameters: z.object({
      bidId: z.string().describe("The ID of the bid to accept"),
      reason: z
        .string()
        .optional()
        .describe("Optional reason for accepting this bid"),
    }),
    execute: async ({ bidId, reason }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Get the bid and verify ownership
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            job: true,
            organization: true,
          },
        });

        if (!bidData) {
          return { success: false, error: "Bid not found" };
        }

        // Get the job with property to check ownership
        const jobWithProperty = await db.query.job.findFirst({
          where: eq(job.id, bidData.jobId),
          with: {
            property: true,
          },
        });

        if (
          !jobWithProperty ||
          jobWithProperty.property.userId !== session.user.id
        ) {
          return {
            success: false,
            error: "You don't have permission to accept this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return {
            success: false,
            error: "This bid is no longer available for acceptance",
          };
        }

        // Accept the bid and update project status
        await db.transaction(async (tx) => {
          // Update the accepted bid
          await tx
            .update(bid)
            .set({
              status: "ACCEPTED",
              updatedAt: new Date(),
            })
            .where(eq(bid.id, bidId));

          // Update project status to awarded
          await tx
            .update(job)
            .set({
              status: "AWARDED",
              updatedAt: new Date(),
            })
            .where(eq(job.id, bidData.jobId));

          // Reject all other bids for this project
          await tx
            .update(bid)
            .set({
              status: "REJECTED",
              updatedAt: new Date(),
            })
            .where(
              and(eq(bid.jobId, bidData.jobId), eq(bid.status, "PROPOSED")),
            );
        });

        return {
          success: true,
          message: `Bid from ${bidData.organization.name} has been accepted! Your project has been awarded and work can begin.`,
          bidId,
          contractorName: bidData.organization.name,
          bidAmount: bidData.amount,
          projectName: bidData.job.name,
        };
      } catch (error) {
        console.error("Error accepting bid:", error);
        return {
          success: false,
          error: "Failed to accept bid. Please try again.",
        };
      }
    },
  });
}

// Tool to schedule a project
export function scheduleProject() {
  return tool({
    description: "Schedule a start date for an awarded project",
    parameters: z.object({
      projectId: z.string().describe("The ID of the project to schedule"),
      startDate: z.string().describe("The start date in YYYY-MM-DD format"),
      notes: z.string().optional().describe("Optional scheduling notes"),
    }),
    execute: async ({ projectId, startDate, notes }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Verify the project belongs to the user and is awarded
        const project = await db.query.job.findFirst({
          where: and(eq(job.id, projectId), eq(job.status, "AWARDED")),
          with: {
            property: true,
          },
        });

        if (!project || project.property.userId !== session.user.id) {
          return {
            success: false,
            error: "Project not found or you don't have permission",
          };
        }

        // Validate date
        const scheduledDate = new Date(startDate);
        if (Number.isNaN(scheduledDate.getTime())) {
          return { success: false, error: "Invalid date format" };
        }

        if (scheduledDate < new Date()) {
          return { success: false, error: "Start date cannot be in the past" };
        }

        // Update project with scheduled start date
        await db
          .update(job)
          .set({
            startsAt: scheduledDate,
            updatedAt: new Date(),
          })
          .where(eq(job.id, projectId));

        return {
          success: true,
          message: `Project "${project.name}" has been scheduled to start on ${scheduledDate.toLocaleDateString()}!`,
          projectId,
          projectName: project.name,
          startDate: scheduledDate.toISOString(),
          notes,
        };
      } catch (error) {
        console.error("Error scheduling project:", error);
        return {
          success: false,
          error: "Failed to schedule project. Please try again.",
        };
      }
    },
  });
}

// Tool to mark project as complete
export function completeProject() {
  return tool({
    description: "Mark a project as completed",
    parameters: z.object({
      projectId: z
        .string()
        .describe("The ID of the project to mark as complete"),
      completionNotes: z
        .string()
        .optional()
        .describe("Optional completion notes"),
    }),
    execute: async ({ projectId, completionNotes }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Verify the project belongs to the user
        const project = await db.query.job.findFirst({
          where: eq(job.id, projectId),
          with: {
            property: true,
          },
        });

        if (!project || project.property.userId !== session.user.id) {
          return {
            success: false,
            error: "Project not found or you don't have permission",
          };
        }

        if (project.status === "COMPLETED") {
          return {
            success: false,
            error: "Project is already marked as completed",
          };
        }

        // Update project status to completed
        await db
          .update(job)
          .set({
            status: "COMPLETED",
            updatedAt: new Date(),
          })
          .where(eq(job.id, projectId));

        return {
          success: true,
          message: `Project "${project.name}" has been marked as completed! You can now leave a review for the contractor.`,
          projectId,
          projectName: project.name,
          completionNotes,
        };
      } catch (error) {
        console.error("Error completing project:", error);
        return {
          success: false,
          error: "Failed to complete project. Please try again.",
        };
      }
    },
  });
}
