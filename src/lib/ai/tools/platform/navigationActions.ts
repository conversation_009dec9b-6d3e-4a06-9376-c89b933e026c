import { tool } from "ai";
import { z } from "zod";

// Tool to help users navigate to specific pages
export function navigateToPage() {
  return tool({
    description:
      "Provide navigation instructions to help users get to specific pages in the application",
    parameters: z.object({
      destination: z
        .enum([
          "dashboard",
          "projects",
          "new-project",
          "properties",
          "new-property",
          "bids",
          "contractors",
          "profile",
          "help",
          "calendar",
        ])
        .describe("The page the user wants to navigate to"),
      context: z
        .string()
        .optional()
        .describe("Additional context about what they want to do"),
    }),
    execute: async ({ destination, context }) => {
      const navigationMap = {
        dashboard: {
          url: "/dashboard",
          description:
            "Your main dashboard with project overview and quick actions",
          instructions:
            "Click on 'Dashboard' in the sidebar or navigate to /dashboard",
        },
        projects: {
          url: "/projects",
          description: "View and manage all your projects",
          instructions:
            "Click on 'Projects' in the sidebar or navigate to /projects",
        },
        "new-project": {
          url: "/projects/new",
          description: "Create a new home improvement project",
          instructions:
            "Go to Projects → Click 'New Project' button or navigate to /projects/new",
        },
        properties: {
          url: "/properties",
          description: "Manage your properties and their details",
          instructions:
            "Click on 'Properties' in the sidebar or navigate to /properties",
        },
        "new-property": {
          url: "/properties/new",
          description: "Add a new property to your account",
          instructions: "Go to Properties → Click 'Add Property' button",
        },
        bids: {
          url: "/bids",
          description: "View and manage project bids",
          instructions: "Click on 'Bids' in the sidebar or navigate to /bids",
        },
        contractors: {
          url: "/contractors",
          description: "Browse and find qualified contractors",
          instructions:
            "Click on 'Contractors' in the sidebar or navigate to /contractors",
        },
        profile: {
          url: "/profile",
          description: "Manage your account settings and profile",
          instructions:
            "Click on your profile picture → 'Profile' or navigate to /profile",
        },
        help: {
          url: "/help",
          description: "Get help and support documentation",
          instructions: "Click on 'Help' in the sidebar or navigate to /help",
        },
        calendar: {
          url: "/calendar",
          description: "View your project schedule and appointments",
          instructions:
            "Click on 'Calendar' in the sidebar or navigate to /calendar",
        },
      };

      const nav = navigationMap[destination];

      return {
        success: true,
        destination,
        url: nav.url,
        description: nav.description,
        instructions: nav.instructions,
        context,
        message: `To ${nav.description.toLowerCase()}, ${nav.instructions}${context ? `. ${context}` : ""}`,
      };
    },
  });
}

// Tool to search for projects, contractors, or other content
export function searchPlatform() {
  return tool({
    description:
      "Help users search for projects, contractors, or other content on the platform",
    parameters: z.object({
      query: z.string().describe("The search query"),
      type: z
        .enum(["projects", "contractors", "properties", "all"])
        .describe("What type of content to search for"),
      filters: z
        .object({
          location: z.string().optional().describe("Filter by location"),
          priceRange: z
            .object({
              min: z.number().optional(),
              max: z.number().optional(),
            })
            .optional()
            .describe("Filter by price range"),
          category: z
            .string()
            .optional()
            .describe("Filter by project category or contractor specialty"),
        })
        .optional()
        .describe("Additional search filters"),
    }),
    execute: async ({ query, type, filters }) => {
      // This tool provides search guidance rather than actual search results
      // In a real implementation, this would integrate with the search API

      const searchInstructions = {
        projects: {
          description: "Search for projects",
          instructions: [
            "Go to the Projects page (/projects)",
            "Use the search bar at the top to enter your query",
            "Apply filters for location, budget, or category as needed",
            "Browse the results and click on projects for more details",
          ],
        },
        contractors: {
          description: "Search for contractors",
          instructions: [
            "Go to the Contractors page (/contractors)",
            "Use the search and filter options to find contractors",
            "Filter by specialty, location, rating, or availability",
            "View contractor profiles and previous work",
          ],
        },
        properties: {
          description: "Search your properties",
          instructions: [
            "Go to the Properties page (/properties)",
            "Use the search to find specific properties",
            "Filter by property type, location, or status",
          ],
        },
        all: {
          description: "Search across the platform",
          instructions: [
            "Use the global search bar in the top navigation",
            "Search results will include projects, contractors, and properties",
            "Use the filter tabs to narrow down results by type",
          ],
        },
      };

      const searchGuide = searchInstructions[type];

      return {
        success: true,
        query,
        searchType: type,
        description: searchGuide.description,
        instructions: searchGuide.instructions,
        filters,
        message: `To ${searchGuide.description} for "${query}", follow these steps:\n${searchGuide.instructions.map((step, i) => `${i + 1}. ${step}`).join("\n")}`,
        suggestedFilters: filters
          ? `Applied filters: ${JSON.stringify(filters, null, 2)}`
          : "No filters specified",
      };
    },
  });
}

// Tool to provide quick actions and shortcuts
export function getQuickActions() {
  return tool({
    description:
      "Provide quick action suggestions based on user role and current context",
    parameters: z.object({
      userRole: z
        .enum(["homeowner", "contractor", "admin"])
        .describe("The user's role"),
      currentPage: z
        .string()
        .optional()
        .describe("The current page the user is on"),
    }),
    execute: async ({ userRole, currentPage }) => {
      const quickActions = {
        homeowner: [
          {
            action: "Create New Project",
            description: "Start a new home improvement project",
            url: "/projects/new",
            icon: "plus",
          },
          {
            action: "View Active Projects",
            description: "Check status of your ongoing projects",
            url: "/projects?status=active",
            icon: "activity",
          },
          {
            action: "Review Pending Bids",
            description: "Evaluate contractor bids on your projects",
            url: "/bids?status=pending",
            icon: "users",
          },
          {
            action: "Find Contractors",
            description: "Browse qualified contractors in your area",
            url: "/contractors",
            icon: "search",
          },
          {
            action: "Add Property",
            description: "Add a new property to your account",
            url: "/properties/new",
            icon: "home",
          },
        ],
        contractor: [
          {
            action: "Find New Jobs",
            description: "Browse available projects to bid on",
            url: "/projects?status=published",
            icon: "briefcase",
          },
          {
            action: "Manage Active Bids",
            description: "Track your submitted bids",
            url: "/bids?type=submitted",
            icon: "clock",
          },
          {
            action: "Update Profile",
            description: "Keep your contractor profile current",
            url: "/contractors/profile",
            icon: "user",
          },
          {
            action: "View Performance",
            description: "Check your bidding success and ratings",
            url: "/dashboard/performance",
            icon: "trending-up",
          },
          {
            action: "Manage Calendar",
            description: "Schedule and track your project work",
            url: "/calendar",
            icon: "calendar",
          },
        ],
        admin: [
          {
            action: "Platform Overview",
            description: "View platform metrics and health",
            url: "/admin/dashboard",
            icon: "bar-chart",
          },
          {
            action: "Manage Users",
            description: "User administration and support",
            url: "/admin/users",
            icon: "users",
          },
          {
            action: "Review Projects",
            description: "Monitor project activity and issues",
            url: "/admin/projects",
            icon: "folder",
          },
          {
            action: "System Settings",
            description: "Configure platform settings",
            url: "/admin/settings",
            icon: "settings",
          },
        ],
      };

      const actions = quickActions[userRole] || [];

      return {
        success: true,
        userRole,
        currentPage,
        quickActions: actions,
        message: `Here are the most common actions for ${userRole}s:`,
        actionCount: actions.length,
      };
    },
  });
}
