import { tool } from "ai";
import { and, eq } from "drizzle-orm";
import { headers } from "next/headers";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, membership } from "@/db/schema";
import { auth } from "@/lib/auth";

// Tool to submit a bid on a project
export function submitBid() {
  return tool({
    description: "Submit a bid on a published project as a contractor",
    parameters: z.object({
      projectId: z.string().describe("The ID of the project to bid on"),
      amount: z.number().describe("The bid amount in dollars"),
      estimatedDuration: z.number().describe("Estimated duration in days"),
      proposal: z
        .string()
        .describe("Detailed proposal explaining the approach"),
      materials: z
        .string()
        .optional()
        .describe("Materials and equipment needed"),
    }),
    execute: async ({
      projectId,
      amount,
      estimatedDuration,
      proposal,
      materials,
    }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        if (session.user.role !== "contractor") {
          return { success: false, error: "Only contractors can submit bids" };
        }

        // Get user's organization through membership table
        const userMembership = await db.query.membership.findFirst({
          where: eq(membership.userId, session.user.id),
          with: {
            organization: true,
          },
        });

        const userOrg = userMembership?.organization;

        if (!userOrg) {
          return {
            success: false,
            error:
              "You need to create a contractor profile before submitting bids",
          };
        }

        // Verify the project exists and is published
        const project = await db.query.job.findFirst({
          where: and(eq(job.id, projectId), eq(job.status, "PUBLISHED")),
        });

        if (!project) {
          return {
            success: false,
            error: "Project not found or not available for bidding",
          };
        }

        // Check if contractor already has a bid on this project
        const existingBid = await db.query.bid.findFirst({
          where: and(
            eq(bid.jobId, projectId),
            eq(bid.organizationId, userOrg.id),
          ),
        });

        if (existingBid) {
          return {
            success: false,
            error: "You have already submitted a bid for this project",
          };
        }

        // Validate bid amount
        if (amount <= 0) {
          return { success: false, error: "Bid amount must be greater than 0" };
        }

        if (estimatedDuration <= 0) {
          return {
            success: false,
            error: "Estimated duration must be greater than 0",
          };
        }

        // Create the bid
        const newBid = await db
          .insert(bid)
          .values({
            name: `Bid for ${project.name}`, // Add required name field
            jobId: projectId,
            organizationId: userOrg.id,
            amount,
            estimatedDuration,
            description: proposal, // Use description field instead of proposal
            status: "PROPOSED", // Use correct status from schema
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();

        return {
          success: true,
          message: `Your bid of $${amount.toLocaleString()} has been successfully submitted for "${project.name}"! The homeowner will review your proposal.`,
          bidId: newBid[0]?.id || "unknown",
          projectName: project.name,
          bidAmount: amount,
          estimatedDuration,
        };
      } catch (error) {
        console.error("Error submitting bid:", error);
        return {
          success: false,
          error: "Failed to submit bid. Please try again.",
        };
      }
    },
  });
}

// Tool to withdraw a bid
export function withdrawBid() {
  return tool({
    description: "Withdraw a previously submitted bid",
    parameters: z.object({
      bidId: z.string().describe("The ID of the bid to withdraw"),
      reason: z.string().optional().describe("Optional reason for withdrawing"),
    }),
    execute: async ({ bidId, reason }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Get the bid and verify ownership
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            job: true,
            organization: true,
          },
        });

        if (!bidData) {
          return { success: false, error: "Bid not found" };
        }

        // Check if user owns this organization through membership
        const userMembership = await db.query.membership.findFirst({
          where: and(
            eq(membership.userId, session.user.id),
            eq(membership.organizationId, bidData.organizationId),
          ),
        });

        if (!userMembership) {
          return {
            success: false,
            error: "You don't have permission to withdraw this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return { success: false, error: "This bid cannot be withdrawn" };
        }

        // Withdraw the bid
        await db
          .update(bid)
          .set({
            status: "WITHDRAWN",
            updatedAt: new Date(),
          })
          .where(eq(bid.id, bidId));

        return {
          success: true,
          message: `Your bid for "${bidData.job.name}" has been withdrawn successfully.`,
          bidId,
          projectName: bidData.job.name,
          reason,
        };
      } catch (error) {
        console.error("Error withdrawing bid:", error);
        return {
          success: false,
          error: "Failed to withdraw bid. Please try again.",
        };
      }
    },
  });
}

// Tool to update a bid (if still pending)
export function updateBid() {
  return tool({
    description: "Update a pending bid with new amount or proposal",
    parameters: z.object({
      bidId: z.string().describe("The ID of the bid to update"),
      amount: z.number().optional().describe("New bid amount in dollars"),
      estimatedDuration: z
        .number()
        .optional()
        .describe("New estimated duration in days"),
      proposal: z.string().optional().describe("Updated proposal"),
      materials: z.string().optional().describe("Updated materials list"),
    }),
    execute: async ({
      bidId,
      amount,
      estimatedDuration,
      proposal,
      materials,
    }) => {
      try {
        const session = await auth.api.getSession({ headers: await headers() });
        if (!session?.user) {
          return { success: false, error: "User not authenticated" };
        }

        // Get the bid and verify ownership
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            job: true,
            organization: true,
          },
        });

        if (!bidData) {
          return { success: false, error: "Bid not found" };
        }

        // Check if user owns this organization through membership
        const userMembership = await db.query.membership.findFirst({
          where: and(
            eq(membership.userId, session.user.id),
            eq(membership.organizationId, bidData.organizationId),
          ),
        });

        if (!userMembership) {
          return {
            success: false,
            error: "You don't have permission to update this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return { success: false, error: "This bid cannot be updated" };
        }

        // Prepare update data
        const updateData: any = { updatedAt: new Date() };

        if (amount !== undefined) {
          if (amount <= 0) {
            return {
              success: false,
              error: "Bid amount must be greater than 0",
            };
          }
          updateData.amount = amount;
        }

        if (estimatedDuration !== undefined) {
          if (estimatedDuration <= 0) {
            return {
              success: false,
              error: "Estimated duration must be greater than 0",
            };
          }
          updateData.estimatedDuration = estimatedDuration;
        }

        if (proposal !== undefined) {
          updateData.description = proposal; // Use description field
        }

        if (materials !== undefined) {
          updateData.materials = materials;
        }

        // Update the bid
        await db.update(bid).set(updateData).where(eq(bid.id, bidId));

        return {
          success: true,
          message: `Your bid for "${bidData.job.name}" has been updated successfully!`,
          bidId,
          projectName: bidData.job.name,
          updatedFields: Object.keys(updateData).filter(
            (key) => key !== "updatedAt",
          ),
        };
      } catch (error) {
        console.error("Error updating bid:", error);
        return {
          success: false,
          error: "Failed to update bid. Please try again.",
        };
      }
    },
  });
}
