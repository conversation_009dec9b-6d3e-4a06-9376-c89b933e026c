import { tool } from "ai";
import type { InferInsertModel } from "drizzle-orm";
import { ilike } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { jobTemplate, templateTask, trade } from "@/db/schema";

export const createTemplate = () =>
  tool({
    description:
      "Create a template for a home project to be used for future projects",
    parameters: z.object({
      name: z.string().describe("The name of the project"),
      budget: z.number().describe("The budget for the project"),
      estimatedDuration: z
        .number()
        .describe("The estimated duration of the project"),
      description: z.string().describe("A description of the project"),
      tasks: z
        .object({
          name: z.string().describe("The name of the task"),
          tradeName: z.string().describe("The name of the trade"),
        })
        .array()
        .describe("The tasks for the project"),
    }),
    execute: async ({
      name,
      budget,
      estimatedDuration,
      description,
      tasks,
    }) => {
      const data: InferInsertModel<typeof jobTemplate> = {
        name,
        budget,
        estimatedDuration,
        description,
      };

      const [newJob] = await db.insert(jobTemplate).values(data).returning();

      if (!newJob) {
        return "Failed to create project";
      }

      for (const task of tasks) {
        const tradeData = await db.query.trade.findFirst({
          where: ilike(trade.name, task.tradeName),
        });

        if (!tradeData) {
          return `Trade ${task.tradeName} not found`;
        }

        await db.insert(templateTask).values({
          name: task.name,
          tradeId: tradeData.id,
          templateId: newJob.id,
        });
      }

      return "Template created";
    },
  });
