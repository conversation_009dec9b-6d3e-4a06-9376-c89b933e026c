import { tool } from "ai";
import { count, eq, gte } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, organization, user } from "@/db/schema";

export const getUserStats = () =>
  tool({
    description:
      "Get comprehensive user and platform statistics for admin dashboard",
    parameters: z.object({
      timeframe: z
        .enum(["7d", "30d", "90d", "1y", "all"])
        .optional()
        .default("30d")
        .describe("Time frame for statistics"),
    }),
    execute: async ({ timeframe }) => {
      const now = new Date();
      let startDate: Date;

      switch (timeframe) {
        case "7d":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "30d":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case "90d":
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case "1y":
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0); // All time
      }

      try {
        const [
          totalUsers,
          totalAdmins,
          totalHomeowners,
          totalContractors,
          verifiedUsers,
          bannedUsers,
          twoFactorUsers,
          recentUsers,
          totalJobs,
          activeJobs,
          completedJobs,
          totalBids,
          totalOrganizations,
        ] = await Promise.all([
          db.$count(user),
          db.$count(user, eq(user.role, "admin")),
          db.$count(user, eq(user.role, "homeowner")),
          db.$count(user, eq(user.role, "contractor")),
          db.$count(user, eq(user.emailVerified, true)),
          db.$count(user, eq(user.banned, true)),
          db.$count(user, eq(user.twoFactorEnabled, true)),
          db.$count(user, gte(user.createdAt, startDate)),
          db.$count(job),
          db.$count(job, eq(job.status, "PUBLISHED")),
          db.$count(job, eq(job.status, "COMPLETED")),
          db.$count(bid),
          db.$count(organization),
        ]);

        // Get user growth data
        const userGrowthData = await db
          .select({
            date: user.createdAt,
            role: user.role,
          })
          .from(user)
          .where(gte(user.createdAt, startDate))
          .orderBy(user.createdAt);

        // Get job status distribution
        const jobStatusData = await db
          .select({
            status: job.status,
            count: count(job.id),
          })
          .from(job)
          .groupBy(job.status);

        const stats = {
          users: {
            total: totalUsers,
            admins: totalAdmins,
            homeowners: totalHomeowners,
            contractors: totalContractors,
            verified: verifiedUsers,
            banned: bannedUsers,
            twoFactorEnabled: twoFactorUsers,
            recentSignups: recentUsers,
          },
          platform: {
            totalJobs,
            activeJobs,
            completedJobs,
            totalBids,
            totalOrganizations,
          },
          growth: {
            timeframe,
            userGrowth: userGrowthData,
            jobStatusDistribution: jobStatusData,
          },
          healthMetrics: {
            verificationRate:
              totalUsers > 0 ? (verifiedUsers / totalUsers) * 100 : 0,
            banRate: totalUsers > 0 ? (bannedUsers / totalUsers) * 100 : 0,
            twoFactorAdoption:
              totalUsers > 0 ? (twoFactorUsers / totalUsers) * 100 : 0,
            jobCompletionRate:
              totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0,
          },
        };

        return JSON.stringify(stats, null, 2);
      } catch (error) {
        console.error("Error fetching user stats:", error);
        return "Failed to fetch user statistics. Please try again.";
      }
    },
  });
