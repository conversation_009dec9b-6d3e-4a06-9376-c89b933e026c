import { tool } from "ai";
import { eq, ilike, or } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { user } from "@/db/schema";
import { authClient } from "@/lib/auth-client";

export const searchUsers = () =>
  tool({
    description:
      "Search for users by name, email, or role for admin management",
    parameters: z.object({
      query: z.string().optional().describe("Search query for name or email"),
      role: z
        .enum(["admin", "homeowner", "contractor"])
        .optional()
        .describe("Filter by user role"),
      limit: z
        .number()
        .optional()
        .default(10)
        .describe("Maximum number of results to return"),
    }),
    execute: async ({ query, role, limit }) => {
      try {
        let whereCondition;

        if (query && role) {
          whereCondition = or(
            ilike(user.name, `%${query}%`),
            ilike(user.email, `%${query}%`),
            eq(user.role, role),
          );
        } else if (query) {
          whereCondition = or(
            ilike(user.name, `%${query}%`),
            ilike(user.email, `%${query}%`),
          );
        } else if (role) {
          whereCondition = eq(user.role, role);
        }

        const users = await db.query.user.findMany({
          where: whereCondition,
          limit,
          columns: {
            id: true,
            name: true,
            email: true,
            role: true,
            emailVerified: true,
            banned: true,
            banReason: true,
            twoFactorEnabled: true,
            createdAt: true,
            onboardingComplete: true,
          },
        });

        return JSON.stringify(users, null, 2);
      } catch (error) {
        console.error("Error searching users:", error);
        return "Failed to search users. Please try again.";
      }
    },
  });

export const changeUserRole = () =>
  tool({
    description: "Change a user's role (admin only)",
    parameters: z.object({
      userId: z.string().describe("The ID of the user to modify"),
      newRole: z
        .enum(["admin", "homeowner", "contractor"])
        .describe("The new role to assign"),
      reason: z.string().optional().describe("Reason for the role change"),
    }),
    execute: async ({ userId, newRole, reason }) => {
      try {
        // First, get the current user info
        const currentUser = await db.query.user.findFirst({
          where: eq(user.id, userId),
          columns: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        });

        if (!currentUser) {
          return `User with ID ${userId} not found.`;
        }

        // Use better-auth admin client to change role
        const result = await authClient.admin.setRole({
          userId,
          role: newRole,
        });

        if (!result.data) {
          return "Failed to update user role. Please check permissions and try again.";
        }

        const logMessage = reason
          ? `Role changed from ${currentUser.role} to ${newRole}. Reason: ${reason}`
          : `Role changed from ${currentUser.role} to ${newRole}`;

        return `Successfully updated ${currentUser.name} (${currentUser.email}): ${logMessage}`;
      } catch (error) {
        console.error("Error changing user role:", error);
        return "Failed to change user role. Please check permissions and try again.";
      }
    },
  });

export const banUser = () =>
  tool({
    description: "Ban or unban a user (admin only)",
    parameters: z.object({
      userId: z.string().describe("The ID of the user to ban/unban"),
      action: z
        .enum(["ban", "unban"])
        .describe("Whether to ban or unban the user"),
      reason: z
        .string()
        .optional()
        .describe("Reason for banning (required for ban action)"),
      duration: z
        .number()
        .optional()
        .describe(
          "Ban duration in days (optional, permanent if not specified)",
        ),
    }),
    execute: async ({ userId, action, reason, duration }) => {
      try {
        // Get the current user info
        const currentUser = await db.query.user.findFirst({
          where: eq(user.id, userId),
          columns: {
            id: true,
            name: true,
            email: true,
            banned: true,
          },
        });

        if (!currentUser) {
          return `User with ID ${userId} not found.`;
        }

        if (action === "ban") {
          if (!reason) {
            return "A reason is required when banning a user.";
          }

          const banExpires = duration
            ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000)
            : null;

          await db
            .update(user)
            .set({
              banned: true,
              banReason: reason,
              banExpires,
            })
            .where(eq(user.id, userId));

          const durationText = duration
            ? ` for ${duration} days`
            : " permanently";
          return `Successfully banned ${currentUser.name} (${currentUser.email})${durationText}. Reason: ${reason}`;
        }
        // Unban user
        await db
          .update(user)
          .set({
            banned: false,
            banReason: null,
            banExpires: null,
          })
          .where(eq(user.id, userId));

        return `Successfully unbanned ${currentUser.name} (${currentUser.email}).`;
      } catch (error) {
        console.error("Error banning/unbanning user:", error);
        return "Failed to ban/unban user. Please check permissions and try again.";
      }
    },
  });
