import { tool } from "ai";
import { asc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { organization, trade } from "@/db/schema";

export const getCompanies = () =>
  tool({
    description: "Get companies by trade ID or name",
    parameters: z.object({
      tradeId: z.string().optional().describe("The ID of the trade"),
      tradeName: z.string().optional().describe("The name of the trade"),
    }),
    execute: async ({ tradeId, tradeName }) => {
      let targetTradeId = tradeId;

      // If tradeName is provided but not tradeId, look up the trade
      if (!targetTradeId && tradeName) {
        const foundTrade = await db.query.trade.findFirst({
          where: eq(trade.name, tradeName),
        });

        if (foundTrade) {
          targetTradeId = foundTrade.id;
        } else {
          return "Trade not found";
        }
      }

      if (!targetTradeId) {
        return "Either tradeId or tradeName must be provided";
      }

      const companies = await db.query.organization.findMany({
        where: eq(organization.tradeId, targetTradeId),
        with: {
          trade: true,
          services: true,
        },
        orderBy: [asc(organization.name)],
      });

      return JSON.stringify(companies);
    },
  });
