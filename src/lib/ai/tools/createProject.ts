import { tool } from "ai";
import { eq, type InferInsertModel } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { job, type Property, property, task } from "@/db/schema";

export const createProject = () =>
  tool({
    description: "Create a new home project",
    parameters: z.object({
      name: z.string().describe("The name of the project"),
      budget: z.number().describe("The budget for the project"),
      propertyName: z
        .string()
        .optional()
        .describe(
          "The users name for the property. Optional if user only has one property.",
        ),
      startDate: z.coerce.date().describe("The start date of the project"),
      endDate: z.coerce.date().describe("The deadline for bids"),
      tasks: z
        .object({
          name: z.string().describe("The name of the task"),
          tradeId: z.string().describe("The ID of the trade for the task"),
        })
        .array()
        .describe("The tasks for the project"),
      userId: z.string().describe("The ID of the user creating the project"),
    }),
    execute: async ({
      name,
      budget,
      propertyName,
      startDate,
      endDate,
      tasks,
      userId,
    }) => {
      // Get all properties for this user
      const properties = await db
        .select()
        .from(property)
        .where(eq(property.userId, userId));

      if (properties.length === 0) {
        return "No properties found for this user. Please create a property first.";
      }

      let selectedProperty: Property | undefined;

      // If there's only one property, use it
      if (properties.length === 1) {
        selectedProperty = properties[0];
      }
      // If propertyName is provided, find that property
      else if (propertyName) {
        selectedProperty = properties.find(
          (p) => p.name.toLowerCase() === propertyName.toLowerCase(),
        );
      }
      // If multiple properties and no name provided, ask user to specify
      else {
        return `Please specify which property to use. Available properties: ${properties.map((p) => p.name).join(", ")}`;
      }

      if (!selectedProperty) {
        return `Property "${propertyName}" not found. Available properties: ${properties.map((p) => p.name).join(", ")}`;
      }

      const data: InferInsertModel<typeof job> = {
        name,
        budget,
        propertyId: selectedProperty.id,
        startsAt: startDate,
        deadline: endDate,
        jobType: "STANDARD",
        status: "DRAFT",
      };

      const [newJob] = await db.insert(job).values(data).returning();

      if (!newJob) {
        return "Failed to create project";
      }

      await db.insert(task).values(
        tasks.map((task) => ({
          name: task.name,
          tradeId: task.tradeId,
          jobId: newJob.id,
        })),
      );

      return `Project "${name}" created for property "${selectedProperty.name}"`;
    },
  });
