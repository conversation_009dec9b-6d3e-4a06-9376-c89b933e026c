import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";

export interface ChatContext {
  jobId?: string;
  bidId?: string;
  userRole: string;
  projectType?: string;
  projectStatus?: string;
  bidStatus?: string;
  contractorName?: string;
  homeownerName?: string;
  recentMessages?: Array<{
    content: string;
    senderType: string;
    createdAt: Date;
  }>;
}

export interface SuggestedQuestion {
  text: string;
  category: 'project' | 'timeline' | 'budget' | 'materials' | 'logistics' | 'clarification';
  priority: 'high' | 'medium' | 'low';
  context: string;
}

export async function generateChatSuggestions(
  context: ChatContext
): Promise<SuggestedQuestion[]> {
  try {
    const prompt = buildSuggestionPrompt(context);
    
    const result = await generateText({
      model: openai("gpt-4-turbo"),
      prompt,
      temperature: 0.7,
      maxTokens: 800,
    });

    return parseSuggestions(result.text);
  } catch (error) {
    console.error("Error generating chat suggestions:", error);
    return getFallbackSuggestions(context);
  }
}

function buildSuggestionPrompt(context: ChatContext): string {
  const { userRole, projectType, projectStatus, bidStatus, recentMessages } = context;
  
  let prompt = `You are Jack, the AI assistant for TradeCrews. Generate 4-6 helpful question suggestions for a ${userRole} in a project chat.

Context:
- User Role: ${userRole}
- Project Type: ${projectType || 'General home improvement'}
- Project Status: ${projectStatus || 'Unknown'}
- Bid Status: ${bidStatus || 'Unknown'}`;

  if (recentMessages && recentMessages.length > 0) {
    prompt += `\n- Recent conversation context: ${recentMessages
      .slice(-3)
      .map(m => `${m.senderType}: "${m.content}"`)
      .join(', ')}`;
  }

  prompt += `\n
Generate practical, actionable questions that would help move the project forward. Focus on:

For Homeowners:
- Project clarifications and requirements
- Timeline and scheduling questions
- Budget and cost discussions
- Material preferences and specifications
- Quality expectations and standards

For Contractors:
- Technical specifications and details
- Access and logistics questions
- Material sourcing and preferences
- Timeline coordination
- Change requests and modifications

Format each suggestion as JSON with this structure:
{
  "text": "The actual question text",
  "category": "project|timeline|budget|materials|logistics|clarification",
  "priority": "high|medium|low",
  "context": "Brief explanation of why this question is relevant"
}

Return only a JSON array of suggestions, no other text.`;

  return prompt;
}

function parseSuggestions(response: string): SuggestedQuestion[] {
  try {
    // Clean up the response to extract JSON
    const jsonMatch = response.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error("No JSON array found in response");
    }
    
    const suggestions = JSON.parse(jsonMatch[0]);
    
    // Validate and filter suggestions
    return suggestions
      .filter((s: any) => s.text && s.category && s.priority)
      .slice(0, 6) // Limit to 6 suggestions
      .map((s: any) => ({
        text: s.text,
        category: s.category,
        priority: s.priority,
        context: s.context || '',
      }));
  } catch (error) {
    console.error("Error parsing suggestions:", error);
    return [];
  }
}

export function getFallbackSuggestions(context: ChatContext): SuggestedQuestion[] {
  const { userRole, projectStatus, bidStatus } = context;
  
  if (userRole === 'homeowner') {
    const homeownerSuggestions: SuggestedQuestion[] = [
      {
        text: "What's the estimated timeline for this project?",
        category: 'timeline',
        priority: 'high',
        context: 'Important for planning and scheduling'
      },
      {
        text: "Can you provide a detailed breakdown of the costs?",
        category: 'budget',
        priority: 'high',
        context: 'Essential for budget planning'
      },
      {
        text: "What materials do you recommend for this project?",
        category: 'materials',
        priority: 'medium',
        context: 'Helps ensure quality and durability'
      },
      {
        text: "Do you need access to any specific areas of the property?",
        category: 'logistics',
        priority: 'medium',
        context: 'Important for project preparation'
      },
      {
        text: "What should I expect during the construction process?",
        category: 'project',
        priority: 'medium',
        context: 'Helps set proper expectations'
      }
    ];

    // Filter based on project status
    if (projectStatus === 'PUBLISHED') {
      return homeownerSuggestions.filter(s => 
        s.category === 'clarification' || s.category === 'project'
      );
    } else if (projectStatus === 'AWARDED') {
      return homeownerSuggestions.filter(s => 
        s.category === 'timeline' || s.category === 'logistics'
      );
    }

    return homeownerSuggestions;
  } else {
    // Contractor suggestions
    const contractorSuggestions: SuggestedQuestion[] = [
      {
        text: "Can you clarify the specific requirements for this project?",
        category: 'clarification',
        priority: 'high',
        context: 'Ensures accurate bidding and execution'
      },
      {
        text: "What's your preferred timeline for completion?",
        category: 'timeline',
        priority: 'high',
        context: 'Critical for project planning'
      },
      {
        text: "Do you have any specific material preferences or requirements?",
        category: 'materials',
        priority: 'medium',
        context: 'Affects cost and quality decisions'
      },
      {
        text: "Are there any access restrictions I should be aware of?",
        category: 'logistics',
        priority: 'medium',
        context: 'Important for scheduling and planning'
      },
      {
        text: "What's your budget range for this project?",
        category: 'budget',
        priority: 'medium',
        context: 'Helps tailor the proposal appropriately'
      }
    ];

    // Filter based on bid status
    if (bidStatus === 'PROPOSED') {
      return contractorSuggestions.filter(s => 
        s.category === 'clarification' || s.category === 'budget'
      );
    } else if (bidStatus === 'ACCEPTED') {
      return contractorSuggestions.filter(s => 
        s.category === 'timeline' || s.category === 'logistics'
      );
    }

    return contractorSuggestions;
  }
}

// Get context-aware suggestions based on recent conversation
export function getContextualSuggestions(
  recentMessages: Array<{ content: string; senderType: string; createdAt: Date }>,
  userRole: string
): SuggestedQuestion[] {
  if (!recentMessages.length) {
    return getConversationStarters(userRole);
  }

  const lastMessage = recentMessages[recentMessages.length - 1];
  if (!lastMessage) {
    return getConversationStarters(userRole);
  }
  const lastMessageContent = lastMessage.content.toLowerCase();

  // Analyze recent messages for context
  if (lastMessageContent.includes('timeline') || lastMessageContent.includes('schedule')) {
    return getTimelineQuestions(userRole);
  } else if (lastMessageContent.includes('cost') || lastMessageContent.includes('budget') || lastMessageContent.includes('price')) {
    return getBudgetQuestions(userRole);
  } else if (lastMessageContent.includes('material') || lastMessageContent.includes('supply')) {
    return getMaterialQuestions(userRole);
  } else if (lastMessageContent.includes('start') || lastMessageContent.includes('begin')) {
    return getLogisticsQuestions(userRole);
  }

  return getGeneralQuestions(userRole);
}

function getConversationStarters(userRole: string): SuggestedQuestion[] {
  if (userRole === 'homeowner') {
    return [
      {
        text: "Hi! I'd like to discuss the project details with you.",
        category: 'project',
        priority: 'high',
        context: 'Good conversation starter'
      },
      {
        text: "When would be a good time to start this project?",
        category: 'timeline',
        priority: 'high',
        context: 'Important initial question'
      },
      {
        text: "Do you have any questions about the project requirements?",
        category: 'clarification',
        priority: 'medium',
        context: 'Opens dialogue for clarifications'
      }
    ];
  } else {
    return [
      {
        text: "Thank you for considering my bid. I'd love to discuss the project details.",
        category: 'project',
        priority: 'high',
        context: 'Professional conversation starter'
      },
      {
        text: "I have some questions about the project specifications.",
        category: 'clarification',
        priority: 'high',
        context: 'Shows attention to detail'
      },
      {
        text: "What's your ideal timeline for this project?",
        category: 'timeline',
        priority: 'medium',
        context: 'Important for planning'
      }
    ];
  }
}

function getTimelineQuestions(userRole: string): SuggestedQuestion[] {
  return [
    {
      text: "What's the latest date you'd like this completed?",
      category: 'timeline',
      priority: 'high',
      context: 'Clarifies deadline constraints'
    },
    {
      text: "Are there any specific dates I should avoid?",
      category: 'timeline',
      priority: 'medium',
      context: 'Helps with scheduling'
    },
    {
      text: "How flexible is the timeline if we encounter delays?",
      category: 'timeline',
      priority: 'medium',
      context: 'Sets expectations for contingencies'
    }
  ];
}

function getBudgetQuestions(userRole: string): SuggestedQuestion[] {
  if (userRole === 'homeowner') {
    return [
      {
        text: "Can you break down the costs by category?",
        category: 'budget',
        priority: 'high',
        context: 'Helps understand value breakdown'
      },
      {
        text: "Are there any optional upgrades I should consider?",
        category: 'budget',
        priority: 'medium',
        context: 'Explores additional value options'
      },
      {
        text: "What payment schedule do you prefer?",
        category: 'budget',
        priority: 'medium',
        context: 'Important for financial planning'
      }
    ];
  } else {
    return [
      {
        text: "Is there flexibility in the budget for quality upgrades?",
        category: 'budget',
        priority: 'medium',
        context: 'Explores value-add opportunities'
      },
      {
        text: "Would you prefer a fixed price or time and materials?",
        category: 'budget',
        priority: 'high',
        context: 'Clarifies pricing structure'
      }
    ];
  }
}

function getMaterialQuestions(userRole: string): SuggestedQuestion[] {
  return [
    {
      text: "Do you have preferred brands or suppliers?",
      category: 'materials',
      priority: 'medium',
      context: 'Ensures material preferences are met'
    },
    {
      text: "Should I source the materials or will you provide them?",
      category: 'materials',
      priority: 'high',
      context: 'Clarifies procurement responsibilities'
    },
    {
      text: "Are there any materials I should avoid due to allergies or preferences?",
      category: 'materials',
      priority: 'medium',
      context: 'Ensures health and preference considerations'
    }
  ];
}

function getLogisticsQuestions(userRole: string): SuggestedQuestion[] {
  return [
    {
      text: "What are the best hours for work at your property?",
      category: 'logistics',
      priority: 'high',
      context: 'Respects homeowner schedule'
    },
    {
      text: "Where can I park and store materials?",
      category: 'logistics',
      priority: 'medium',
      context: 'Important for project setup'
    },
    {
      text: "Do I need to coordinate with any other contractors?",
      category: 'logistics',
      priority: 'medium',
      context: 'Ensures smooth coordination'
    }
  ];
}

function getGeneralQuestions(userRole: string): SuggestedQuestion[] {
  return getFallbackSuggestions({ userRole });
}