import { type ClassValue, clsx } from "clsx";
import { format, parseISO } from "date-fns";
import { and, between, count, eq, gte, inArray } from "drizzle-orm";
import { twMerge } from "tailwind-merge";
import type { Db } from "@/db";
import { type BidStatus, type JobStatus, job, user } from "@/db/schema";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Calculate percentage change between current and previous values
 * @param current Current period value
 * @param previous Previous period value
 * @returns Percentage change (positive for increase, negative for decrease)
 */
export function calculateTrend(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;

  const percentageChange = ((current - previous) / previous) * 100;
  return Math.round(percentageChange);
}

/**
 * Calculate user growth trend by comparing current month to previous month
 * @param db Database client
 * @returns Percentage change in user count
 */
export async function calculateUsersTrend(db: Db): Promise<number> {
  const today = new Date();
  const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  const previousMonthStart = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    1,
  );

  // Get current month users
  const currentMonthUsers = await db
    .select({ count: count(user.id) })
    .from(user)
    .where(gte(user.createdAt, currentMonthStart));

  // Get previous month users
  const previousMonthUsers = await db
    .select({ count: count(user.id) })
    .from(user)
    .where(between(user.createdAt, previousMonthStart, currentMonthStart));

  return calculateTrend(
    Number(currentMonthUsers[0]?.count || 0),
    Number(previousMonthUsers[0]?.count || 0),
  );
}

/**
 * Calculate job growth trend by comparing current month to previous month
 * @param db Database client
 * @param status Optional job status to filter by
 * @returns Percentage change in job count
 */
export async function calculateJobsTrend(
  db: Db,
  status?: string | string[],
): Promise<number> {
  const today = new Date();
  const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  const previousMonthStart = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    1,
  );

  // Get current month jobs
  const currentMonthJobs = await db
    .select({ count: count(job.id) })
    .from(job)
    .where(
      and(
        gte(job.createdAt, currentMonthStart),
        status
          ? Array.isArray(status)
            ? inArray(job.status, status as JobStatus[])
            : eq(job.status, status as JobStatus)
          : undefined,
      ),
    );

  // Get previous month jobs
  const previousMonthJobs = await db
    .select({ count: count(job.id) })
    .from(job)
    .where(
      and(
        between(job.createdAt, previousMonthStart, currentMonthStart),
        status
          ? Array.isArray(status)
            ? inArray(job.status, status as JobStatus[])
            : eq(job.status, status as JobStatus)
          : undefined,
      ),
    );

  return calculateTrend(
    Number(currentMonthJobs[0]?.count || 0),
    Number(previousMonthJobs[0]?.count || 0),
  );
}

// Define a generic type for status variants
type StatusVariant =
  | "success"
  | "default"
  | "destructive"
  | "outline"
  | "secondary";

// Generic function to get status variant based on status and mapping
export function getStatusVariant<T extends string>(
  status: T,
  variantMap: Record<T, StatusVariant>,
): StatusVariant {
  return variantMap[status] || "outline";
}

// Define status variant mappings
export const JOB_STATUS_VARIANTS: Record<JobStatus, StatusVariant> = {
  AWARDED: "success",
  PUBLISHED: "default",
  CANCELED: "destructive",
  COMPLETED: "success",
  DRAFT: "outline",
  CLOSED: "secondary",
};

export const BID_STATUS_VARIANTS: Record<BidStatus, StatusVariant> = {
  ACCEPTED: "success",
  REJECTED: "destructive",
  CANCELED: "destructive",
  PROPOSED: "default",
  WITHDRAWN: "outline",
};

// Maintain backward compatibility with existing function calls
export function getJobStatusVariant(status: JobStatus): StatusVariant {
  return getStatusVariant(status, JOB_STATUS_VARIANTS);
}

export function getBidStatusVariant(status: BidStatus): StatusVariant {
  return getStatusVariant(status, BID_STATUS_VARIANTS);
}

// Date formatting utilities
type DateInput = Date | string | number;

/**
 * Formats a date in a standard format (e.g., "Jan 1, 2023")
 */
export function formatDate(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "MMM d, yyyy");
}

/**
 * Formats a date in a long format (e.g., "January 1, 2023")
 */
export function formatLongDate(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "LLLL d, yyyy");
}

/**
 * Formats a date in a short format (e.g., "01/01/2023")
 */
export function formatShortDate(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "P");
}

/**
 * Formats a date with time (e.g., "Jan 1, 2023 at 12:00 PM")
 */
export function formatDateTime(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "MMM d, yyyy 'at' h:mm a");
}

/**
 * Formats a date for display in a calendar or date picker (e.g., "2023-01-01")
 */
export function formatCalendarDate(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "yyyy-MM-dd");
}

/**
 * Formats a date for display in a chat (e.g., "12:00 PM")
 */
export function formatChatDate(date: DateInput): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  return format(dateObj, "h:mm a");
}
