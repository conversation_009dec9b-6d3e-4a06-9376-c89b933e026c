interface AssemblyResult {
  ok?: string;
  error?: string;
  results?: {
    optimized?: Array<{
      url: string;
    }>;
  };
}

export async function pollAssemblyStatus(
  assemblyUrl: string,
  maxAttempts = 30,
): Promise<AssemblyResult> {
  let attempts = 0;

  while (attempts < maxAttempts) {
    const response = await fetch(assemblyUrl);
    const result = await response.json();

    if (result.error || result.ok === "ASSEMBLY_COMPLETED") {
      return result;
    }

    // Wait 1 second before next attempt
    await new Promise((resolve) => setTimeout(resolve, 1000));
    attempts++;
  }

  throw new Error("Assembly timed out");
}
