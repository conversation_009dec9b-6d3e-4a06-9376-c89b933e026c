import { useEffect, useState } from "react";

// Touch-friendly size constants
export const TOUCH_SIZES = {
  // Minimum touch target size (44px recommended by Apple/Google)
  minTouchTarget: "44px",
  minTouchTargetRem: "2.75rem",

  // Comfortable touch targets
  comfortable: "48px",
  comfortableRem: "3rem",

  // Large touch targets for primary actions
  large: "56px",
  largeRem: "3.5rem",
} as const;

// Mobile-first spacing scale
export const MOBILE_SPACING = {
  xs: "0.25rem", // 4px
  sm: "0.5rem", // 8px
  md: "0.75rem", // 12px
  lg: "1rem", // 16px
  xl: "1.25rem", // 20px
  "2xl": "1.5rem", // 24px
  "3xl": "2rem", // 32px
} as const;

// Responsive breakpoints (matching Tailwind defaults)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
} as const;

// Touch gesture detection hook
export function useTouchGestures(element: HTMLElement | null) {
  const [gestures, setGestures] = useState({
    isTouch: false,
    isSwiping: false,
    swipeDirection: null as "left" | "right" | "up" | "down" | null,
    touchStart: null as { x: number; y: number } | null,
  });

  useEffect(() => {
    if (!element) return;

    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartX = e.changedTouches[0]?.clientX || 0;
      touchStartY = e.changedTouches[0]?.clientY || 0;

      setGestures((prev) => ({
        ...prev,
        isTouch: true,
        touchStart: { x: touchStartX, y: touchStartY },
      }));
    };

    const handleTouchMove = (e: TouchEvent) => {
      setGestures((prev) => ({
        ...prev,
        isSwiping: true,
      }));
    };

    const handleTouchEnd = (e: TouchEvent) => {
      touchEndX = e.changedTouches[0]?.clientX || 0;
      touchEndY = e.changedTouches[0]?.clientY || 0;

      const deltaX = touchEndX - touchStartX;
      const deltaY = touchEndY - touchStartY;
      const minSwipeDistance = 50;

      let swipeDirection: typeof gestures.swipeDirection = null;

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (Math.abs(deltaX) > minSwipeDistance) {
          swipeDirection = deltaX > 0 ? "right" : "left";
        }
      } else {
        // Vertical swipe
        if (Math.abs(deltaY) > minSwipeDistance) {
          swipeDirection = deltaY > 0 ? "down" : "up";
        }
      }

      setGestures((prev) => ({
        ...prev,
        isSwiping: false,
        swipeDirection,
      }));

      // Reset after a short delay
      setTimeout(() => {
        setGestures((prev) => ({
          ...prev,
          isTouch: false,
          swipeDirection: null,
          touchStart: null,
        }));
      }, 100);
    };

    element.addEventListener("touchstart", handleTouchStart, { passive: true });
    element.addEventListener("touchmove", handleTouchMove, { passive: true });
    element.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener("touchstart", handleTouchStart);
      element.removeEventListener("touchmove", handleTouchMove);
      element.removeEventListener("touchend", handleTouchEnd);
    };
  }, [element]);

  return gestures;
}

// Viewport height hook that accounts for mobile browser UI
export function useViewportHeight() {
  const [viewportHeight, setViewportHeight] = useState<number>(0);

  useEffect(() => {
    const updateHeight = () => {
      // Use visualViewport if available (better for mobile)
      if (window.visualViewport) {
        setViewportHeight(window.visualViewport.height);
      } else {
        setViewportHeight(window.innerHeight);
      }
    };

    updateHeight();

    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", updateHeight);
      return () =>
        window.visualViewport?.removeEventListener("resize", updateHeight);
    }

    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  return viewportHeight;
}

// Safe area insets for devices with notches/rounded corners
export function useSafeAreaInsets() {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    const updateInsets = () => {
      const style = getComputedStyle(document.documentElement);

      setInsets({
        top: Number.parseInt(
          style.getPropertyValue("--safe-area-inset-top") || "0",
        ),
        right: Number.parseInt(
          style.getPropertyValue("--safe-area-inset-right") || "0",
        ),
        bottom: Number.parseInt(
          style.getPropertyValue("--safe-area-inset-bottom") || "0",
        ),
        left: Number.parseInt(
          style.getPropertyValue("--safe-area-inset-left") || "0",
        ),
      });
    };

    updateInsets();
    window.addEventListener("resize", updateInsets);

    return () => window.removeEventListener("resize", updateInsets);
  }, []);

  return insets;
}

// Device orientation detection
export function useDeviceOrientation() {
  const [orientation, setOrientation] = useState<"portrait" | "landscape">(
    "portrait",
  );

  useEffect(() => {
    const updateOrientation = () => {
      if (window.screen?.orientation) {
        setOrientation(
          window.screen.orientation.angle === 0 ||
            window.screen.orientation.angle === 180
            ? "portrait"
            : "landscape",
        );
      } else {
        // Fallback for older browsers
        setOrientation(
          window.innerHeight > window.innerWidth ? "portrait" : "landscape",
        );
      }
    };

    updateOrientation();

    if (window.screen?.orientation) {
      window.screen.orientation.addEventListener("change", updateOrientation);
      return () =>
        window.screen.orientation?.removeEventListener(
          "change",
          updateOrientation,
        );
    }
    window.addEventListener("resize", updateOrientation);
    return () => window.removeEventListener("resize", updateOrientation);
  }, []);

  return orientation;
}

// Utility functions for mobile-specific styling
export const mobileUtils = {
  // Generate touch-friendly classes
  touchTarget: (size: keyof typeof TOUCH_SIZES = "minTouchTarget") => ({
    minHeight: TOUCH_SIZES[size],
    minWidth: TOUCH_SIZES[size],
  }),

  // Generate responsive padding classes
  responsivePadding: (mobile: string, desktop?: string) =>
    `p-${mobile} ${desktop ? `sm:p-${desktop}` : `sm:p-${mobile}`}`,

  // Generate responsive margin classes
  responsiveMargin: (mobile: string, desktop?: string) =>
    `m-${mobile} ${desktop ? `sm:m-${desktop}` : `sm:m-${mobile}`}`,

  // Generate responsive gap classes
  responsiveGap: (mobile: string, desktop?: string) =>
    `gap-${mobile} ${desktop ? `sm:gap-${desktop}` : `sm:gap-${mobile}`}`,

  // Check if device supports hover
  supportsHover: () =>
    typeof window !== "undefined" &&
    window.matchMedia("(hover: hover)").matches,

  // Check if device supports touch
  supportsTouch: () =>
    typeof window !== "undefined" &&
    ("ontouchstart" in window || navigator.maxTouchPoints > 0),
};

// CSS custom properties for mobile-specific values
export const mobileCSSVars = {
  "--touch-target-min": TOUCH_SIZES.minTouchTarget,
  "--touch-target-comfortable": TOUCH_SIZES.comfortable,
  "--touch-target-large": TOUCH_SIZES.large,
  "--mobile-spacing-xs": MOBILE_SPACING.xs,
  "--mobile-spacing-sm": MOBILE_SPACING.sm,
  "--mobile-spacing-md": MOBILE_SPACING.md,
  "--mobile-spacing-lg": MOBILE_SPACING.lg,
  "--mobile-spacing-xl": MOBILE_SPACING.xl,
  "--mobile-spacing-2xl": MOBILE_SPACING["2xl"],
  "--mobile-spacing-3xl": MOBILE_SPACING["3xl"],
} as const;
