import { z } from "zod/v4";

export const contractorSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  trade: z.object({
    id: z.string().min(1, "Please select a trade"),
  }),
  description: z.string().optional(),
  email: z.email().optional(),
  phone: z.string().optional(),
  address: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zip: z.string().min(1, "ZIP code is required"),
  }),
  acceptsQuickHire: z.boolean().default(false),
});

export type ContractorFormData = z.infer<typeof contractorSchema>;
