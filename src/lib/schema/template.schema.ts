import { z } from "zod/v4";

export const templateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  budget: z.number().min(1, "Budget must be greater than 0"),
  estimatedDuration: z.number().min(1, "Duration must be at least 1 day"),
  tasks: z
    .array(
      z.object({
        name: z.string().min(1, "Task name is required"),
        tradeId: z.string().min(1, "Trade is required"),
      }),
    )
    .min(1, "At least one task is required"),
});

export type TemplateFormData = z.infer<typeof templateSchema>;
