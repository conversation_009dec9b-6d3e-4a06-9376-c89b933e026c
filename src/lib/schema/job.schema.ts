import { z } from "zod/v4";

export const jobSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    startsAt: z.date().refine((date) => date > new Date(), {
      message: "Start date must be in the future",
    }),
    deadline: z.date().refine((date) => date > new Date(), {
      message: "Bid deadline must be in the future",
    }),
    budget: z.number(),
    propertyId: z.string().min(1, "Property is required"),
    tasks: z.object({ name: z.string(), tradeId: z.string() }).array(),
    images: z
      .object({
        url: z.string().optional(),
        description: z.string().optional().nullable(),
      })
      .array()
      .optional(),
    jobType: z.enum(["STANDARD", "QUICK_HIRE"]),
    isRecurring: z.boolean(),
    recurringFrequency: z.string().nullable(),
    contractorId: z.string().optional(),
    serviceId: z.string().optional(),
  })
  .refine(
    (data) => {
      return data.startsAt > data.deadline;
    },
    {
      message: "Bid deadline must be before the expected start date",
      path: ["deadline"],
    },
  );

export const reviewSchema = z.object({
  rating: z.number().min(1, "Please select a rating").max(5),
  comment: z.string().min(1, "Please provide a comment"),
});

export const completeJobSchema = z.object({
  completionNotes: z.string().optional(),
  images: z
    .object({
      url: z.string(),
      description: z.string().optional().nullable(),
    })
    .array()
    .optional(),
});

export type JobFormData = z.infer<typeof jobSchema>;
export type ReviewFormData = z.infer<typeof reviewSchema>;
export type CompleteJobFormData = z.infer<typeof completeJobSchema>;
