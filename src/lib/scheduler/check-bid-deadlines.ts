import { and, eq, gte, lte } from "drizzle-orm";
import { db } from "@/db";
import { job } from "@/db/schema";
import { sendBidDeadlineNotifications } from "@/lib/email/send-bid-deadline-notification";

export async function checkBidDeadlines() {
  // Find jobs with deadlines approaching in the next 3 days
  const today = new Date();
  const threeDaysFromNow = new Date();
  threeDaysFromNow.setDate(today.getDate() + 3);

  // Find published jobs with approaching deadlines
  const jobsWithApproachingDeadlines = await db.query.job.findMany({
    where: and(
      eq(job.status, "PUBLISHED"),
      gte(job.deadline, today),
      lte(job.deadline, threeDaysFromNow),
    ),
    columns: {
      id: true,
    },
  });

  // Send notifications for each job
  for (const job of jobsWithApproachingDeadlines) {
    await sendBidDeadlineNotifications(job.id);
  }

  return jobsWithApproachingDeadlines.length;
}
