import { addDays } from "date-fns";
import { and, eq, gte, lte } from "drizzle-orm";
import { db } from "@/db";
import { job, schedule } from "@/db/schema";
import { sendScheduledJobReminder } from "@/lib/email/send-scheduled-job-reminder";

export async function checkUpcomingScheduledJobs() {
  // Find jobs with confirmed schedules in the next 24 hours
  const today = new Date();
  const tomorrow = addDays(today, 1);

  // Find jobs with confirmed schedules starting within the next 24 hours
  const upcomingJobs = await db.query.job.findMany({
    where: and(
      eq(job.status, "AWARDED"),
      eq(schedule.status, "CONFIRMED"),
      gte(schedule.confirmedStartDate, today),
      lte(schedule.confirmedStartDate, tomorrow),
    ),
    columns: {
      id: true,
    },
  });

  // Send reminders for each upcoming job
  for (const job of upcomingJobs) {
    await sendScheduledJobReminder(job.id);
  }

  return upcomingJobs.length;
}
