import { and, eq, ilike } from "drizzle-orm";
import { db } from "@/db";
import { bid, job, membership, task, trade, user } from "@/db/schema";

type CommandResult = {
  success: boolean;
  message: string;
  data?: unknown;
};

export async function processChatCommand({
  command,
  userId,
  bidId,
  jobId,
}: {
  command: string;
  userId: string;
  bidId?: string;
  jobId?: string;
}): Promise<CommandResult> {
  // Parse command
  const parts = command.slice(1).split(" ");
  const action = parts[0]?.toLowerCase();

  try {
    // Handle bid-related commands
    if (bidId) {
      const bidData = await db.query.bid.findFirst({
        where: eq(bid.id, bidId),
        with: {
          job: {
            with: {
              property: {
                with: {
                  user: true,
                },
              },
            },
          },
          organization: {
            with: {
              memberships: true,
            },
          },
        },
      });

      if (!bidData) {
        return { success: false, message: "Bid not found" };
      }

      // Check permissions
      const isPropertyOwner = await db.query.user.findFirst({
        where: and(
          eq(user.id, userId),
          // Check if user owns property with this job
        ),
      });

      const isBidder = await db.query.membership.findFirst({
        where: and(
          eq(membership.userId, userId),
          eq(membership.organizationId, bidData.organizationId),
        ),
      });

      // Handle bid commands
      switch (action) {
        case "update-amount": {
          if (!isBidder) {
            return {
              success: false,
              message: "Only the bidder can update the bid amount",
            };
          }

          const amount = Number.parseFloat(parts[1] as string);
          if (Number.isNaN(amount) || amount <= 0) {
            return {
              success: false,
              message: "Invalid amount. Format: /update-amount 1000",
            };
          }

          await db.update(bid).set({ amount }).where(eq(bid.id, bidId));

          return {
            success: true,
            message: `Bid amount updated to $${amount}`,
            data: { amount },
          };
        }
        case "update-duration": {
          if (!isBidder) {
            return {
              success: false,
              message: "Only the bidder can update the duration",
            };
          }

          const duration = Number.parseInt(parts[1] as string);
          if (Number.isNaN(duration) || duration <= 0) {
            return {
              success: false,
              message: "Invalid duration. Format: /update-duration 14",
            };
          }

          await db
            .update(bid)
            .set({ estimatedDuration: duration })
            .where(eq(bid.id, bidId));

          return {
            success: true,
            message: `Estimated duration updated to ${duration} days`,
            data: { estimatedDuration: duration },
          };
        }
        case "accept-bid": {
          if (!isPropertyOwner) {
            return {
              success: false,
              message: "Only the property owner can accept bids",
            };
          }

          await db
            .update(bid)
            .set({ status: "ACCEPTED" })
            .where(eq(bid.id, bidId));

          // Also update the job status
          await db
            .update(job)
            .set({ status: "AWARDED" })
            .where(eq(job.id, bid.jobId));

          return {
            success: true,
            message: "Bid accepted successfully",
            data: { status: "ACCEPTED" },
          };
        }
      }
    }

    // Handle job-related commands
    if (jobId) {
      const jobData = await db.query.job.findFirst({
        where: eq(job.id, jobId),
        with: {
          property: {
            with: {
              user: true,
            },
          },
        },
      });

      if (!jobData) {
        return { success: false, message: "Job not found" };
      }

      // Check permissions
      const isPropertyOwner = await db.query.user.findFirst({
        where: and(
          eq(user.id, userId),
          // Check if user owns this property
        ),
      });

      // Handle job commands
      switch (action) {
        case "update-name": {
          if (!isPropertyOwner) {
            return {
              success: false,
              message: "Only the property owner can update the job name",
            };
          }

          const name = parts.slice(1).join(" ");
          if (!name) {
            return {
              success: false,
              message: "Invalid name. Format: /update-name New Job Name",
            };
          }

          await db.update(job).set({ name }).where(eq(job.id, jobId));

          return {
            success: true,
            message: `Job name updated to "${name}"`,
            data: { name },
          };
        }
        case "complete-job": {
          if (!isPropertyOwner) {
            return {
              success: false,
              message: "Only the property owner can mark a job as complete",
            };
          }

          await db
            .update(job)
            .set({ status: "COMPLETED" })
            .where(eq(job.id, jobId));

          return {
            success: true,
            message: "Job marked as completed",
            data: { status: "COMPLETED" },
          };
        }
        case "add-task": {
          if (!isPropertyOwner) {
            return {
              success: false,
              message: "Only the property owner can add tasks to the job",
            };
          }

          const taskName = parts.slice(1, -1).join(" ");
          const tradeName = parts[parts.length - 1];

          if (!taskName || !tradeName) {
            return {
              success: false,
              message: "Invalid format. Use: /add-task Task Name Trade Name",
            };
          }

          // Verify the trade exists
          const tradeData = await db.query.trade.findFirst({
            where: ilike(trade.name, tradeName),
          });

          if (!tradeData) {
            return {
              success: false,
              message: "Trade not found. Please provide a valid trade name",
            };
          }

          // Create the new task
          const taskData = await db
            .insert(task)
            .values({
              name: taskName,
              tradeId: tradeData.id,
              jobId,
            })
            .returning();

          return {
            success: true,
            message: `Task "${taskName}" added to the job with trade ${tradeData.name}`,
            data: {
              taskName,
              tradeName: tradeData.name,
              tradeId: tradeData.id,
            },
          };
        }
      }
    }

    // Add these handlers to your existing chat command processors

    // For updating bid amount
    if (command.startsWith("/update-amount") && bidId) {
      const amount = Number.parseFloat(command.split(" ")[1] as string);
      if (Number.isNaN(amount)) {
        return {
          success: false,
          message: "Invalid amount format. Use /update-amount 1000",
        };
      }

      try {
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        });

        if (!bidData) {
          return { success: false, message: "Bid not found" };
        }

        // Check if user is part of the organization
        const isMember = bidData.organization.memberships.some(
          (m) => m.userId === userId,
        );

        if (!isMember) {
          return {
            success: false,
            message: "You don't have permission to update this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return {
            success: false,
            message: "Only proposed bids can be updated",
          };
        }

        const updatedBid = await db
          .update(bid)
          .set({ amount })
          .where(eq(bid.id, bidId));

        return {
          success: true,
          message: `Bid amount updated to $${amount}`,
          data: JSON.stringify({ amount }),
        };
      } catch (error) {
        console.error("Error updating bid amount:", error);
        return { success: false, message: "Failed to update bid amount" };
      }
    }

    // For updating bid duration
    if (command.startsWith("/update-duration") && bidId) {
      const duration = Number.parseInt(command.split(" ")[1] as string);
      if (Number.isNaN(duration)) {
        return {
          success: false,
          message: "Invalid duration format. Use /update-duration 14",
        };
      }

      try {
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        });

        if (!bidData) {
          return { success: false, message: "Bid not found" };
        }

        // Check if user is part of the organization
        const isMember = bidData.organization.memberships.some(
          (m) => m.userId === userId,
        );

        if (!isMember) {
          return {
            success: false,
            message: "You don't have permission to update this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return {
            success: false,
            message: "Only proposed bids can be updated",
          };
        }

        const updatedBid = await db
          .update(bid)
          .set({ estimatedDuration: duration })
          .where(eq(bid.id, bidId));

        return {
          success: true,
          message: `Bid duration updated to ${duration} days`,
          data: JSON.stringify({ estimatedDuration: duration }),
        };
      } catch (error) {
        console.error("Error updating bid duration:", error);
        return { success: false, message: "Failed to update bid duration" };
      }
    }

    // For updating bid description
    if (command.startsWith("/edit-description") && bidId) {
      const description = command.substring("/edit-description".length).trim();
      if (!description) {
        return {
          success: false,
          message:
            "Description cannot be empty. Use /edit-description Your new description",
        };
      }

      try {
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        });

        if (!bidData) {
          return { success: false, message: "Bid not found" };
        }

        // Check if user is part of the organization
        const isMember = bidData.organization.memberships.some(
          (m) => m.userId === userId,
        );

        if (!isMember) {
          return {
            success: false,
            message: "You don't have permission to update this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return {
            success: false,
            message: "Only proposed bids can be updated",
          };
        }

        const updatedBid = await db
          .update(bid)
          .set({ description })
          .where(eq(bid.id, bidId));

        return {
          success: true,
          message: "Bid description updated",
          data: JSON.stringify({
            description: `${description.substring(0, 20)}...`,
          }),
        };
      } catch (error) {
        console.error("Error updating bid description:", error);
        return { success: false, message: "Failed to update bid description" };
      }
    }

    // For withdrawing a bid
    if (command === "/withdraw-bid" && bidId) {
      try {
        const bidData = await db.query.bid.findFirst({
          where: eq(bid.id, bidId),
          with: {
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        });

        if (!bidData) {
          return { success: false, message: "Bid not found" };
        }

        // Check if user is part of the organization
        const isMember = bidData.organization.memberships.some(
          (m) => m.userId === userId,
        );

        if (!isMember) {
          return {
            success: false,
            message: "You don't have permission to withdraw this bid",
          };
        }

        if (bidData.status !== "PROPOSED") {
          return {
            success: false,
            message: "Only proposed bids can be withdrawn",
          };
        }

        const updatedBid = await db
          .update(bid)
          .set({ status: "WITHDRAWN" })
          .where(eq(bid.id, bidId));

        return {
          success: true,
          message: "Bid has been withdrawn",
          data: JSON.stringify({ status: "WITHDRAWN" }),
        };
      } catch (error) {
        console.error("Error withdrawing bid:", error);
        return { success: false, message: "Failed to withdraw bid" };
      }
    }

    // If we get here, the command wasn't recognized
    return { success: false, message: "Unknown command" };
  } catch (error) {
    console.error("Error processing chat command:", error);
    return {
      success: false,
      message: "An error occurred while processing the command",
    };
  }
}
