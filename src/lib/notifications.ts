import { toast } from "sonner";

/**
 * Standard success notification
 */
export function notify<PERSON><PERSON><PERSON>(message: string, description?: string) {
  toast.success(message, {
    description,
    duration: 3000,
  });
}

/**
 * Standard error notification
 */
export function notifyError(error: unknown) {
  const message =
    error instanceof Error
      ? error.message
      : typeof error === "string"
        ? error
        : "An unexpected error occurred";

  toast.error("Error", {
    description: message,
    duration: 5000,
  });
}

/**
 * Standard info notification
 */
export function notifyInfo(message: string, description?: string) {
  toast.info(message, {
    description,
    duration: 3000,
  });
}

/**
 * Standard warning notification
 */
export function notifyWarning(message: string, description?: string) {
  toast.warning(message, {
    description,
    duration: 4000,
  });
}
