import { render } from "@react-email/render";
import { Resend } from "resend";
import { env } from "@/env";

// Initialize Resend with API key
const resend = new Resend(env.RESEND_API_KEY);

// Email sending options interface
export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: React.ReactNode;
  from?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
}

/**
 * Send an email using Resend
 */
export async function sendEmail({
  to,
  subject,
  template,
  from = "Jack from TradeCrews <<EMAIL>>",
  replyTo = "<EMAIL>",
  cc,
  bcc,
}: SendEmailOptions) {
  // In development, log to console instead of sending
  if (env.NODE_ENV === "development") {
    console.log("\n----- DEV EMAIL -----");
    console.log(`From: ${from}`);
    console.log(`To: ${Array.isArray(to) ? to.join(", ") : to}`);
    console.log(`Subject: ${subject}`);
    if (cc) console.log(`CC: ${Array.isArray(cc) ? cc.join(", ") : cc}`);
    if (bcc) console.log(`BCC: ${Array.isArray(bcc) ? bcc.join(", ") : bcc}`);
    console.log(`Reply-To: ${replyTo}`);
    console.log("Template:", await render(template, { plainText: true }));
    console.log("----- END EMAIL -----\n");

    return { id: "dev-email-id" };
  }

  try {
    const { data, error } = await resend.emails.send({
      from,
      to,
      subject,
      react: template,
      replyTo,
      cc,
      bcc,
    });

    if (error) {
      console.error("Failed to send email:", error);
      throw new Error(`Failed to send email: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}
