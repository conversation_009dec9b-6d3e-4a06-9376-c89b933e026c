import { and, eq, inArray, not } from "drizzle-orm";
import BidDeadlineNotificationEmail from "@/components/templates/emails/bid-deadline-notification";
import { db } from "@/db";
import { job, organization, user } from "@/db/schema";
import { sendEmail } from "@/lib/email";

export async function sendBidDeadlineNotifications(jobId: string) {
  // Get the job details
  const jobData = await db.query.job.findFirst({
    where: eq(job.id, jobId),
    with: {
      property: {
        with: {
          address: true,
        },
      },
      tasks: {
        with: {
          trade: true,
        },
      },
      bids: {
        columns: {
          organizationId: true,
        },
      },
    },
  });

  if (!jobData) return;

  // Calculate days remaining until deadline
  const today = new Date();
  const deadline = jobData.deadline;
  const daysRemaining = Math.ceil(
    (deadline.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
  );

  if (daysRemaining <= 0) return; // Don't send if deadline has passed

  // Get trade IDs from job tasks
  const tradeIds = jobData.tasks.map((task) => task.tradeId);

  // Get organizations that already bid
  const orgsThatBid = jobData.bids.map((bid) => bid.organizationId);

  // Find organizations with matching trades that haven't bid yet
  const organizationList = await db.query.organization.findMany({
    where: and(
      inArray(organization.tradeId, tradeIds),
      not(inArray(organization.id, orgsThatBid)),
    ),
    with: {
      trade: true,
      memberships: {
        columns: {
          userId: true,
        },
      },
    },
  });

  // Get location string
  const location = jobData.property.address
    ? `${jobData.property.address.city}, ${jobData.property.address.state}`
    : "Location not specified";

  // Format deadline
  const deadlineFormatted = jobData.deadline.toLocaleDateString();

  // Send email to each organization member
  for (const org of organizationList) {
    for (const membership of org.memberships) {
      const userData = await db.query.user.findFirst({
        where: eq(user.id, membership.userId),
      });
      const email = userData?.email;

      if (email) {
        await sendEmail({
          to: email,
          subject: `Jack here - friendly reminder: ${daysRemaining} days left to bid! ⏰`,
          template: BidDeadlineNotificationEmail({
            jobName: jobData.name,
            jobId: jobData.id,
            tradeName: org.trade?.name || "trade",
            deadline: deadlineFormatted,
            daysRemaining: daysRemaining.toString(),
            location,
            budget: jobData.budget,
            userId: membership.userId,
            recipientEmail: email,
          }),
        });
      }
    }
  }
}
