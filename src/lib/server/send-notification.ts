"use server";

import { eq, inArray } from "drizzle-orm";
import webpush from "web-push";
import { db } from "@/db";
import { account, pushSubscription } from "@/db/schema";
import { env } from "@/env";

// Initialize web-push with VAPID keys
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  env.VAPID_PRIVATE_KEY,
);

type NotificationOptions = {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: Record<string, unknown>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
};

/**
 * Send a push notification to a specific account
 */
export async function sendNotificationToAccount(
  accountId: string,
  options: NotificationOptions,
): Promise<{ sent: number; failed: number }> {
  // Get all subscriptions for this account
  const subscriptions = await db.query.pushSubscription.findMany({
    where: eq(pushSubscription.accountId, accountId),
  });

  if (subscriptions.length === 0) {
    return { sent: 0, failed: 0 };
  }

  // Convert to web-push format
  const webPushSubscriptions = subscriptions.map((sub) => ({
    endpoint: sub.endpoint,
    keys: {
      p256dh: sub.p256dh,
      auth: sub.auth,
    },
  }));

  // Send notifications
  const payload = JSON.stringify(options);
  const results = await Promise.allSettled(
    webPushSubscriptions.map((subscription) =>
      webpush.sendNotification(subscription, payload),
    ),
  );

  // Clean up expired subscriptions
  const expiredEndpoints = webPushSubscriptions
    .filter((_, index) => {
      const result = results[index];
      return result?.status === "rejected" && result.reason?.statusCode === 410;
    })
    .map((sub) => sub.endpoint);

  if (expiredEndpoints.length > 0) {
    await db
      .delete(pushSubscription)
      .where(inArray(pushSubscription.endpoint, expiredEndpoints));
  }

  return {
    sent: results.filter((r) => r.status === "fulfilled").length,
    failed: results.filter((r) => r.status === "rejected").length,
  };
}

/**
 * Send a push notification to a user by their userId
 */
export async function sendNotificationToUser(
  userId: string,
  options: NotificationOptions,
): Promise<{ sent: number; failed: number }> {
  // Find the user's account
  const accountData = await db.query.account.findFirst({
    where: eq(account.userId, userId),
  });

  if (!accountData) {
    return { sent: 0, failed: 0 };
  }

  return sendNotificationToAccount(accountData.id, options);
}
