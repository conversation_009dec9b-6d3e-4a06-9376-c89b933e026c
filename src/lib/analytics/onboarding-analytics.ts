"use client";

import type { PostHog, Properties } from "posthog-js";

export interface OnboardingAnalytics {
  // Step tracking
  trackOnboardingStarted: (userRole: string, userId?: string) => void;
  trackOnboardingStepViewed: (
    step: string,
    userRole: string,
    userId?: string,
  ) => void;
  trackOnboardingStepCompleted: (
    step: string,
    userRole: string,
    timeSpent: number,
    userId?: string,
  ) => void;
  trackOnboardingCompleted: (
    userRole: string,
    totalTime: number,
    userId?: string,
  ) => void;
  trackOnboardingAbandoned: (
    step: string,
    userRole: string,
    timeSpent: number,
    userId?: string,
  ) => void;

  // Action tracking
  trackPropertyWizardOpened: (source: string, userId?: string) => void;
  trackPropertyCreated: (
    propertyType: string,
    timeSpent: number,
    userId?: string,
  ) => void;
  trackContractorProfileCreated: (timeSpent: number, userId?: string) => void;

  // Error tracking
  trackOnboardingError: (
    step: string,
    error: string,
    userRole: string,
    userId?: string,
  ) => void;

  // Conversion tracking
  trackFirstProjectCreated: (
    daysAfterOnboarding: number,
    userId?: string,
  ) => void;
  trackFirstBidSubmitted: (
    daysAfterOnboarding: number,
    userId?: string,
  ) => void;
}

class OnboardingAnalyticsImpl implements OnboardingAnalytics {
  private posthog: PostHog | undefined = undefined;

  constructor(posthogInstance?: PostHog) {
    this.posthog = posthogInstance;
  }

  private getCommonProperties(userRole: string, userId?: string) {
    return {
      user_role: userRole,
      user_id: userId,
      timestamp: new Date().toISOString(),
      platform: "web",
    };
  }

  private capture(event: string, properties: Properties) {
    if (this.posthog && typeof window !== "undefined") {
      this.posthog.capture(event, properties);
    }
  }

  private setPersonProperties(properties: Properties) {
    if (this.posthog && typeof window !== "undefined") {
      this.posthog.setPersonProperties(properties);
    }
  }

  trackOnboardingStarted(userRole: string, userId?: string) {
    this.capture("onboarding_started", {
      ...this.getCommonProperties(userRole, userId),
    });
  }

  trackOnboardingStepViewed(step: string, userRole: string, userId?: string) {
    this.capture("onboarding_step_viewed", {
      ...this.getCommonProperties(userRole, userId),
      step,
    });
  }

  trackOnboardingStepCompleted(
    step: string,
    userRole: string,
    timeSpent: number,
    userId?: string,
  ) {
    this.capture("onboarding_step_completed", {
      ...this.getCommonProperties(userRole, userId),
      step,
      time_spent_seconds: timeSpent,
    });
  }

  trackOnboardingCompleted(
    userRole: string,
    totalTime: number,
    userId?: string,
  ) {
    this.capture("onboarding_completed", {
      ...this.getCommonProperties(userRole, userId),
      total_time_seconds: totalTime,
    });

    // Set user properties for completed onboarding
    this.setPersonProperties({
      onboarding_completed: true,
      onboarding_completion_date: new Date().toISOString(),
      onboarding_total_time: totalTime,
    });
  }

  trackOnboardingAbandoned(
    step: string,
    userRole: string,
    timeSpent: number,
    userId?: string,
  ) {
    this.capture("onboarding_abandoned", {
      ...this.getCommonProperties(userRole, userId),
      step,
      time_spent_seconds: timeSpent,
    });
  }

  trackPropertyWizardOpened(source: string, userId?: string) {
    this.capture("property_wizard_opened", {
      user_id: userId,
      source, // "onboarding", "dashboard", "properties_page"
      timestamp: new Date().toISOString(),
    });
  }

  trackPropertyCreated(
    propertyType: string,
    timeSpent: number,
    userId?: string,
  ) {
    this.capture("property_created", {
      user_id: userId,
      property_type: propertyType,
      time_spent_seconds: timeSpent,
      source: "onboarding",
      timestamp: new Date().toISOString(),
    });
  }

  trackContractorProfileCreated(timeSpent: number, userId?: string) {
    this.capture("contractor_profile_created", {
      user_id: userId,
      time_spent_seconds: timeSpent,
      source: "onboarding",
      timestamp: new Date().toISOString(),
    });
  }

  trackOnboardingError(
    step: string,
    error: string,
    userRole: string,
    userId?: string,
  ) {
    this.capture("onboarding_error", {
      ...this.getCommonProperties(userRole, userId),
      step,
      error_message: error,
    });
  }

  trackFirstProjectCreated(daysAfterOnboarding: number, userId?: string) {
    this.capture("first_project_created", {
      user_id: userId,
      days_after_onboarding: daysAfterOnboarding,
      timestamp: new Date().toISOString(),
    });
  }

  trackFirstBidSubmitted(daysAfterOnboarding: number, userId?: string) {
    this.capture("first_bid_submitted", {
      user_id: userId,
      days_after_onboarding: daysAfterOnboarding,
      timestamp: new Date().toISOString(),
    });
  }
}

// Factory function to create analytics instance with PostHog
export function createOnboardingAnalytics(
  posthogInstance: PostHog,
): OnboardingAnalytics {
  return new OnboardingAnalyticsImpl(posthogInstance);
}

// Utility function to calculate time spent
export function createTimeTracker() {
  const startTime = Date.now();

  return {
    getTimeSpent: () => Math.round((Date.now() - startTime) / 1000),
    reset: () => {
      const newStartTime = Date.now();
      return {
        getTimeSpent: () => Math.round((Date.now() - newStartTime) / 1000),
      };
    },
  };
}
