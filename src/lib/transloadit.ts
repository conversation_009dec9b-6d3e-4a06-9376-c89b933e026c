import crypto from "node:crypto";
import { env } from "@/env";

interface TransloaditTokenParams {
  template_id: string;
  expires: string;
  fields?: Record<string, unknown>;
}

export async function createTransloaditToken({
  template_id,
  expires,
  fields = {},
}: TransloaditTokenParams) {
  const authKey = env.TRANSLOADIT_KEY;
  const authSecret = env.TRANSLOADIT_SECRET;

  const params = {
    auth: { key: authKey },
    template_id,
    expires,
    fields,
  };

  const paramsJson = JSON.stringify(params);
  const signature = crypto
    .createHmac("sha384", authSecret)
    .update(Buffer.from(paramsJson, "utf-8"))
    .digest("hex");

  return { signature, params: paramsJson };
}

export function formatTransloaditUrl(assemblyUrl: string) {
  // Extract the last segment of the URL which contains the file name
  const fileName = assemblyUrl.split("/").pop();
  return `${env.NEXT_PUBLIC_UPLOAD_BASE_URL}/${fileName}`;
}
