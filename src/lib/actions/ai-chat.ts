"use server";

import type { UIMessage } from "ai";
import { and, eq, gt } from "drizzle-orm";
import { db } from "@/db";
import { aiChat, aiMessage } from "@/db/schema";

export const createChat = async () => {
  const [result] = await db.insert(aiChat).values({}).returning();

  return result?.id;
};

export const upsertMessage = async ({
  chatId,
  message,
  id,
}: {
  id: string;
  chatId: string;
  message: UIMessage;
}) => {
  const [result] = await db
    .insert(aiMessage)
    .values({
      chatId,
      parts: message.parts ?? [],
      role: message.role,
      id,
    })
    .onConflictDoUpdate({
      target: aiMessage.id,
      set: {
        parts: message.parts ?? [],
        chatId,
      },
    })
    .returning();
  return result;
};

export const loadChat = async (chatId: string) => {
  const messagesResult = await db
    .select()
    .from(aiMessage)
    .where(eq(aiMessage.chatId, chatId))
    .orderBy(aiMessage.createdAt);
  return messagesResult;
};

export const getChats = async () => {
  const c = await db.select().from(aiChat);
  return c;
};

export const deleteChat = async (chatId: string) => {
  await db.delete(aiChat).where(eq(aiChat.id, chatId));
};

export const deleteMessage = async (messageId: string) => {
  return await db.transaction(async (tx) => {
    const message = await tx
      .select()
      .from(aiMessage)
      .where(eq(aiMessage.id, messageId))
      .limit(1);

    if (message.length > 0) {
      const targetMessage = message[0];

      if (!targetMessage) {
        return false;
      }

      const removed = await tx
        .delete(aiMessage)
        .where(
          and(
            eq(aiMessage.chatId, targetMessage.chatId),
            gt(aiMessage.createdAt, targetMessage.createdAt),
          ),
        )
        .returning();

      await tx.delete(aiMessage).where(eq(aiMessage.id, messageId));

      return removed;
    }
    return false;
  });
};
