"use server";

import type { InferInsertModel } from "drizzle-orm";
import { db } from "@/db";
import { embeddings as embeddingsTable, resources } from "@/db/schema";
import { generateEmbeddings } from "../ai/embedding";

export const createResource = async (
  input: InferInsertModel<typeof resources>,
) => {
  const [resource] = await db.insert(resources).values(input).returning();

  const embeddings = await generateEmbeddings(input.content);

  await db.insert(embeddingsTable).values(
    embeddings.map((e) => ({
      resourceId: resource?.id,
      ...e,
    })),
  );

  return resource;
};
