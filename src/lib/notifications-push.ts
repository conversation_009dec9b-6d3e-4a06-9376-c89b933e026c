/**
 * Utility functions for sending push notifications
 * Uses the web push API endpoint at /api/notifications/[...path]
 */

/**
 * Subscribe to push notifications for the current user
 * @returns Promise that resolves when subscription is successful
 */
export async function subscribeToPushNotifications(): Promise<boolean> {
  try {
    // Request permission
    const permission = await Notification.requestPermission();
    if (permission !== "granted") {
      console.error("Notification permission denied");
      return false;
    }

    // Get service worker registration
    const registration = await navigator.serviceWorker.ready;

    // Get push subscription
    let subscription = await registration.pushManager.getSubscription();

    // If no subscription exists, create one
    if (!subscription) {
      // Get VAPID public key
      const response = await fetch("/api/notifications/vapid-key");
      const { publicKey } = await response.json();

      // Create new subscription
      subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: publicKey,
      });
    }

    // Send subscription to server
    const result = await fetch("/api/notifications/subscription", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ subscription }),
    });

    if (!result.ok) {
      throw new Error(`Failed to save subscription: ${result.statusText}`);
    }

    return true;
  } catch (error) {
    console.error("Error subscribing to push notifications:", error);
    return false;
  }
}

/**
 * Send a push notification
 * @param title Notification title
 * @param body Notification body text
 * @param options Additional options for the notification
 * @returns Promise that resolves when notification is sent
 */
export async function sendPushNotification(
  title: string,
  body: string,
  options?: {
    accountId?: string;
    data?: Record<string, unknown>;
    icon?: string;
    badge?: string;
    tag?: string;
    actions?: Array<{
      action: string;
      title: string;
      icon?: string;
    }>;
  },
): Promise<boolean> {
  try {
    const response = await fetch("/api/notifications/push", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        title,
        body,
        accountId: options?.accountId,
        icon: options?.icon || "/web-app-manifest-192x192.png",
        badge: options?.badge,
        tag: options?.tag,
        actions: options?.actions,
        data: options?.data,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send notification: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success;
  } catch (error) {
    console.error("Error sending push notification:", error);
    return false;
  }
}

/**
 * Check if push notifications are supported in this browser
 * @returns boolean indicating if push is supported
 */
export function isPushNotificationSupported(): boolean {
  return (
    "serviceWorker" in navigator &&
    "PushManager" in window &&
    "Notification" in window
  );
}

/**
 * Get current push notification permission status
 * @returns Permission status: 'granted', 'denied', or 'default'
 */
export function getPushNotificationPermission(): NotificationPermission {
  return Notification.permission;
}

/**
 * Unsubscribe from push notifications
 * @returns Promise that resolves when unsubscription is successful
 */
export async function unsubscribeFromPushNotifications(): Promise<boolean> {
  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();

    if (subscription) {
      await subscription.unsubscribe();
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error unsubscribing from push notifications:", error);
    return false;
  }
}
