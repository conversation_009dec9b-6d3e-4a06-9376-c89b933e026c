import { createAccessControl } from "better-auth/plugins/access";
import { adminAc, defaultStatements } from "better-auth/plugins/admin/access";

// Define all resources and actions in the system
const statement = {
	...defaultStatements,
	job: ["create", "read", "update", "delete", "publish", "award", "complete"],
	property: ["create", "read", "update", "delete"],
	bid: ["create", "read", "update", "delete", "accept", "reject", "withdraw"],
	organization: ["create", "read", "update", "delete", "manage"],
	schedule: ["create", "read", "update", "delete", "confirm", "reschedule"],
	message: ["create", "read"],
	review: ["create", "read"],
	service: ["create", "read", "update", "delete"],
	template: ["create", "read", "update", "delete"],
} as const;

export const ac = createAccessControl(statement);

// Define role permissions
export const admin = ac.newRole({
	...adminAc.statements,
	job: ["create", "read", "update", "delete", "publish", "award", "complete"],
	property: ["create", "read", "update", "delete"],
	bid: ["create", "read", "update", "delete", "accept", "reject", "withdraw"],
	organization: ["create", "read", "update", "delete", "manage"],
	schedule: ["create", "read", "update", "delete", "confirm", "reschedule"],
	message: ["create", "read"],
	review: ["create", "read"],
	service: ["create", "read", "update", "delete"],
	template: ["create", "read", "update", "delete"],
});

export const homeowner = ac.newRole({
	job: ["create", "read", "update", "delete", "publish", "award", "complete"],
	property: ["create", "read", "update", "delete"],
	bid: ["read", "accept", "reject"],
	organization: ["read"],
	schedule: ["read", "confirm", "reschedule"],
	message: ["create", "read"],
	review: ["create", "read"],
});

export const contractor = ac.newRole({
	job: ["read"],
	property: ["read"],
	bid: ["create", "read", "update", "withdraw"],
	organization: ["create", "read", "update", "manage"],
	schedule: ["create", "read", "update", "reschedule"],
	message: ["create", "read"],
	review: ["read"],
	service: ["create", "read", "update", "delete"],
});
