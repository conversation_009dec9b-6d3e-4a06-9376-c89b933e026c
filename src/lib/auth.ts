import { type BetterAuthOptions, betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import {
  admin as adminPlugin,
  haveIBeenPwned,
  twoFactor,
} from "better-auth/plugins";
import { cookies } from "next/headers";
import { db } from "@/db";
import { env } from "@/env";
import { sendEmailVerification } from "./email/send-email-verification";
import { sendPasswordReset } from "./email/send-password-reset";
import { ac, admin, contractor, homeowner } from "./permissions";

const options = {
  appName: "TradeCrews",
  database: drizzleAdapter(db, { provider: "pg" }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async ({ user, url, token }) => {
      await sendPasswordReset({
        email: user.email,
        resetCode: token,
        resetLink: url,
        userId: user.id,
      });
    },
  },
  socialProviders: {
    google: {
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    },
    microsoft: {
      clientId: env.MICROSOFT_CLIENT_ID,
      clientSecret: env.MICROSOFT_CLIENT_SECRET,
    },
  },
  plugins: [
    adminPlugin({
      ac,
      roles: {
        admin,
        homeowner,
        contractor,
      },
    }),
    twoFactor({
      issuer: "TradeCrews",
    }),
    haveIBeenPwned(),
    nextCookies(),
  ],
  emailVerification: {
    sendVerificationEmail: async ({ user, url, token }) => {
      await sendEmailVerification({
        email: user.email,
        verificationCode: token,
        verificationLink: url,
        userId: user.id,
      });
    },
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        required: true,
        defaultValue: "homeowner",
        input: true,
      },
      onboardingComplete: {
        type: "boolean",
        required: false,
        defaultValue: false,
        input: true,
      },
    },
  },
} satisfies BetterAuthOptions;

export const auth = betterAuth({
  ...options,
  plugins: [...options.plugins],
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          const cookieStore = await cookies();
          const accountType = cookieStore.get("signup_account_type")?.value;

          if (accountType) {
            cookieStore.delete("signup_account_type");
          }

          return {
            data: { ...user, role: accountType },
          };
        },
      },
    },
  },
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;
