import Uppy from "@uppy/core";
import Transloadit from "@uppy/transloadit";
import { env } from "@/env";

export type UppyOptions = {
  /**
   * User ID for the upload
   */
  userId?: string;

  /**
   * Type of upload (e.g., "property-image", "job-image")
   */
  type?: string;

  /**
   * Maximum number of files allowed
   */
  maxFiles?: number;

  /**
   * Maximum file size in bytes (default: 10MB)
   */
  maxFileSize?: number;

  /**
   * Allowed file types
   */
  allowedFileTypes?: string[];

  /**
   * Whether to automatically start uploading after file selection
   */
  autoProceed?: boolean;
};

/**
 * Creates a configured Uppy instance for file uploads
 */
export function createUppy({
  userId = "",
  type = "image",
  maxFiles = 1,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedFileTypes = ["image/*"],
  autoProceed = false,
}: UppyOptions = {}) {
  const uppy = new Uppy({
    restrictions: {
      maxNumberOfFiles: maxFiles,
      maxFileSize,
      allowedFileTypes,
    },
    meta: {
      type,
      userId,
    },
    autoProceed,
  });

  uppy.use(Transloadit, {
    async assemblyOptions() {
      const { meta } = uppy.getState();
      const body = JSON.stringify({ userId: meta.userId });
      const res = await fetch("/api/transloadit", { method: "POST", body });
      return res.json();
    },
    waitForEncoding: true,
  });

  return uppy;
}

/**
 * Formats the uploaded file URL using the storage URL from environment variables
 */
export function formatUploadedFileUrl(filename: string): string {
  return `${env.NEXT_PUBLIC_STORAGE_URL}${filename}`;
}
