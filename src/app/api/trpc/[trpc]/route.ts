import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from "@trpc/server/adapters/fetch";
import posthog from "posthog-js";
import { appRouter } from "@/lib/trpc";
import { createContext } from "@/lib/trpc/core/context";
import { ERROR_SEVERITY, safeError<PERSON>and<PERSON> } from "@/lib/trpc/core/errors";

function handler(req: Request) {
  return fetchRequestHandler({
    endpoint: "/api/trpc",
    req,
    router: appRouter,
    createContext,
    onError(opts) {
      // Enhanced error handling with standardized error processing
      const standardError = safeErrorHandler(
        opts.error,
        `${opts.type} ${opts.path}`,
      );

      // Structured error logging
      const errorLog = {
        type: opts.type,
        path: opts.path,
        input: opts.input,
        error: {
          code: standardError.code,
          message: standardError.message,
          category: standardError.category,
          severity: standardError.severity,
          details: standardError.details,
        },
        timestamp: standardError.timestamp,
      };

      // Log based on severity
      if (
        standardError.severity === ERROR_SEVERITY.CRITICAL ||
        standardError.severity === ERROR_SEVERITY.HIGH
      ) {
        console.error("TRPC Critical Error:", errorLog);
      } else if (standardError.severity === ERROR_SEVERITY.MEDIUM) {
        console.warn("TRPC Warning:", errorLog);
      } else {
        console.log("TRPC Info:", errorLog);
      }

      // Send to PostHog with enhanced context
      try {
        posthog.captureException(opts.error, {
          ...opts,
          standardError,
          errorCategory: standardError.category,
          errorSeverity: standardError.severity,
        });
      } catch (posthogError) {
        console.error("Failed to send error to PostHog:", posthogError);
      }
    },
  });
}

export { handler as GET, handler as POST };
