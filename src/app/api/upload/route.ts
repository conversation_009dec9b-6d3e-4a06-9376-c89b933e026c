import { NextResponse } from "next/server";
import { env } from "@/env";
import { createTransloaditToken } from "@/lib/transloadit";

export async function POST() {
  try {
    const token = await createTransloaditToken({
      template_id: env.TRANSLOADIT_TEMPLATE_ID,
      expires: new Date(Date.now() + 1800000).toISOString(), // 30 minutes from now
    });

    return NextResponse.json({ token });
  } catch (error) {
    console.error("Error generating upload token:", error);
    return NextResponse.json(
      { error: "Failed to generate upload token" },
      { status: 500 },
    );
  }
}
