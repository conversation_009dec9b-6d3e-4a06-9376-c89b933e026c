import crypto from "node:crypto";
import { type NextRequest, NextResponse } from "next/server";
import { env } from "@/env";

function utcDateString(ms: number): string {
  return new Date(ms)
    .toISOString()
    .replace(/-/g, "/")
    .replace(/T/, " ")
    .replace(/\.\d+Z$/, "+00:00");
}

export async function POST(request: NextRequest) {
  const expires = utcDateString(Date.now() + 1 * 60 * 60 * 1000);
  const authKey = env.TRANSLOADIT_KEY;
  const authSecret = env.TRANSLOADIT_SECRET;
  const templateId = env.TRANSLOADIT_TEMPLATE_ID;

  if (!authKey || !authSecret || !templateId) {
    return NextResponse.json(
      { error: "Missing Transloadit credentials" },
      { status: 500 },
    );
  }

  const body = await request.json();
  const params = JSON.stringify({
    auth: {
      key: authKey,
      expires: expires,
    },
    template_id: templateId,
    fields: {
      userId: body.userId,
    },
  });

  const signatureBytes = crypto
    .createHmac("sha384", authSecret)
    .update(Buffer.from(params, "utf-8"));
  const signature = `sha384:${signatureBytes.digest("hex")}`;

  return NextResponse.json({ expires, signature, params });
}
