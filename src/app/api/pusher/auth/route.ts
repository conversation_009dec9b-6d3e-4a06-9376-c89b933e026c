import { and, eq } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { chat, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { getPusherServer } from "@/lib/pusher-server";

export async function POST(req: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.text();
    const params = new URLSearchParams(body);
    const socketId = params.get("socket_id");
    const channelName = params.get("channel_name");

    if (!socketId || !channelName) {
      return NextResponse.json(
        { error: "Missing socket_id or channel_name" },
        { status: 400 },
      );
    }

    // Extract chatId from channel name (format: private-chat-{chatId})
    const chatIdMatch = channelName.match(/^private-chat-(.+)$/);
    if (!chatIdMatch) {
      return NextResponse.json(
        { error: "Invalid channel name format" },
        { status: 400 },
      );
    }

    const chatId = chatIdMatch[1];

    if (!chatId) {
      return NextResponse.json(
        { error: "Invalid chat ID in channel name" },
        { status: 400 },
      );
    }

    // Verify user has access to this chat
    const c = await db.query.chat.findFirst({
      where: eq(chat.id, chatId),
      with: {
        bid: {
          with: {
            job: {
              with: {
                property: {
                  with: {
                    user: true,
                  },
                },
              },
            },
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        },
        job: {
          with: {
            property: {
              with: {
                user: true,
              },
            },
          },
        },
      },
    });

    if (!c) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }

    let isPropertyOwner = false;
    let isBidder = false;

    if (c.bid) {
      // Bid-based chat logic
      const propertyOwnerMemberships = c.bid.job.property.user;
      const bidderMemberships = c.bid.organization.memberships;

      isPropertyOwner = propertyOwnerMemberships.id === userId;
      isBidder = bidderMemberships.some(
        (membership) => membership.userId === userId,
      );
    } else if (c.job) {
      // Job-based chat logic
      const propertyOwnerMemberships = c.job.property.user;

      isPropertyOwner = propertyOwnerMemberships.id === userId;

      // For job chats, any professional can participate
      const isProfessional = await db.query.user.findFirst({
        where: and(eq(user.id, userId), eq(user.role, "contractor")),
      });

      isBidder = !!isProfessional;
    }

    if (!isPropertyOwner && !isBidder) {
      return NextResponse.json(
        { error: "Access denied to this chat" },
        { status: 403 },
      );
    }

    // Authenticate the user for this private channel
    const pusher = getPusherServer();
    const authResponse = pusher.authorizeChannel(socketId, channelName, {
      user_id: userId,
      user_info: {
        name: userId,
        role: isPropertyOwner ? "homeowner" : "professional",
      },
    });

    return NextResponse.json(authResponse);
  } catch (error) {
    console.error("Pusher auth error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
