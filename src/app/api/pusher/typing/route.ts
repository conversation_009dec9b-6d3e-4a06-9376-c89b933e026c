import { and, eq } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";
import { db } from "@/db";
import { chat, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { triggerTypingEvent } from "@/lib/pusher-server";

const typingSchema = z.object({
  chatId: z.string(),
  userId: z.string(),
  isTyping: z.boolean(),
  userName: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const {
      chatId,
      userId: requestUserId,
      isTyping,
    } = typingSchema.parse(body);

    // Verify the requesting user matches the authenticated user
    if (userId !== requestUserId) {
      return NextResponse.json({ error: "User mismatch" }, { status: 403 });
    }

    // Verify user has access to this chat
    const c = await db.query.chat.findFirst({
      where: eq(chat.id, chatId),
      with: {
        bid: {
          with: {
            job: {
              with: {
                property: {
                  with: {
                    user: true,
                  },
                },
              },
            },
            organization: {
              with: {
                memberships: true,
              },
            },
          },
        },
        job: {
          with: {
            property: {
              with: {
                user: true,
              },
            },
          },
        },
      },
    });

    if (!c) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }

    let isPropertyOwner = false;
    let isBidder = false;

    if (c.bid) {
      // Bid-based chat logic
      const propertyOwnerMemberships = c.bid.job.property.user;
      const bidderMemberships = c.bid.organization.memberships;

      isPropertyOwner = propertyOwnerMemberships.id === userId;
      isBidder = bidderMemberships.some(
        (membership) => membership.userId === userId,
      );
    } else if (c.job) {
      // Job-based chat logic
      const propertyOwnerMemberships = c.job.property.user;

      isPropertyOwner = propertyOwnerMemberships.id === userId;

      // For job chats, any professional can participate
      const isProfessional = await db.query.user.findFirst({
        where: and(eq(user.id, userId), eq(user.role, "contractor")),
      });

      isBidder = !!isProfessional;
    }

    if (!isPropertyOwner && !isBidder) {
      return NextResponse.json(
        { error: "Access denied to this chat" },
        { status: 403 },
      );
    }

    // Trigger typing event
    await triggerTypingEvent(chatId, userId, isTyping, body.userName);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Typing indicator error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
