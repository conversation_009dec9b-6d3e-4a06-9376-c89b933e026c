import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import type { NextApiRequest } from "next-ts-api";
import OpenAI from "openai";
import { db } from "@/db";
import { bid, job, type Message } from "@/db/schema";
import { env } from "@/env";
import { auth } from "@/lib/auth";

const openai = new OpenAI({
  apiKey: env.OPENAI_API_KEY,
});

export async function POST(
  req: NextApiRequest<{
    messageHistory: Message[];
    bidId?: string;
    jobId?: string;
  }>,
) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { messageHistory, bidId, jobId } = await req.json();

    // Ensure either bidId or jobId is provided
    if (!bidId && !jobId) {
      return NextResponse.json(
        { error: "Either bidId or jobId must be provided" },
        { status: 400 },
      );
    }

    let projectContext = "";

    if (bidId) {
      // Get project details for bid chat
      const bidData = await db.query.bid.findFirst({
        where: eq(bid.id, bidId),
        with: {
          job: {
            with: {
              property: true,
              tasks: true,
            },
          },
        },
      });

      if (!bidData) {
        return NextResponse.json({ error: "Bid not found" }, { status: 404 });
      }

      // Create project context for bid
      projectContext = `
Project: ${bidData.job.name}
Budget: $${bidData.job.budget}
Timeline: ${bidData.job.startsAt.toLocaleDateString()} to ${bidData.job.deadline.toLocaleDateString()}
Tasks: ${bidData.job.tasks.map((task) => task.name).join(", ")}
Bid Amount: $${bidData.amount}
`;
    } else if (jobId) {
      // Get project details for job chat
      const jobData = await db.query.job.findFirst({
        where: eq(job.id, jobId),
        with: {
          property: true,
          tasks: true,
        },
      });

      if (!jobData) {
        return NextResponse.json({ error: "Job not found" }, { status: 404 });
      }

      // Create project context for job
      projectContext = `
Project: ${jobData.name}
Budget: $${jobData.budget}
Timeline: ${jobData.startsAt.toLocaleDateString()} to ${jobData.deadline.toLocaleDateString()}
Tasks: ${jobData.tasks.map((task) => task.name).join(", ")}
Property: ${jobData.property.name}
`;
    }

    // Format message history for the AI
    const formattedHistory = messageHistory
      .map((msg) => `${msg.senderType}: ${msg.content}`)
      .join("\n");

    const response = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        {
          role: "system",
          content:
            "Generate 3-4 relevant follow-up questions for a conversation between a homeowner and contractor about a construction project. Keep questions brief, practical and focused on project details, timeline, or clarifications.",
        },
        {
          role: "user",
          content: `Project Context:\n${projectContext}\n\nHere's the recent conversation:\n${formattedHistory}\n\nSuggest follow-up questions relevant to this specific project.`,
        },
      ],
      temperature: 0.7,
      max_tokens: 150,
    });

    // Parse the response to extract questions
    const suggestions = response.choices[0]?.message.content
      ?.split(/\d+\.|\n/)
      .map((q) => q.trim())
      .filter((q) => q.length > 0 && q.endsWith("?"))
      .slice(0, 4);

    return NextResponse.json({ suggestions });
  } catch (error) {
    console.error("Error generating suggestions:", error);
    return NextResponse.json(
      { error: "Failed to generate suggestions" },
      { status: 500 },
    );
  }
}
