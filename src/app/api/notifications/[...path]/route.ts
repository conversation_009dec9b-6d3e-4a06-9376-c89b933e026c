import { eq, inArray } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";
import type { PushSubscription } from "web-push";
import webpush from "web-push";
import { db } from "@/db";
import { account, pushSubscription } from "@/db/schema";
import { env } from "@/env";
import { auth } from "@/lib/auth";

webpush.setVapidDetails(
  "mailto:<EMAIL>",
  env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  env.VAPID_PRIVATE_KEY,
);

export async function POST(req: NextRequest) {
  const { pathname } = new URL(req.url);
  switch (pathname) {
    case "/api/notifications/subscription":
      return handleSubscription(req);
    case "/api/notifications/push":
      return handlePush(req);
    default:
      return NextResponse.json({ error: "Not found" }, { status: 404 });
  }
}

export async function GET(req: NextRequest) {
  const { pathname } = new URL(req.url);
  switch (pathname) {
    case "/api/notifications/vapid-key":
      return NextResponse.json({ publicKey: env.NEXT_PUBLIC_VAPID_PUBLIC_KEY });
    default:
      return NextResponse.json({ error: "Not found" }, { status: 404 });
  }
}

async function handleSubscription(request: NextRequest) {
  const session = await auth.api.getSession({ headers: request.headers });
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body: { subscription: PushSubscription } = await request.json();
  const subscription = body.subscription;

  // Find the user's account
  const a = await db.query.account.findFirst({
    where: eq(account.userId, userId),
  });

  if (!a) {
    return NextResponse.json({ error: "Account not found" }, { status: 404 });
  }

  // Store subscription in database
  await db
    .insert(pushSubscription)
    .values({
      endpoint: subscription.endpoint,
      p256dh: subscription.keys.p256dh,
      auth: subscription.keys.auth,
      accountId: a.id,
    })
    .onConflictDoUpdate({
      target: pushSubscription.endpoint,
      set: {
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
        accountId: a.id,
      },
    });

  return NextResponse.json({ success: true });
}

async function handlePush(request: NextRequest) {
  const body = await request.json();
  const payload = JSON.stringify(body);

  // Get target account ID if specified
  const targetAccountId = body.accountId;

  const subscriptions = await db.query.pushSubscription.findMany({
    where: eq(pushSubscription.accountId, targetAccountId),
  });

  if (subscriptions.length === 0) {
    return NextResponse.json({
      success: false,
      message: "No subscriptions found",
    });
  }

  // Convert DB subscriptions to web-push format
  const webPushSubscriptions = subscriptions.map((sub) => ({
    endpoint: sub.endpoint,
    keys: {
      p256dh: sub.p256dh,
      auth: sub.auth,
    },
  }));

  // Send to all subscriptions
  const results = await Promise.allSettled(
    webPushSubscriptions.map((subscription) =>
      webpush.sendNotification(subscription, payload),
    ),
  );

  // Remove expired subscriptions
  const expiredEndpoints = webPushSubscriptions
    .filter((_, index) => {
      const result = results[index];
      return result?.status === "rejected" && result.reason?.statusCode === 410;
    })
    .map((sub) => sub.endpoint);

  if (expiredEndpoints.length > 0) {
    await db
      .delete(pushSubscription)
      .where(inArray(pushSubscription.endpoint, expiredEndpoints));
  }

  return NextResponse.json({
    success: true,
    sent: results.filter((r) => r.status === "fulfilled").length,
    failed: results.filter((r) => r.status === "rejected").length,
  });
}
