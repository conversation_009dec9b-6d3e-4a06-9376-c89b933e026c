import { openai } from "@ai-sdk/openai";
import { createIdGenerator, streamText } from "ai";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { createChat, upsertMessage } from "@/lib/actions/ai-chat";
import { systemPrompt } from "@/lib/ai/prompts";
import { auth } from "@/lib/auth";

const generateId = createIdGenerator({
  prefix: "msgc",
  size: 16,
});

export async function POST() {
  try {
    // Get user session
    const session = await auth.api.getSession({ headers: await headers() });
    const user = session?.user;

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create the chat
    const chatId = await createChat();

    if (!chatId) {
      return NextResponse.json(
        { error: "Failed to create chat" },
        { status: 500 },
      );
    }

    // Generate Jack's initial message
    const initialMessageId = generateId();

    const result = streamText({
      model: openai("gpt-4o-mini"),
      system: systemPrompt({
        userName: user.name as string,
        userRole: user.role as string,
        userId: user.id as string,
      }),
      messages: [],
      prompt: `This is the start of a new conversation. You are Jack, the friendly AI assistant for TradeCrews! 

Greet ${user.name} warmly by name and introduce yourself with enthusiasm. Based on their role as a ${user.role}, offer 2-3 specific, actionable ways you can help them today. 

Make your introduction:
- Warm and welcoming 
- Professional but friendly
- Specific to their role (${user.role})
- Include concrete examples of what you can do
- End with an engaging question to start the conversation

Keep it concise but personable - you want them to feel excited to chat with you!`,
    });

    // Get the full text from the stream
    let initialMessageContent = "";
    for await (const textPart of result.textStream) {
      initialMessageContent += textPart;
    }

    // Save Jack's initial message to the database
    await upsertMessage({
      id: initialMessageId,
      chatId,
      message: {
        id: initialMessageId,
        role: "assistant",
        content: initialMessageContent,
        parts: [
          {
            type: "text",
            text: initialMessageContent,
          },
        ],
        createdAt: new Date(),
      },
    });

    return NextResponse.json({ chatId });
  } catch (error) {
    console.error("Error creating chat:", error);
    return NextResponse.json(
      { error: "Failed to create chat" },
      { status: 500 },
    );
  }
}
