import { createAISDKTools } from "@agentic/ai-sdk";
import { WikipediaClient } from "@agentic/wikipedia";
import { openai } from "@ai-sdk/openai";
import { withTracing } from "@posthog/ai";
import { streamText, tool, type UIMessage } from "ai";
import { headers } from "next/headers";
import { PostHog } from "posthog-node";
import { z } from "zod";
import { env } from "@/env";
import { loadChat, upsertMessage } from "@/lib/actions/ai-chat";
import { createResource } from "@/lib/actions/resources";
import { systemPrompt } from "@/lib/ai/prompts";
// Admin tools
import { getUserStats } from "@/lib/ai/tools/admin/getUserStats";
import {
  banUser,
  changeUserRole,
  searchUsers,
} from "@/lib/ai/tools/admin/manageUser";
import { getPlatformInsights } from "@/lib/ai/tools/admin/platformInsights";
// Platform action tools
import {
  publishProject,
  acceptBid,
  scheduleProject,
  completeProject,
} from "@/lib/ai/tools/platform/projectActions";
import {
  submitBid,
  withdrawBid,
  updateBid,
} from "@/lib/ai/tools/platform/bidActions";
import {
  navigateToPage,
  searchPlatform,
  getQuickActions,
} from "@/lib/ai/tools/platform/navigationActions";
// Existing tools
import { createProject } from "@/lib/ai/tools/createProject";
import { createTemplate } from "@/lib/ai/tools/createTemplates";
import { createTrade, findTradeId } from "@/lib/ai/tools/createTrade";
import { getCompanies } from "@/lib/ai/tools/getCompanies";
import {
  analyzeBidCompetition,
  findRelevantJobs,
  getContractorPerformance,
} from "@/lib/ai/tools/user/contractorTools";
// User tools
import {
  analyzeProjectBids,
  getProjectRecommendations,
  getProjectTimeline,
} from "@/lib/ai/tools/user/projectAssistant";
import { auth } from "@/lib/auth";

export const maxDuration = 30;

export async function POST(req: Request) {
  const { message, chatId }: { message: UIMessage; chatId: string } =
    await req.json();

  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  const phClient = new PostHog(env.NEXT_PUBLIC_POSTHOG_KEY, {
    host: env.NEXT_PUBLIC_POSTHOG_HOST,
  });

  const model = withTracing(openai("gpt-4.1-mini"), phClient, {
    posthogDistinctId: user?.id,
    posthogProperties: { conversation_id: chatId },
  });

  await upsertMessage({ chatId, message, id: message.id });

  const previousMessages = await loadChat(chatId);

  // Convert to the format expected by streamText
  const messages = [
    ...previousMessages.map((m) => ({
      role: m.role,
      content:
        m.parts
          ?.map((part) => {
            if (part.type === "text") return part.text;
            return "";
          })
          .join("") || "",
    })),
    {
      role: message.role,
      content: message.content || "",
    },
  ];

  const wikipedia = new WikipediaClient();

  // Create role-based tools
  const baseTools = {
    addResource: tool({
      description:
        "add a resource to your knowledge base. If the user provides a random piece of knowledge unprompted, use this tool to add it to your knowledge base.",
      parameters: z.object({
        content: z.string().describe("The content of the resource"),
      }),
      execute: async ({ content }) => createResource({ content }),
    }),
    // Navigation and search tools (available to all users)
    navigateToPage: navigateToPage(),
    searchPlatform: searchPlatform(),
    getQuickActions: getQuickActions(),
    // Project creation tools
    createProject: createProject(),
    getCompanies: getCompanies(),
    createTemplate: createTemplate(),
    createTrade: createTrade(),
    findTradeId: findTradeId(),
    testTool: tool({
      description: "Test tool to verify AI agent is working correctly",
      parameters: z.object({
        message: z.string().describe("Test message to echo back"),
      }),
      execute: async ({ message }) => {
        return `AI Agent Test Successful! You sent: "${message}"`;
      },
    }),
    checkUserRole: tool({
      description:
        "Check what role the current user has and what tools are available",
      parameters: z.object({}),
      execute: async () => {
        return JSON.stringify(
          {
            message: "Role check completed",
            userRole: user?.role || "unknown",
            userName: user?.name || "unknown",
            availableFeatures: [
              "Basic chat functionality",
              "Knowledge base access",
              "Role-specific tools based on user permissions",
              "Platform navigation assistance",
              "Project and bid management tools",
            ],
          },
          null,
          2,
        );
      },
    }),
    ...createAISDKTools(wikipedia),
  };

  // Add role-specific tools
  const roleSpecificTools: Record<string, unknown> = {};

  if (user?.role === "admin") {
    roleSpecificTools.getUserStats = getUserStats();
    roleSpecificTools.searchUsers = searchUsers();
    roleSpecificTools.changeUserRole = changeUserRole();
    roleSpecificTools.banUser = banUser();
    roleSpecificTools.getPlatformInsights = getPlatformInsights();
  }

  if (user?.role === "homeowner") {
    // Analysis tools
    roleSpecificTools.getProjectRecommendations = getProjectRecommendations();
    roleSpecificTools.analyzeProjectBids = analyzeProjectBids();
    roleSpecificTools.getProjectTimeline = getProjectTimeline();
    // Action tools
    roleSpecificTools.publishProject = publishProject();
    roleSpecificTools.acceptBid = acceptBid();
    roleSpecificTools.scheduleProject = scheduleProject();
    roleSpecificTools.completeProject = completeProject();
  }

  if (user?.role === "contractor") {
    // Analysis tools
    roleSpecificTools.findRelevantJobs = findRelevantJobs();
    roleSpecificTools.analyzeBidCompetition = analyzeBidCompetition();
    roleSpecificTools.getContractorPerformance = getContractorPerformance();
    // Action tools
    roleSpecificTools.submitBid = submitBid();
    roleSpecificTools.withdrawBid = withdrawBid();
    roleSpecificTools.updateBid = updateBid();
  }

  const allTools = { ...baseTools, ...roleSpecificTools };

  const result = streamText({
    model,
    system: systemPrompt({
      userName: user?.name as string,
      userRole: user?.role as string,
      userId: user?.id as string,
    }),
    messages,
    tools: allTools,
    maxSteps: 5,
  });

  phClient.shutdown();

  return result.toDataStreamResponse();
}
