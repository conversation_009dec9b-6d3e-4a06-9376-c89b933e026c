import Link from "next/link";
import { SignUpForm } from "@/components/features/auth/sign-up-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function SignUpPage() {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Create a Homeowner Account</CardTitle>
          <CardDescription>
            Sign up to find qualified professionals for your home projects
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SignUpForm accountType="homeowner" redirectUrl="/onboarding" />
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-muted-foreground text-sm">
            Already have an account?{" "}
            <Button variant="link" className="p-0" asChild>
              <Link href="/sign-in">Sign in</Link>
            </Button>
          </div>
          <div className="text-muted-foreground text-sm">
            Are you a contractor?{" "}
            <Button variant="link" className="p-0" asChild>
              <Link href="/sign-up/contractor">Sign up as a contractor</Link>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
