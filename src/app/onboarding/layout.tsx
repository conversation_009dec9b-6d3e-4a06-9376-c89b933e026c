"use client";

import { redirect, useRouter } from "next/navigation";
import { ErrorBoundary } from "react-error-boundary";
import { useSession } from "@/lib/auth-client";

export default function OnboardingLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { data: session } = useSession();
  const router = useRouter();

  if (!session) {
    redirect("/sign-in");
  }

  const user = session.user;

  // Check if user has completed onboarding
  if (user.onboardingComplete) {
    router.push("/dashboard");
  }

  return (
    <>
      <ErrorBoundary fallback={<div>Something went wrong</div>}>
        {children}
      </ErrorBoundary>
    </>
  );
}
