import { BookOpenIcon, TrendingUpIcon } from "lucide-react";
import { headers } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { auth } from "@/lib/auth";

export default async function HelpPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session) {
    redirect("/sign-in");
  }

  const helpTopics = [
    {
      title: "Bidding Tips & Strategies",
      description:
        "Learn how to create competitive bids and evaluate proposals effectively.",
      href: "/help/bidding-tips",
      icon: TrendingUpIcon,
      category: "Bidding",
    },
  ];

  return (
    <div className="container mx-auto space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-50">
          <BookOpenIcon className="h-8 w-8 text-blue-600" />
        </div>
        <h1 className="mb-4 font-bold text-3xl text-gray-900 sm:text-4xl">
          Help Center
        </h1>
        <p className="mx-auto max-w-2xl text-gray-600 text-lg">
          Find guides, tips, and resources to help you succeed on TradeCrews.
        </p>
      </div>

      {/* Help Topics */}
      <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {helpTopics.map((topic) => (
          <Link key={topic.href} href={topic.href}>
            <Card className="h-full transition-colors hover:bg-gray-50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100">
                    <topic.icon className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{topic.title}</CardTitle>
                    <div className="text-muted-foreground text-xs">
                      {topic.category}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>{topic.description}</CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Coming Soon */}
      <Card className="border-dashed">
        <CardContent className="p-6 text-center">
          <h3 className="mb-2 font-semibold text-gray-900">
            More Help Topics Coming Soon
          </h3>
          <p className="text-gray-600 text-sm">
            We're continuously adding new guides and resources to help you
            succeed.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
