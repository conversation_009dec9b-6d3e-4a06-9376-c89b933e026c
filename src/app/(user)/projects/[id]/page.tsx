import { headers } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { PageLayout } from "@/components/core/page-layout";
import { JackChatButton } from "@/components/features/chat/jack-chat-button";
import { EnhancedProjectActions } from "@/components/features/projects/enhanced-project-actions";
import { EnhancedProjectDetailContent } from "@/components/features/projects/enhanced-project-detail-content";
import {
  getQueryClient,
  HydrateClient,
  trpc,
} from "@/components/integrations/trpc/server";
import {
  MobileActionBar,
  MobileActionBarItem,
} from "@/components/ui/mobile-action-bar";
import { Skeleton } from "@/components/ui/skeleton";
import { auth } from "@/lib/auth";

function ProjectDetailSkeleton() {
  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Responsive skeleton layout */}
      <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
        {/* Main content skeleton - 2/3 on desktop */}
        <div className="space-y-4 sm:space-y-6 lg:col-span-2">
          <Skeleton className="h-32 w-full rounded-lg" />

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <Skeleton className="h-20 w-full rounded-lg" />
            <Skeleton className="h-20 w-full rounded-lg" />
            <Skeleton className="h-20 w-full rounded-lg" />
          </div>

          <Skeleton className="h-48 w-full rounded-lg" />
          <Skeleton className="h-32 w-full rounded-lg" />
        </div>

        {/* Sidebar skeleton - 1/3 on desktop */}
        <div className="space-y-4 sm:space-y-6">
          <Skeleton className="h-64 w-full rounded-lg" />
          <Skeleton className="h-32 w-full rounded-lg" />
        </div>
      </div>

      {/* Bottom spacing for mobile actionbar only */}
      <div className="h-20 md:h-0" />
    </div>
  );
}

type Props = {
  params: Promise<{ id: string }>;
};

export default async function JobDetailPage({ params }: Props) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();

  // Fetch the job data
  const job = await queryClient.fetchQuery(
    trpc.projects.getById.queryOptions({
      id,
    }),
  );

  if (!job) {
    notFound();
  }

  // Prefetch related data
  await Promise.all([
    // Prefetch bids if it's a standard job
    job.jobType !== "QUICK_HIRE" && job.bids?.length
      ? Promise.all(
          job.bids.map((bid) =>
            queryClient.prefetchQuery(
              trpc.bids.getById.queryOptions({ id: bid.id }),
            ),
          ),
        )
      : Promise.resolve(),

    // Prefetch crew data if there's an accepted bid
    (() => {
      const acceptedBid = job.bids?.find((bid) => bid.status === "ACCEPTED");
      return acceptedBid
        ? queryClient.prefetchQuery(
            trpc.contractor.getCrewMembers.queryOptions({
              organizationId: acceptedBid.organizationId,
            }),
          )
        : Promise.resolve();
    })(),
  ]);

  return (
    <>
      {/* Mobile-First Page Layout with Desktop Actions */}
      <PageLayout
        title={job.name}
        actions={
          <div className="hidden gap-2 md:flex">
            <EnhancedProjectActions
              job={job as any}
              userRole={session.user.role}
              layout="horizontal"
            />
          </div>
        }
      >
        <HydrateClient>
          <Suspense fallback={<ProjectDetailSkeleton />}>
            <EnhancedProjectDetailContent
              jobId={id}
              userId={session.user.id}
              userRole={session.user.role}
            />
          </Suspense>
        </HydrateClient>
      </PageLayout>

      {/* Mobile Action Bar with Jack - Mobile Only */}
      <MobileActionBar variant="elevated">
        <MobileActionBarItem flex>
          <EnhancedProjectActions
            job={job as any}
            userRole={session.user.role}
            layout="actionbar"
          />
        </MobileActionBarItem>

        <MobileActionBarItem>
          <JackChatButton variant="actionbar" size="md" showLabel jobId={id} />
        </MobileActionBarItem>
      </MobileActionBar>

      {/* Desktop Jack FAB - Hidden on mobile */}
      <div className="hidden md:block">
        <JackChatButton variant="fab" size="lg" jobId={id} />
      </div>
    </>
  );
}
