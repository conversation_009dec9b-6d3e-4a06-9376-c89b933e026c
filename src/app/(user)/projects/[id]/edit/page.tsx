import { PageLayout } from "@/components/core/page-layout";
import { ProjectForm } from "@/components/features/projects/project-form";
import { caller } from "@/lib/trpc";

export default async function EditJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const job = await caller.projects.getById({ id });

  if (!job) {
    throw new Error("Job not found");
  }

  return (
    <PageLayout title="Edit Job">
      <div className="p-8">
        <ProjectForm initialData={job} propertyId={job.propertyId} />
      </div>
    </PageLayout>
  );
}
