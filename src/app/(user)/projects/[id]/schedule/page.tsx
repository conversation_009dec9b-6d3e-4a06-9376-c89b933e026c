import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { notFound, redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { ScheduleForm } from "@/components/features/scheduling/schedule-form";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { caller } from "@/lib/trpc";

export default async function SchedulePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const job = await caller.projects.getById({ id });
  // Get the most recent schedule if any
  const schedules = await caller.projects.getSchedules({ jobId: id });
  const latestSchedule = schedules?.[0];

  if (!job) {
    notFound();
  }

  return (
    <PageLayout title="Job Schedule">
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Job Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-medium">{job.name}</p>
                <p>Property: {job.property?.name}</p>
                <div className="mt-2 flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground text-sm">
                    Original timeline: {format(job.startsAt, "PPP")} to{" "}
                    {format(job.deadline, "PPP")}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                {latestSchedule ? "Create New Schedule" : "Schedule Job"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScheduleForm
                jobId={job.id}
                onComplete={() => {
                  redirect(`/jobs/${job.id}`);
                }}
              />
            </CardContent>
          </Card>

          {schedules && schedules.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Previous Schedules</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {schedules.map((schedule) => (
                    <div key={schedule.id} className="rounded-md border p-4">
                      <div className="flex justify-between">
                        <div>
                          <p className="font-medium">
                            {schedule.status} Schedule
                          </p>
                          <p className="text-muted-foreground text-sm">
                            {format(
                              new Date(schedule.proposedStartDate),
                              "PPP",
                            )}{" "}
                            to{" "}
                            {format(new Date(schedule.proposedEndDate), "PPP")}
                          </p>
                        </div>
                        <Badge>{schedule.status}</Badge>
                      </div>
                      {schedule.notes && (
                        <p className="mt-2 text-sm">{schedule.notes}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </PageLayout>
  );
}
