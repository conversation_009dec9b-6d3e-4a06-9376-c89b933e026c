"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { PageLayout } from "@/components/core/page-layout";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export default function CancelProjectPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const trpc = useTRPC();
  const [confirmed, setConfirmed] = useState(false);

  const { data: job } = useQuery(
    trpc.projects.getById.queryOptions({
      id,
    }),
  );

  const cancelJob = useMutation(
    trpc.projects.cancel.mutationOptions({
      onSuccess: () => {
        toast.success("Project cancelled successfully");
        router.push("/projects");
      },
      onError: (error) => {
        toast.error(`Error cancelling project: ${error.message}`);
      },
    }),
  );

  const handleCancel = () => {
    if (!confirmed) {
      toast.error("Please confirm that you want to cancel this project");
      return;
    }

    cancelJob.mutate({ id });
  };

  if (!job) {
    return <div>Loading...</div>;
  }

  return (
    <PageLayout title="Cancel Project">
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Cancel Project</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6 space-y-2">
                <p>You are about to cancel the following project:</p>
                <p className="font-medium">{job.name}</p>
                <p>Property: {job.property?.name}</p>
                <div className="mt-4 rounded-md border border-yellow-200 bg-yellow-50 p-3 text-sm">
                  <p className="font-medium text-yellow-800">
                    Warning: Cancelling this project will:
                  </p>
                  <ul className="mt-2 list-inside list-disc text-yellow-700">
                    <li>Remove it from public listings</li>
                    <li>Notify any contractors who have submitted bids</li>
                    <li>Mark the project as cancelled</li>
                  </ul>
                </div>
              </div>

              <div className="mb-6 flex items-center space-x-2">
                <Checkbox
                  checked={confirmed}
                  onCheckedChange={(checked) => setConfirmed(checked === true)}
                />
                <Label htmlFor="confirm">
                  I understand and want to cancel this project
                </Label>
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  Go Back
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleCancel}
                  disabled={cancelJob.isPending || !confirmed}
                >
                  {cancelJob.isPending ? "Cancelling..." : "Cancel Project"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
