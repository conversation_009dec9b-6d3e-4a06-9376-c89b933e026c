import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { CompleteProjectForm } from "@/components/features/projects/complete-project-form";
import { getQueryClient, trpc } from "@/components/integrations/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "@/lib/auth";

export default async function CompleteJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(
    trpc.projects.getById.queryOptions({ id }),
  );

  if (!job || job.status !== "AWARDED") {
    redirect(`/projects/${id}`);
  }

  return (
    <PageLayout title="Complete Project">
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Mark Project as Complete</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6 space-y-2">
                <p>You are about to mark the following project as complete:</p>
                <p className="font-medium">{job.name}</p>
                <p>Property: {job.property?.name}</p>
                {job.homeownerCompleted && !job.contractorCompleted && (
                  <div className="mt-4 rounded-md border border-yellow-200 bg-yellow-50 p-3 text-sm">
                    <p className="font-medium text-yellow-800">
                      Homeowner has marked this project as complete
                    </p>
                    <p className="text-yellow-700">
                      Waiting for contractor confirmation
                    </p>
                  </div>
                )}
                {!job.homeownerCompleted && job.contractorCompleted && (
                  <div className="mt-4 rounded-md border border-yellow-200 bg-yellow-50 p-3 text-sm">
                    <p className="font-medium text-yellow-800">
                      Contractor has marked this project as complete
                    </p>
                    <p className="text-yellow-700">
                      Waiting for homeowner confirmation
                    </p>
                  </div>
                )}
              </div>
              <CompleteProjectForm
                projectId={id}
                homeownerCompleted={job.homeownerCompleted}
                contractorCompleted={job.contractorCompleted}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
