import { format } from "date-fns";
import {
  BuildingIcon,
  MailIcon,
  MapPinIcon,
  PhoneIcon,
  StarIcon,
} from "lucide-react";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { CrewList } from "@/components/features/contractors/crew-list";
import { getQueryClient, trpc } from "@/components/integrations/trpc/server";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "@/lib/auth";

export default async function ContractorProfilePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();
  const contractor = await queryClient.fetchQuery(
    trpc.contractor.getById.queryOptions({ id }),
  );

  if (!contractor) {
    redirect("/dashboard");
  }

  // Fetch reviews for this contractor
  const reviews = await queryClient.fetchQuery(
    trpc.reviews.listForOrganization.queryOptions({ organizationId: id }),
  );

  // Calculate average rating
  const averageRating =
    reviews && reviews.length > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      : 0;

  return (
    <PageLayout title={contractor.name}>
      <div className="p-8">
        <div className="grid gap-6 md:grid-cols-3">
          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle>Business Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="flex items-center gap-2 font-medium">
                    <BuildingIcon className="h-4 w-4 text-orange-500" />
                    Organization
                  </h3>
                  <p>{contractor.name}</p>
                  {contractor.trade && (
                    <Badge variant="outline" className="mt-1">
                      {contractor.trade.name}
                    </Badge>
                  )}
                </div>

                {contractor.address && (
                  <div>
                    <h3 className="flex items-center gap-2 font-medium">
                      <MapPinIcon className="h-4 w-4 text-orange-500" />
                      Address
                    </h3>
                    <p>
                      {contractor.address.street}, {contractor.address.city},{" "}
                      {contractor.address.state} {contractor.address.zip}
                    </p>
                  </div>
                )}

                {contractor.phone && (
                  <div>
                    <h3 className="flex items-center gap-2 font-medium">
                      <PhoneIcon className="h-4 w-4 text-orange-500" />
                      Phone
                    </h3>
                    <p>{contractor.phone}</p>
                  </div>
                )}

                {contractor.email && (
                  <div>
                    <h3 className="flex items-center gap-2 font-medium">
                      <MailIcon className="h-4 w-4 text-orange-500" />
                      Email
                    </h3>
                    <p>{contractor.email}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Reviews Section */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Reviews</CardTitle>
                {reviews && reviews.length > 0 && (
                  <div className="flex items-center">
                    <span className="mr-2 font-medium">
                      {averageRating.toFixed(1)}
                    </span>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <StarIcon
                          key={star}
                          className={`h-5 w-5 ${
                            star <= Math.round(averageRating)
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="ml-2 text-muted-foreground text-sm">
                      ({reviews.length}{" "}
                      {reviews.length === 1 ? "review" : "reviews"})
                    </span>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {reviews && reviews.length > 0 ? (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div
                        key={review.id}
                        className="border-b pb-4 last:border-0"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <StarIcon
                                  key={star}
                                  className={`h-4 w-4 ${
                                    star <= review.rating
                                      ? "fill-yellow-400 text-yellow-400"
                                      : "text-gray-300"
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="ml-2 font-medium text-sm">
                              Project: {review.job.name}
                            </span>
                          </div>
                          <span className="text-muted-foreground text-sm">
                            {format(new Date(review.createdAt), "MMM d, yyyy")}
                          </span>
                        </div>
                        <p className="mt-2">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    No reviews yet for this contractor.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Crew Section - Renamed from Collaborators */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Crew Members</CardTitle>
            </CardHeader>
            <CardContent>
              <CrewList organizationId={id} />
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
