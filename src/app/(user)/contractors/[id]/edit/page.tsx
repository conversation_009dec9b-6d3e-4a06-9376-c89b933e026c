"use client";

import { useQuery } from "@tanstack/react-query";
import { notFound, useParams } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { ContractorForm } from "@/components/features/contractors/contractor-form";
import { useTRPC } from "@/components/integrations/trpc/client";

export default function EditContractorPage() {
  const trpc = useTRPC();
  const { id } = useParams<{ id: string }>();
  const { data: organization, isLoading } = useQuery(
    trpc.contractor.getById.queryOptions({ id }),
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!organization || organization.id !== id) {
    return notFound();
  }

  return (
    <PageLayout title="Edit Contractor">
      <div className="p-8">
        <ContractorForm initialData={organization} />
      </div>
    </PageLayout>
  );
}
