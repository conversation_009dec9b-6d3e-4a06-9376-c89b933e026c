import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { EnhancedDashboard } from "@/components/features/dashboard/enhanced-dashboard";
import { auth } from "@/lib/auth";

export default async function DashboardPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  if (!session?.user.onboardingComplete) {
    redirect("/onboarding");
  }

  // Determine if user is a homeowner or professional
  const isProfessional = session?.user?.role === "contractor";

  return (
    <PageLayout title={isProfessional ? "Contractor Dashboard" : "Dashboard"}>
      <EnhancedDashboard />
    </PageLayout>
  );
}
