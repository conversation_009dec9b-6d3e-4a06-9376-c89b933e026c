"use client";

import {
  CalendarClockIcon,
  ClipboardListIcon,
  LayoutDashboardIcon,
  MapIcon,
} from "lucide-react";
import { AppSidebar } from "@/components/core/app-sidebar";
import { EnhancedPopupChat } from "@/components/features/chat/enhanced-popup-chat";
import { OrganizationProvider } from "@/components/providers/organization-context";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";

export default function UserLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <SidebarProvider>
      <OrganizationProvider>
        <UserLayoutContent>{children}</UserLayoutContent>
      </OrganizationProvider>
    </SidebarProvider>
  );
}

function UserLayoutContent({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { data: session } = useSession();

  const isHomeowner = session?.user.role === "homeowner";

  const menu = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboardIcon,
      visible: true,
    },
    {
      title: "Properties",
      href: "/properties",
      icon: MapIcon,
      visible: isHomeowner,
    },
    {
      title: "My Projects",
      href: "/projects",
      icon: ClipboardListIcon,
      visible: true,
    },
    {
      title: "Calendar",
      href: "/calendar",
      icon: CalendarClockIcon,
      visible: !isHomeowner,
    },
  ];

  return (
    <>
      <AppSidebar menu={menu} />
      <SidebarInset>
        {children}
        <EnhancedPopupChat
          userName={session?.user?.name}
          userAvatar={session?.user?.image}
        />
      </SidebarInset>
    </>
  );
}
