import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { TwoFactorContent } from "@/components/features/profile/two-factor-content";
import { auth } from "@/lib/auth";

export default async function TwoFactorPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  return (
    <PageLayout title="Two-Factor Authentication">
      <TwoFactorContent user={session.user} />
    </PageLayout>
  );
}
