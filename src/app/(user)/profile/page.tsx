import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PageLayout } from "@/components/core/page-layout";
import { ProfileForm } from "@/components/features/profile/profile-form";
import { auth } from "@/lib/auth";

export default async function ProfilePage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  return (
    <PageLayout title="Profile Settings">
      <div className="p-8">
        <ProfileForm user={session.user} />
      </div>
    </PageLayout>
  );
}
