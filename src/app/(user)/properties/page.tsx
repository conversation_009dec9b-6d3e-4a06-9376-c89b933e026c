import { Suspense } from "react";
import { PageLayout } from "@/components/core/page-layout";
import { PropertiesContent } from "@/components/features/properties/properties-content";
import {
  getQueryClient,
  HydrateClient,
  trpc,
} from "@/components/integrations/trpc/server";
import { PropertyGridSkeleton } from "@/components/shared/loading-states";

export default async function PropertiesPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Properties">
      <HydrateClient>
        <Suspense fallback={<PropertyGridSkeleton />}>
          <PropertiesContent />
        </Suspense>
      </HydrateClient>
    </PageLayout>
  );
}
