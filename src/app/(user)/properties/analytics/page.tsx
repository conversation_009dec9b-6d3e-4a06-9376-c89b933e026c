import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { PageLayout } from "@/components/core/page-layout";
import { PropertyAnalytics } from "@/components/features/properties/property-analytics";
import {
  getQueryClient,
  HydrateClient,
  trpc,
} from "@/components/integrations/trpc/server";
import { PropertyGridSkeleton } from "@/components/shared/loading-states";
import { Button } from "@/components/ui/button";

export default async function PropertyAnalyticsPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Property Analytics">
      <HydrateClient>
        <div className="space-y-6">
          {/* Back Navigation */}
          <div className="flex items-center gap-4">
            <Button asChild variant="ghost" size="sm">
              <Link href="/properties">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Properties
              </Link>
            </Button>
          </div>

          <Suspense fallback={<PropertyGridSkeleton />}>
            <PropertyAnalytics />
          </Suspense>
        </div>
      </HydrateClient>
    </PageLayout>
  );
}
