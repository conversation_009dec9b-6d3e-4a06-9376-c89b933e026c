"use server";

import { cookies } from "next/headers";

export async function setSocialSignUpType(
	accountType: "homeowner" | "contractor",
) {
	const yum = await cookies();

	// Set a cookie to remember the account type during the social auth flow
	yum.set("signup_account_type", accountType, {
		httpOnly: true,
		secure: process.env.NODE_ENV === "production",
		maxAge: 60 * 30, // 30 minutes
		path: "/",
	});
}
