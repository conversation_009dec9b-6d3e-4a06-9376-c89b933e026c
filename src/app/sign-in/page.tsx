import Link from "next/link";
import { SignInForm } from "@/components/features/auth/sign-in-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function SignInPage() {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Sign In</CardTitle>
          <CardDescription>
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SignInForm />
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-muted-foreground text-sm">
            Don't have an account?
          </div>
          <div className="flex w-full gap-4">
            <Button asChild variant="outline" className="w-1/2">
              <Link href="/sign-up/homeowner">Homeowner</Link>
            </Button>
            <Button asChild variant="outline" className="w-1/2">
              <Link href="/sign-up/contractor">Contractor</Link>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
