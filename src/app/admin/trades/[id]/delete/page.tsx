import { useMutation } from "@tanstack/react-query";
import { redirect } from "next/navigation";
import { trpc } from "@/components/integrations/trpc/server";

export default async function DeleteTradePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const deleteMutation = useMutation(trpc.trades.delete.mutationOptions());

  const handleDelete = async () => {
    deleteMutation.mutate(
      { id },
      {
        onSuccess: () => {
          redirect("/admin/trades");
        },
        onError: (error) => {
          console.error("Error deleting trade:", error);
        },
      },
    );
  };

  return (
    <div className="p-8">
      <h1 className="mb-6 font-bold text-2xl">Delete Trade</h1>
      <div className="max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
        <p className="mb-4">Are you sure you want to delete this trade?</p>
        <div className="flex gap-4">
          <button
            type="button"
            onClick={handleDelete}
            className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
          >
            Delete
          </button>
          <button
            type="button"
            onClick={() => redirect("/admin/trades")}
            className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
