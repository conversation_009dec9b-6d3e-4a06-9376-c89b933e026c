"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeftIcon, SaveIcon, UserIcon } from "lucide-react";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { caller } from "@/lib/trpc";

// Form schema
const EditUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["homeowner", "contractor", "admin"], {
    required_error: "Please select a role",
  }),
  emailVerified: z.boolean(),
  onboardingComplete: z.boolean(),
});

type EditUserForm = z.infer<typeof EditUserSchema>;

interface UserData {
  id: string;
  name: string | null;
  email: string;
  role: string | null;
  emailVerified: boolean | null;
  onboardingComplete: boolean | null;
  createdAt: Date;
  updatedAt: Date;
  banned: boolean | null;
  banReason: string | null;
  banExpires: Date | null;
  twoFactorEnabled: boolean | null;
  image: string | null;
  activeSessions?: Array<{
    id: string;
    createdAt: Date;
    ipAddress: string | null;
    userAgent: string | null;
    impersonatedBy: string | null;
  }>;
  accounts?: Array<{
    id: string;
    providerId: string;
    createdAt: Date;
  }>;
}

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUser, setIsLoadingUser] = useState(true);
  const [user, setUser] = useState<UserData | null>(null);

  const form = useForm<EditUserForm>({
    resolver: zodResolver(EditUserSchema),
    defaultValues: {
      name: "",
      email: "",
      role: "homeowner",
      emailVerified: false,
      onboardingComplete: false,
    },
  });

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoadingUser(true);
        const userData = await caller.adminUsers.getById({ userId });

        if (userData) {
          setUser(userData);
          form.reset({
            name: userData.name || "",
            email: userData.email || "",
            role:
              (userData.role as "homeowner" | "contractor" | "admin") ||
              "homeowner",
            emailVerified: userData.emailVerified || false,
            onboardingComplete: userData.onboardingComplete || false,
          });
        } else {
          toast.error("User not found");
          router.push("/admin/users");
        }
      } catch (error) {
        console.error("Error loading user:", error);
        toast.error("Failed to load user data");
        router.push("/admin/users");
      } finally {
        setIsLoadingUser(false);
      }
    };

    if (userId) {
      loadUser();
    }
  }, [userId, form, router]);

  const onSubmit = async (_data: EditUserForm) => {
    try {
      setIsLoading(true);

      // Here you would call your tRPC mutation to update the user
      // For now, we'll simulate the process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success("User updated successfully");
      router.push(`/admin/users/${userId}`);
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update user");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingUser) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 w-48 rounded bg-muted" />
          <div className="h-4 w-96 rounded bg-muted" />
          <div className="h-96 w-full rounded bg-muted" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="text-center">
          <h1 className="font-bold text-2xl">User not found</h1>
          <p className="text-muted-foreground">
            The user you're looking for doesn't exist.
          </p>
          <Button asChild className="mt-4">
            <Link href="/admin/users">Back to Users</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeftIcon className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="font-bold text-3xl tracking-tight">Edit User</h1>
          </div>
          <p className="text-muted-foreground">
            Update user information and permissions for {user.name}
          </p>
        </div>
      </div>

      {/* Edit Form */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                User Information
              </CardTitle>
              <CardDescription>
                Update the user's basic information and account settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter user's full name"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>User Role</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a role for the user" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="homeowner">
                              <div className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-orange-500" />
                                Homeowner
                              </div>
                            </SelectItem>
                            <SelectItem value="contractor">
                              <div className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-blue-500" />
                                Contractor
                              </div>
                            </SelectItem>
                            <SelectItem value="admin">
                              <div className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-red-500" />
                                Administrator
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          This determines the user's permissions and access
                          level
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="emailVerified"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Email Verified
                            </FormLabel>
                            <FormDescription>
                              Whether the user's email address has been verified
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="onboardingComplete"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Onboarding Complete
                            </FormLabel>
                            <FormDescription>
                              Whether the user has completed the onboarding
                              process
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex gap-3">
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          Saving Changes...
                        </>
                      ) : (
                        <>
                          <SaveIcon className="mr-2 h-4 w-4" />
                          Save Changes
                        </>
                      )}
                    </Button>
                    <Button type="button" variant="outline" asChild>
                      <Link href={`/admin/users/${userId}`}>Cancel</Link>
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar with current user info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Current User Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">User ID</span>
                <span className="font-mono text-sm">{user.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">Created</span>
                <span className="text-sm">
                  {new Date(user.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Last Updated
                </span>
                <span className="text-sm">
                  {new Date(user.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
