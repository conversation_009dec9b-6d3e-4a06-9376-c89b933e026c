import { format } from "date-fns";
import {
  AlertCircleIcon,
  BanIcon,
  CalendarIcon,
  CheckCircleIcon,
  EditIcon,
  MailIcon,
  ShieldCheckIcon,
  ShieldIcon,
  UserIcon,
  XCircleIcon,
} from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { caller } from "@/lib/trpc";

// Role color mapping
const getRoleColor = (role: string) => {
  switch (role) {
    case "admin":
      return "destructive";
    case "contractor":
      return "tc_blue";
    case "homeowner":
      return "tc_orange";
    default:
      return "secondary";
  }
};

export default async function UserDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  // Fetch user details using tRPC admin users router
  const user = await caller.adminUsers.getById({ userId: id });

  if (!user) {
    notFound();
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h1 className="font-bold text-3xl tracking-tight">{user.name}</h1>
            <Badge
              variant={
                getRoleColor(user.role || "homeowner") as
                  | "default"
                  | "secondary"
                  | "destructive"
                  | "outline"
                  | "tc_orange"
                  | "tc_blue"
              }
            >
              {user.role
                ? user.role.charAt(0).toUpperCase() + user.role.slice(1)
                : "Homeowner"}
            </Badge>
          </div>
          <p className="text-muted-foreground">
            User ID: {user.id} • Joined{" "}
            {format(new Date(user.createdAt), "PPP")}
          </p>
        </div>

        {/* Admin Actions */}
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/users/${user.id}/edit`}>
              <EditIcon className="mr-2 h-4 w-4" />
              Edit User
            </Link>
          </Button>
          <Button variant="outline" size="sm">
            <UserIcon className="mr-2 h-4 w-4" />
            Impersonate
          </Button>
          <Button variant={user.banned ? "default" : "destructive"} size="sm">
            <BanIcon className="mr-2 h-4 w-4" />
            {user.banned ? "Unban User" : "Ban User"}
          </Button>
        </div>
      </div>

      {/* Ban Alert */}
      {user.banned && (
        <Alert variant="destructive">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertDescription>
            This user is currently banned.
            {user.banReason && ` Reason: ${user.banReason}`}
            {user.banExpires &&
              ` Expires: ${format(new Date(user.banExpires), "PPP")}`}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <div className="space-y-6 lg:col-span-2">
          {/* User Profile Card */}
          <Card>
            <CardHeader>
              <CardTitle>User Profile</CardTitle>
              <CardDescription>
                Basic user information and account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={user.image as string} />
                  <AvatarFallback className="text-lg">
                    {user.name?.charAt(0)?.toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">{user.name}</h3>
                  <p className="text-muted-foreground">{user.email}</p>
                  <div className="flex items-center gap-2">
                    {user.emailVerified ? (
                      <div className="flex items-center gap-1 text-green-600">
                        <CheckCircleIcon className="h-4 w-4" />
                        <span className="text-sm">Email Verified</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-red-600">
                        <XCircleIcon className="h-4 w-4" />
                        <span className="text-sm">Email Not Verified</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Account Role
                  </div>
                  <p className="text-sm">
                    {user.role
                      ? user.role.charAt(0).toUpperCase() + user.role.slice(1)
                      : "Homeowner"}
                  </p>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Onboarding Status
                  </div>
                  <p className="text-sm">
                    {user.onboardingComplete ? "Completed" : "Pending"}
                  </p>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Two-Factor Authentication
                  </div>
                  <div className="flex items-center gap-2">
                    {user.twoFactorEnabled ? (
                      <>
                        <ShieldCheckIcon className="h-4 w-4 text-green-600" />
                        <span className="text-sm">Enabled</span>
                      </>
                    ) : (
                      <>
                        <ShieldIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">Disabled</span>
                      </>
                    )}
                  </div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Last Updated
                  </div>
                  <p className="text-sm">
                    {format(new Date(user.updatedAt), "PPP 'at' p")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Activity Card */}
          <Card>
            <CardHeader>
              <CardTitle>Account Activity</CardTitle>
              <CardDescription>
                Recent account activity and session information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex items-center space-x-3">
                    <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium text-sm">Account Created</p>
                      <p className="text-muted-foreground text-sm">
                        {format(new Date(user.createdAt), "PPP 'at' p")}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex items-center space-x-3">
                    <MailIcon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium text-sm">Email Verification</p>
                      <p className="text-muted-foreground text-sm">
                        {user.emailVerified
                          ? "Verified"
                          : "Pending verification"}
                      </p>
                    </div>
                  </div>
                  <Badge variant={user.emailVerified ? "success" : "secondary"}>
                    {user.emailVerified ? "Verified" : "Pending"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Active Sessions Card */}
          {user.activeSessions && user.activeSessions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>
                  Active Sessions ({user.activeSessions.length})
                </CardTitle>
                <CardDescription>
                  Current active login sessions for this user
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {user.activeSessions.map((session) => (
                    <div key={session.id} className="rounded-lg border p-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <p className="font-medium text-sm">
                            {session.userAgent
                              ? session.userAgent.split(" ")[0] ||
                                "Unknown Browser"
                              : "Unknown Browser"}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            IP: {session.ipAddress || "Unknown"}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            Started:{" "}
                            {format(
                              new Date(session.createdAt),
                              "MMM d, yyyy 'at' h:mm a",
                            )}
                          </p>
                          {session.impersonatedBy && (
                            <p className="text-orange-600 text-xs">
                              Impersonated by: {session.impersonatedBy}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Social Accounts Card */}
          {user.accounts && user.accounts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Connected Accounts</CardTitle>
                <CardDescription>
                  Social login providers connected to this account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {user.accounts.map((account) => (
                    <div
                      key={account.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                          <span className="font-medium text-sm">
                            {account.providerId.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-sm capitalize">
                            {account.providerId}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            Connected{" "}
                            {format(new Date(account.createdAt), "MMM d, yyyy")}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href={`/admin/users/${user.id}/edit`}>
                  <EditIcon className="mr-2 h-4 w-4" />
                  Edit Profile
                </Link>
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <UserIcon className="mr-2 h-4 w-4" />
                Impersonate User
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <MailIcon className="mr-2 h-4 w-4" />
                Send Email
              </Button>
              <Button
                variant={user.banned ? "default" : "destructive"}
                size="sm"
                className="w-full"
              >
                <BanIcon className="mr-2 h-4 w-4" />
                {user.banned ? "Unban User" : "Ban User"}
              </Button>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle>Account Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">Status</span>
                <Badge variant={user.banned ? "destructive" : "success"}>
                  {user.banned ? "Banned" : "Active"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">Email</span>
                <Badge variant={user.emailVerified ? "success" : "secondary"}>
                  {user.emailVerified ? "Verified" : "Unverified"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">2FA</span>
                <Badge
                  variant={user.twoFactorEnabled ? "success" : "secondary"}
                >
                  {user.twoFactorEnabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Onboarding
                </span>
                <Badge
                  variant={user.onboardingComplete ? "success" : "secondary"}
                >
                  {user.onboardingComplete ? "Complete" : "Pending"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
