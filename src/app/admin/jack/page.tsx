"use client";

import { useChat } from "@ai-sdk/react";
import { createIdGenerator } from "ai";
import { Send } from "lucide-react";
import { useEffect, useRef } from "react";
import jackAvatar from "@/assets/images/jack.png";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useLocalStorage } from "@/hooks/use-local-storage";

export default function JackPage() {
  const [chatId, setChatId] = useLocalStorage<string>("jack-chat-id", "");

  useEffect(() => {
    const initChat = async () => {
      if (!chatId) {
        try {
          const response = await fetch("/api/chat/create", {
            method: "POST",
          });
          if (response.ok) {
            const data = await response.json();
            setChatId(data.chatId);
          }
        } catch (error) {
          console.error("Failed to create chat:", error);
        }
      }
    };

    initChat();
  }, [chatId, setChatId]);

  const { messages, input, handleInputChange, handleSubmit, isLoading } =
    useChat({
      api: "/api/chat",
      id: chatId,
      generateId: createIdGenerator({
        prefix: "msgc",
        size: 16,
      }),
      sendExtraMessageFields: true,
      experimental_prepareRequestBody: ({ messages }) => {
        const lastMessage = messages[messages.length - 1];
        return {
          message: lastMessage,
          chatId,
        };
      },
    });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  });

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Left column with Jack's image */}
      <div className="hidden w-1/4 bg-muted/30 p-6 md:flex md:flex-col md:items-center">
        <div className="mb-4 text-center">
          <Avatar className="mx-auto h-32 w-32">
            <AvatarImage src={jackAvatar.src} alt="Jack" />
            <AvatarFallback>JA</AvatarFallback>
          </Avatar>
          <h2 className="mt-4 font-bold text-2xl">Jack</h2>
          <p className="text-muted-foreground">AI Assistant</p>
        </div>
        <div className="mt-6 space-y-4 text-center">
          <p className="text-muted-foreground text-sm">
            Jack can help you with questions about the platform, construction
            projects, and provide general assistance.
          </p>
        </div>
      </div>

      {/* Right column with chat */}
      <div className="flex w-full flex-col md:w-3/4">
        <div className="flex-1 p-4">
          <ScrollArea className="h-[calc(100vh-12rem)]">
            <div className="space-y-4 p-4">
              {messages.length === 0 ? (
                <div className="flex h-full items-center justify-center">
                  <p className="text-center text-muted-foreground">
                    Start a conversation with Jack by typing a message below.
                  </p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`flex max-w-[80%] items-start gap-3 rounded-lg p-4 ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      {message.role !== "user" && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={jackAvatar.src} alt="Jack" />
                          <AvatarFallback>JA</AvatarFallback>
                        </Avatar>
                      )}
                      <div>
                        <div className="mb-1 font-medium text-xs">
                          {message.role === "user" ? "You" : "Jack"}
                        </div>
                        {message.parts?.length ? (
                          <div className="whitespace-pre-wrap">
                            {message.parts.map((part, i) => {
                              switch (part.type) {
                                case "text":
                                  return (
                                    <div key={`${message.id}-part-${i}`}>
                                      {part.text}
                                    </div>
                                  );
                                case "tool-invocation": {
                                  const { toolInvocation } = part;

                                  return (
                                    <details
                                      key={`tool-${toolInvocation.toolCallId}`}
                                    >
                                      <summary>
                                        <span>
                                          {part.toolInvocation.toolName}
                                        </span>
                                      </summary>
                                      {part.toolInvocation.state ===
                                        "result" && (
                                        <pre className="mt-1 max-w-lg overflow-x-scroll rounded bg-muted/50 p-2 text-xs">
                                          {JSON.stringify(
                                            part.toolInvocation.result,
                                            null,
                                            2,
                                          )}
                                        </pre>
                                      )}
                                    </details>
                                  );
                                }
                              }
                            })}
                          </div>
                        ) : (
                          <p className="whitespace-pre-wrap">
                            {message.content || ""}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </div>

        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Type your message..."
              className="flex-1"
              disabled={isLoading}
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              <Send className="h-4 w-4" />
              <span className="ml-2">Send</span>
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
