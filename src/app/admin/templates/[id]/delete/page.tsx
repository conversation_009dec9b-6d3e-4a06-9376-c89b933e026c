"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { Header } from "@/components/core/header";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";

export default function DeleteTemplatePage() {
  const router = useRouter();
  const trpc = useTRPC();
  const { id } = useParams<{ id: string }>();
  const { data: template, isLoading } = useQuery(
    trpc.templates.one.queryOptions({ id: id }),
  );
  const deleteTemplate = useMutation(trpc.templates.delete.mutationOptions());

  const handleDelete = async () => {
    deleteTemplate.mutate(
      { id: id },
      {
        onSuccess: () => {
          toast.success("Template deleted successfully");
          router.push("/admin/templates");
        },
        onError: (error) => {
          console.error("Error deleting template:", error);
          toast.error("Failed to delete template");
        },
      },
    );
  };

  if (isLoading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <>
      <Header title="Delete Template" />
      <div className="p-8">
        <h1 className="mb-6 font-bold text-2xl">Delete Project Template</h1>
        <div className="max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
          <p className="mb-4">
            Are you sure you want to delete the template "{template?.name}"?
          </p>
          <p className="mb-6 text-red-600">
            This action cannot be undone. All template data will be permanently
            removed.
          </p>
          <div className="flex gap-4">
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteTemplate.isPending}
            >
              {deleteTemplate.isPending ? "Deleting..." : "Delete Template"}
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/admin/templates")}
            >
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
