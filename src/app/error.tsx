"use client";

import { <PERSON><PERSON><PERSON><PERSON>gleIcon, HomeIcon, RefreshCwIcon, WrenchIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import posthog from "posthog-js";
import { useEffect } from "react";
import logo from "@/assets/images/tc-logomark.webp";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// biome-ignore lint/suspicious/noShadowRestrictedNames: This is the name Next.js expects
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    posthog.captureException(error);
  }, [error]);

  return (
    <main className="relative min-h-screen bg-background">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
          <div
            className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-red-400 to-orange-500 opacity-20 dark:opacity-10 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8">
        <div className="w-full max-w-md text-center">
          {/* Logo */}
          <div className="mb-8 flex justify-center">
            <div className="rounded-2xl bg-background/80 p-4 shadow-lg backdrop-blur-sm dark:bg-background/60">
              <Image 
                src={logo} 
                alt="TradeCrews Logo" 
                width={80} 
                height={80}
                className="h-16 w-16 sm:h-20 sm:w-20" 
              />
            </div>
          </div>

          {/* Error icon and title */}
          <div className="mb-6 flex items-center justify-center gap-3">
            <AlertTriangleIcon className="h-8 w-8 text-red-500 sm:h-10 sm:w-10" />
            <WrenchIcon className="h-8 w-8 text-orange-500 sm:h-10 sm:w-10" />
          </div>

          {/* Error message */}
          <div className="mb-8 space-y-3">
            <h1 className="font-bold text-2xl text-foreground sm:text-3xl">
              Oops! Something Went Wrong
            </h1>
            <p className="text-muted-foreground text-sm leading-relaxed sm:text-base">
              We're experiencing some technical difficulties. Our team has been notified and is working to fix this issue.
            </p>
            {process.env.NODE_ENV === "development" && error.message && (
              <details className="mt-4 rounded-lg bg-muted p-3 text-left">
                <summary className="cursor-pointer font-medium text-sm">
                  Technical Details
                </summary>
                <pre className="mt-2 overflow-auto text-xs text-muted-foreground">
                  {error.message}
                </pre>
              </details>
            )}
          </div>

          {/* Action buttons - Mobile first */}
          <div className="space-y-3 sm:space-y-4">
            {/* Primary action */}
            <Button
              onClick={() => reset()}
              variant="tc_orange"
              size="lg"
              className="w-full touch-target-comfortable gap-2 text-base font-semibold"
            >
              <RefreshCwIcon className="h-5 w-5" />
              Try Again
            </Button>

            {/* Secondary action */}
            <Link
              href="/"
              className={cn(
                buttonVariants({ 
                  variant: "outline", 
                  size: "lg" 
                }),
                "w-full touch-target-comfortable gap-2 text-sm"
              )}
            >
              <HomeIcon className="h-4 w-4" />
              Back to Home
            </Link>
          </div>

          {/* Help section */}
          <div className="mt-8 space-y-2 border-t border-border pt-6">
            <p className="text-muted-foreground text-xs sm:text-sm">
              Still having trouble? We're here to help.
            </p>
            <div className="flex flex-col gap-2 sm:flex-row sm:justify-center sm:gap-4">
              <Link
                href="/how-it-works"
                className="font-medium text-orange-600 text-sm hover:text-orange-500 dark:text-orange-400 dark:hover:text-orange-300"
              >
                How it works
              </Link>
              <Link
                href="/contractors"
                className="font-medium text-orange-600 text-sm hover:text-orange-500 dark:text-orange-400 dark:hover:text-orange-300"
              >
                Find contractors
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="font-medium text-muted-foreground text-sm hover:text-foreground"
              >
                Contact support
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom decoration */}
      <div className="-z-10 absolute inset-x-0 top-[calc(100%-13rem)] transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
        <div
          className="-translate-x-1/2 relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] bg-gradient-to-tr from-red-500 to-orange-400 opacity-20 dark:opacity-10 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>
    </main>
  );
}
