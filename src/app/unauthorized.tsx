import { KeyIcon, LogInIcon, ShieldIcon, UserPlusIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import logo from "@/assets/images/tc-logomark.webp";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function UnauthorizedPage() {
  return (
    <main className="relative min-h-screen bg-background">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="-top-40 -z-10 sm:-top-80 absolute inset-x-0 transform-gpu overflow-hidden blur-3xl">
          <div
            className="-translate-x-1/2 relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] rotate-[30deg] bg-gradient-to-tr from-blue-400 to-orange-500 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem] dark:opacity-10"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8">
        <div className="w-full max-w-md text-center">
          {/* Logo */}
          <div className="mb-8 flex justify-center">
            <div className="rounded-2xl bg-background/80 p-4 shadow-lg backdrop-blur-sm dark:bg-background/60">
              <Image
                src={logo}
                alt="TradeCrews Logo"
                width={80}
                height={80}
                className="h-16 w-16 sm:h-20 sm:w-20"
              />
            </div>
          </div>

          {/* Access denied icon */}
          <div className="mb-6 flex items-center justify-center gap-3">
            <ShieldIcon className="h-8 w-8 text-blue-500 sm:h-10 sm:w-10" />
            <KeyIcon className="h-8 w-8 text-orange-500 sm:h-10 sm:w-10" />
          </div>

          {/* Error message */}
          <div className="mb-8 space-y-3">
            <h1 className="font-bold text-2xl text-foreground sm:text-3xl">
              Access Restricted
            </h1>
            <p className="text-muted-foreground text-sm leading-relaxed sm:text-base">
              You need to sign in to access this area of TradeCrews. Join our
              community of homeowners and contractors today.
            </p>
          </div>

          {/* Action buttons - Mobile first */}
          <div className="space-y-3 sm:space-y-4">
            {/* Primary action */}
            <Link
              href="/sign-in"
              className={cn(
                buttonVariants({
                  variant: "tc_orange",
                  size: "lg",
                }),
                "touch-target-comfortable w-full gap-2 font-semibold text-base",
              )}
            >
              <LogInIcon className="h-5 w-5" />
              Sign In
            </Link>

            {/* Secondary actions */}
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-2">
              <Link
                href="/sign-up/homeowner"
                className={cn(
                  buttonVariants({
                    variant: "outline",
                    size: "lg",
                  }),
                  "touch-target-comfortable gap-2 text-sm",
                )}
              >
                <UserPlusIcon className="h-4 w-4" />
                Join as Homeowner
              </Link>

              <Link
                href="/sign-up/contractor"
                className={cn(
                  buttonVariants({
                    variant: "outline",
                    size: "lg",
                  }),
                  "touch-target-comfortable gap-2 text-sm",
                )}
              >
                <UserPlusIcon className="h-4 w-4" />
                Join as Contractor
              </Link>
            </div>
          </div>

          {/* Help section */}
          <div className="mt-8 space-y-2 border-border border-t pt-6">
            <p className="text-muted-foreground text-xs sm:text-sm">
              New to TradeCrews? Learn more about our platform.
            </p>
            <div className="flex flex-col gap-2 sm:flex-row sm:justify-center sm:gap-4">
              <Link
                href="/how-it-works"
                className="font-medium text-orange-600 text-sm hover:text-orange-500 dark:text-orange-400 dark:hover:text-orange-300"
              >
                How it works
              </Link>
              <Link
                href="/verification-process"
                className="font-medium text-orange-600 text-sm hover:text-orange-500 dark:text-orange-400 dark:hover:text-orange-300"
              >
                Verification process
              </Link>
              <Link
                href="/"
                className="font-medium text-muted-foreground text-sm hover:text-foreground"
              >
                Back to home
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom decoration */}
      <div className="-z-10 absolute inset-x-0 top-[calc(100%-13rem)] transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
        <div
          className="-translate-x-1/2 relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] bg-gradient-to-tr from-blue-500 to-orange-400 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem] dark:opacity-10"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>
    </main>
  );
}
