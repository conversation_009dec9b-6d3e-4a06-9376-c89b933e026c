import { CheckCircle2Icon } from "lucide-react";
import type { Metadata } from "next";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export const metadata: Metadata = {
  metadataBase: new URL("https://tradecrews.com"),
  title: "Contractor Opportunities | TradeCrews",
  description:
    "Join TradeCrews as a contractor to connect with homeowners, manage projects efficiently, and grow your trade business with our trusted platform.",
  openGraph: {
    title: "Grow Your Trade Business with TradeCrews",
    description:
      "Connect with homeowners looking for your expertise, manage projects efficiently, and build your reputation on our trusted platform.",
    images: ["/images/contractors-og.webp"],
  },
};

export default function ContractorsPage() {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Service",
    name: "TradeCrews Contractor Platform",
    description:
      "Connect with homeowners looking for your expertise, manage projects efficiently, and build your reputation on the platform trusted by contractors.",
    provider: {
      "@type": "Organization",
      name: "TradeCrews",
      url: "https://tradecrews.com",
    },
    audience: {
      "@type": "Audience",
      audienceType: "Trade Professionals",
    },
  };

  return (
    <main className="bg-white">
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: JSON-LD structured data
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="font-bold text-4xl text-gray-900 tracking-tight sm:text-6xl">
              Grow Your Business with TradeCrews
            </h1>
            <p className="mt-6 text-gray-600 text-lg leading-8">
              Connect with homeowners looking for your expertise, manage
              projects efficiently, and build your reputation on the platform
              trusted by contractors.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/sign-up/contractor">
                <Button variant="tc_orange" size="lg">
                  Join as a Contractor
                </Button>
              </Link>
              <Link
                href="#benefits"
                className="font-semibold text-gray-900 text-sm leading-6"
              >
                Learn more <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="font-semibold text-base text-tradecrews-blue leading-7">
              For Contractors
            </h2>
            <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Everything you need to succeed
            </p>
            <p className="mt-6 text-gray-600 text-lg leading-8">
              TradeCrews provides the tools and opportunities you need to take
              your trade business to the next level.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Find New Clients
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Connect with homeowners actively seeking your specific
                    skills and services in your local area.
                  </p>
                </dd>
              </div>
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Build Your Reputation
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Showcase your work, collect reviews, and build a stellar
                    reputation that helps you win more business.
                  </p>
                </dd>
              </div>
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Streamlined Management
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Manage quotes, schedules, and client communications all in
                    one place with our easy-to-use tools.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="bg-white py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Simple, transparent pricing
            </h2>
            <p className="mt-6 text-gray-600 text-lg leading-8">
              Invest in your business growth with our affordable subscription
              plan.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-lg">
            <div className="rounded-3xl bg-white p-8 shadow-sm ring-1 ring-gray-200 transition-shadow hover:shadow-md">
              <div className="flex items-center justify-between gap-x-4">
                <h3 className="font-semibold text-lg text-tradecrews-blue">
                  Professional Plan
                </h3>
                <p className="rounded-full bg-tradecrews-blue/10 px-2.5 py-1 font-semibold text-tradecrews-blue text-xs">
                  Most Popular
                </p>
              </div>

              <p className="mt-4 text-gray-600 text-sm">
                Everything you need to grow your trade business and connect with
                quality clients.
              </p>

              <div className="mt-6 flex items-baseline gap-x-1">
                <span className="font-bold text-4xl text-gray-900 tracking-tight">
                  $70
                </span>
                <span className="font-semibold text-gray-600 text-sm">
                  /month
                </span>
              </div>
              <p className="mt-1 text-gray-500 text-sm">
                Billed annually ($840/year)
              </p>

              <ul className="mt-8 space-y-3 text-gray-600 text-sm">
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Unlimited project bids</span>
                </li>
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Verified contractor profile</span>
                </li>
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Project management tools</span>
                </li>
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Secure messaging with clients</span>
                </li>
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Payment processing</span>
                </li>
                <li className="flex gap-x-3">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  <span>Customer review collection</span>
                </li>
              </ul>

              <Link href="/sign-up/contractor" className="mt-8 block">
                <Button variant="tc_orange" className="w-full">
                  Get started today
                </Button>
              </Link>
            </div>
          </div>

          <p className="mt-8 text-center text-gray-500 text-sm">
            All plans come with a 30-day money-back guarantee. No contracts or
            hidden fees.
          </p>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Ready to grow your business?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-gray-600 text-lg leading-8">
              Join thousands of contractors who are expanding their client base
              and increasing their revenue with TradeCrews.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/sign-up/contractor">
                <Button variant="tc_orange" size="lg">
                  Sign up now
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
