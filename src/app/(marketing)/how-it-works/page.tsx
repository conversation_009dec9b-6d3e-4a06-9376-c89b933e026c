import Footer from "@/components/marketing/footer";
import { FAQ } from "@/components/marketing/how-it-works/faq";
import { HowItWorksHero } from "@/components/marketing/how-it-works/hero";
import { HowItWorksSteps } from "@/components/marketing/how-it-works/steps";
import { Testimonials } from "@/components/marketing/how-it-works/testimonials";

export default function HowItWorksPage() {
  // JSON-LD structured data for better SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    name: "How TradeCrews Works",
    description:
      "Learn how to find and hire trusted trade professionals for your home projects using TradeCrews.",
    step: [
      {
        "@type": "HowToStep",
        name: "Create Your Project",
        text: "Describe your project needs and requirements in detail.",
      },
      {
        "@type": "HowToStep",
        name: "Get Matched",
        text: "Our system matches you with verified professionals who specialize in your project type.",
      },
      {
        "@type": "HowToStep",
        name: "Compare Quotes",
        text: "Review competitive bids from multiple contractors.",
      },
      {
        "@type": "HowToStep",
        name: "<PERSON><PERSON> and <PERSON><PERSON>",
        text: "Select your preferred professional and manage your project through our platform.",
      },
    ],
  };

  return (
    <main>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: JSON-LD structured data
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <HowItWorksHero />
      <HowItWorksSteps />
      <Testimonials />
      <FAQ />
      <Footer />
    </main>
  );
}
