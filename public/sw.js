// This is the service worker for TradeCrews PWA

// Push notification event
self.addEventListener('push', (event) => {
  // @ts-ignore
  if (event.data) {
    // @ts-ignore
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/web-app-manifest-192x192.png',
      badge: '/web-app-manifest-192x192.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: '1',
        url: data.url || '/',
      },
    };
    // @ts-ignore
    event.waitUntil(
      // @ts-ignore
      self.registration.showNotification(data.title || 'TradeCrews', options)
    );
  }
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  // @ts-ignore
  event.notification.close();

  // @ts-ignore
  const url = event.notification.data?.url || '/';

  // @ts-ignore
  event.waitUntil(
    // @ts-ignore
    clients.openWindow(url)
  );
});
