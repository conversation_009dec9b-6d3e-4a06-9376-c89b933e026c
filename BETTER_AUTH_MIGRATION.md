# Better-Auth Permission System Migration

## Overview

This migration replaces the entire custom tRPC permission system with better-auth's built-in access control integration. The new system provides:

- **Simplified permission checks** using better-auth's access control
- **Better type safety** with better-auth's built-in types
- **Consistent permission model** across client and server
- **Reduced code complexity** for simple CRUD operations
- **Maintained flexibility** for complex business rules

## What Changed

### 1. New Better-Auth Integration Files

- `src/lib/trpc/middleware/better-auth-permissions.ts` - Better-auth permission middleware
- `src/lib/trpc/procedures/better-auth.ts` - Better-auth procedure definitions

### 2. Updated Main Procedures File

- `src/lib/trpc/procedures/index.ts` now exports better-auth procedures as primary exports
- Legacy procedures are available with `legacy` prefix for backward compatibility

### 3. Permission System Replacement

**Before (Custom System):**

```typescript
import { requireAdmin, jobMiddlewares } from "../middleware/permissions";

const adminProcedure = protectedProcedure.use(requireAdmin);
const jobCreate = protectedProcedure.use(jobMiddlewares.canCreate);
```

**After (Better-Auth System):**

```typescript
import { adminProcedure, jobProcedures } from "../procedures";

// Role-based permissions
const adminOnlyProcedure = adminProcedure;

// Resource-specific permissions
const jobCreate = jobProcedures.create;
```

## Migration Benefits

### 1. Simplified Permission Definitions

**Before:**

```typescript
// Complex middleware chains
export const jobMiddlewares = {
  canCreate: createPermissionMiddleware("job", "create"),
  canUpdate: createPermissionMiddleware("job", "update", { requireOwnership: true }),
  canDelete: createPermissionMiddleware("job", "delete", { requireOwnership: true }),
};
```

**After:**

```typescript
// Simple permission objects
export const jobProcedures = {
  create: protectedProcedure.use(jobPermissions.create()),
  update: protectedProcedure.use(jobPermissions.update()),
  delete: protectedProcedure.use(jobPermissions.delete()),
};
```

### 2. Better Type Safety

**Before:**

```typescript
// Generic context with any types
customCheck?: (ctx: any) => Promise<boolean> | boolean;
```

**After:**

```typescript
// Strongly typed context
customCheck?: (ctx: BetterAuthContext) => Promise<boolean> | boolean;
```

### 3. Consistent Permission Model

All permissions are now defined in `src/lib/permissions.ts` using better-auth's access control:

```typescript
export const admin = ac.newRole({
  job: ["create", "read", "update", "delete", "publish", "award", "complete"],
  property: ["create", "read", "update", "delete"],
  // ... other permissions
});
```

## Current Status

### ✅ Completed

1. **Better-auth middleware integration** - `betterAuthMiddleware`
2. **Resource-specific permission factories** - `jobPermissions`, `propertyPermissions`, etc.
3. **Role-based procedures** - `adminProcedure`, `contractorProcedure`, `homeownerProcedure`
4. **Utility procedure builders** - `createPermissionProcedure`, `createCRUDProcedures`
5. **Backward compatibility** - All legacy procedures available with `legacy` prefix
6. **Main procedures file updated** - Primary exports now use better-auth system

### 🔄 In Progress

1. **Router migration** - All existing routers continue to work with new system
2. **Testing** - Need to verify all permissions work correctly

### 📋 Next Steps

1. **Test the new system** with existing routers
2. **Verify permission checks** work as expected
3. **Update documentation** for new permission patterns
4. **Remove legacy system** once migration is complete

## Usage Examples

### Basic Resource Procedures

```typescript
import { jobProcedures, propertyProcedures } from "@/lib/trpc/procedures";

export const myRouter = router({
  // Automatically checks job creation permissions
  createJob: jobProcedures.create
    .input(jobCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Permission already verified
    }),

  // Automatically checks ownership before updates
  updateProperty: propertyProcedures.update
    .input(propertyUpdateSchema)
    .mutation(async ({ input, ctx }) => {
      // Ownership already verified
    }),
});
```

### Custom Permission Procedures

```typescript
import { createPermissionProcedure } from "@/lib/trpc/procedures";

const customProcedure = createPermissionProcedure("job", "publish", {
  requireOwnership: true,
  customCheck: async (ctx) => {
    // Custom business logic
    return ctx.userRole === "homeowner";
  },
});
```

### Role-Based Procedures

```typescript
import { adminProcedure, contractorProcedure } from "@/lib/trpc/procedures";

export const adminRouter = router({
  // Admin-only procedures
  deleteUser: adminProcedure
    .input(userDeleteSchema)
    .mutation(async ({ input }) => {
      // Only admins can access this
    }),

  // Contractor-only procedures
  updateOrganization: contractorProcedure
    .input(orgUpdateSchema)
    .mutation(async ({ input }) => {
      // Only contractors can access this
    }),
});
```

## Backward Compatibility

All existing routers continue to work without changes. Legacy procedures are available:

```typescript
// Legacy imports still work
import {
  legacyAdminProcedure,
  legacyJobProcedures,
  legacyProtectedProcedure
} from "@/lib/trpc/procedures";
```

## Testing

To test the new system:

1. **Run existing tests** - All should pass with new system
2. **Test permission checks** - Verify role-based access works
3. **Test ownership checks** - Verify resource ownership is enforced
4. **Test custom permissions** - Verify complex business rules work

## Rollback Plan

If issues arise, the legacy system is still available:

1. Update imports to use `legacy` prefixed procedures
2. All existing middleware and permission checks remain functional
3. No data migration required - only code changes
