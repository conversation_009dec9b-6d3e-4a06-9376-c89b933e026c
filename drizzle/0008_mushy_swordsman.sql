ALTER TYPE "public"."AccountRole" RENAME TO "account_role";--> statement-breakpoint
ALTER TYPE "public"."role" RENAME TO "ai_chat_role";--> statement-breakpoint
ALTER TYPE "public"."BidStatus" RENAME TO "bid_status";--> statement-breakpoint
ALTER TYPE "public"."JobStatus" RENAME TO "job_status";--> statement-breakpoint
ALTER TYPE "public"."JobType" RENAME TO "job_type";--> statement-breakpoint
ALTER TYPE "public"."ScheduleStatus" RENAME TO "schedule_status";--> statement-breakpoint
ALTER TABLE "bid" DROP CONSTRAINT "Bid_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "bid" DROP CONSTRAINT "Bid_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "_bid_to_task" DROP CONSTRAINT "_BidToTask_A_fkey";
--> statement-breakpoint
ALTER TABLE "_bid_to_task" DROP CONSTRAINT "_BidToTask_B_fkey";
--> statement-breakpoint
ALTER TABLE "chat" DROP CONSTRAINT "Chat_bidId_fkey";
--> statement-breakpoint
ALTER TABLE "chat" DROP CONSTRAINT "Chat_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "collaboration" DROP CONSTRAINT "Collaboration_primaryOrgId_fkey";
--> statement-breakpoint
ALTER TABLE "collaboration" DROP CONSTRAINT "Collaboration_crewMemberId_fkey";
--> statement-breakpoint
ALTER TABLE "job" DROP CONSTRAINT "Job_propertyId_fkey";
--> statement-breakpoint
ALTER TABLE "job_image" DROP CONSTRAINT "JobImage_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "membership" DROP CONSTRAINT "Membership_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "message" DROP CONSTRAINT "Message_chatId_fkey";
--> statement-breakpoint
ALTER TABLE "organization" DROP CONSTRAINT "Organization_tradeId_fkey";
--> statement-breakpoint
ALTER TABLE "organization" DROP CONSTRAINT "Organization_addressId_fkey";
--> statement-breakpoint
ALTER TABLE "property" DROP CONSTRAINT "Property_addressId_fkey";
--> statement-breakpoint
ALTER TABLE "property" DROP CONSTRAINT "Property_accountId_fkey";
--> statement-breakpoint
ALTER TABLE "push_subscription" DROP CONSTRAINT "PushSubscription_accountId_fkey";
--> statement-breakpoint
ALTER TABLE "review" DROP CONSTRAINT "Review_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "schedule" DROP CONSTRAINT "Schedule_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "service" DROP CONSTRAINT "Service_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "Task_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "Task_tradeId_fkey";
--> statement-breakpoint
ALTER TABLE "template_task" DROP CONSTRAINT "TemplateTask_templateId_fkey";
--> statement-breakpoint
ALTER TABLE "template_task" DROP CONSTRAINT "TemplateTask_tradeId_fkey";
--> statement-breakpoint
DROP INDEX "Chat_bidId_key";--> statement-breakpoint
DROP INDEX "Chat_jobId_key";--> statement-breakpoint
DROP INDEX "Collaboration_crewMemberId_idx";--> statement-breakpoint
DROP INDEX "Collaboration_primaryOrgId_crewMemberId_key";--> statement-breakpoint
DROP INDEX "Collaboration_primaryOrgId_idx";--> statement-breakpoint
DROP INDEX "Membership_userId_organizationId_key";--> statement-breakpoint
DROP INDEX "Message_chatId_idx";--> statement-breakpoint
DROP INDEX "Organization_addressId_key";--> statement-breakpoint
DROP INDEX "Property_addressId_key";--> statement-breakpoint
DROP INDEX "PushSubscription_accountId_idx";--> statement-breakpoint
DROP INDEX "PushSubscription_endpoint_key";--> statement-breakpoint
DROP INDEX "Review_jobId_idx";--> statement-breakpoint
DROP INDEX "Review_reviewerId_idx";--> statement-breakpoint
DROP INDEX "Schedule_jobId_idx";--> statement-breakpoint
DROP INDEX "TemplateTask_templateId_idx";--> statement-breakpoint
DROP INDEX "Trade_name_key";--> statement-breakpoint
ALTER TABLE "_bid_to_task" DROP CONSTRAINT "_BidToTask_AB_pkey";--> statement-breakpoint
ALTER TABLE "membership" DROP CONSTRAINT "Membership_pkey";--> statement-breakpoint
ALTER TABLE "verification" ALTER COLUMN "created_at" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "verification" ALTER COLUMN "updated_at" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "_bid_to_task" ADD CONSTRAINT "_bid_to_task_AB_pkey" PRIMARY KEY("A","B");--> statement-breakpoint
ALTER TABLE "membership" ADD CONSTRAINT "membership_pkey" PRIMARY KEY("userId","organizationId");--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "onboarding_complete" boolean;--> statement-breakpoint
ALTER TABLE "bid" ADD CONSTRAINT "bid_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bid" ADD CONSTRAINT "bid_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_bid_to_task" ADD CONSTRAINT "_bid_to_task_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_bid_to_task" ADD CONSTRAINT "_bid_to_task_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."task"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "chat_bidId_fkey" FOREIGN KEY ("bidId") REFERENCES "public"."bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "chat_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "collaboration" ADD CONSTRAINT "collaboration_primaryOrgId_fkey" FOREIGN KEY ("primaryOrgId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "collaboration" ADD CONSTRAINT "collaboration_crewMemberId_fkey" FOREIGN KEY ("crewMemberId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "job" ADD CONSTRAINT "job_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "public"."property"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "job_image" ADD CONSTRAINT "job_image_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "membership" ADD CONSTRAINT "membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "message_chatId_fkey" FOREIGN KEY ("chatId") REFERENCES "public"."chat"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "organization_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "organization_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "property" ADD CONSTRAINT "property_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "property" ADD CONSTRAINT "property_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "push_subscription" ADD CONSTRAINT "push_subscription_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "review" ADD CONSTRAINT "review_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "schedule" ADD CONSTRAINT "schedule_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service" ADD CONSTRAINT "service_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "task_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "task_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "template_task" ADD CONSTRAINT "template_task_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."job_template"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "template_task" ADD CONSTRAINT "template_task_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "chat_bidId_key" ON "chat" USING btree ("bidId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "chat_jobId_key" ON "chat" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "collaboration_crewMemberId_idx" ON "collaboration" USING btree ("crewMemberId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "collaboration_primaryOrgId_crewMemberId_key" ON "collaboration" USING btree ("primaryOrgId" text_ops,"crewMemberId" text_ops);--> statement-breakpoint
CREATE INDEX "collaboration_primaryOrgId_idx" ON "collaboration" USING btree ("primaryOrgId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "membership_userId_organizationId_key" ON "membership" USING btree ("userId" text_ops,"organizationId" text_ops);--> statement-breakpoint
CREATE INDEX "message_chatId_idx" ON "message" USING btree ("chatId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "organization_addressId_key" ON "organization" USING btree ("addressId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "property_addressId_key" ON "property" USING btree ("addressId" text_ops);--> statement-breakpoint
CREATE INDEX "push_subscription_accountId_idx" ON "push_subscription" USING btree ("accountId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "push_subscription_endpoint_key" ON "push_subscription" USING btree ("endpoint" text_ops);--> statement-breakpoint
CREATE INDEX "review_jobId_idx" ON "review" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "review_reviewerId_idx" ON "review" USING btree ("reviewerId" text_ops);--> statement-breakpoint
CREATE INDEX "schedule_jobId_idx" ON "schedule" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "template_task_templateId_idx" ON "template_task" USING btree ("templateId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "trade_name_key" ON "trade" USING btree ("name" text_ops);