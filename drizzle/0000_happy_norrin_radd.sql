-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."AccountRole" AS ENUM('HOMEOWNER', 'PROFESSIONAL', 'ADMIN');--> statement-breakpoint
CREATE TYPE "public"."BidStatus" AS ENUM('PROPOSED', 'ACCEPTED', 'REJECTED', 'CANCELED', 'WITHDRAWN');--> statement-breakpoint
CREATE TYPE "public"."JobStatus" AS ENUM('DRAFT', 'PUBLISHED', 'CLOSED', 'CANCELED', 'AWARDED', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."JobType" AS ENUM('STANDARD', 'QUICK_HIRE');--> statement-breakpoint
CREATE TYPE "public"."ScheduleStatus" AS ENUM('PROPOSED', 'CONFIRMED', 'RESCHEDULED', 'CANCELED');--> statement-breakpoint
CREATE TABLE "_prisma_migrations" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"checksum" varchar(64) NOT NULL,
	"finished_at" timestamp with time zone,
	"migration_name" varchar(255) NOT NULL,
	"logs" text,
	"rolled_back_at" timestamp with time zone,
	"started_at" timestamp with time zone DEFAULT now() NOT NULL,
	"applied_steps_count" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Task" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"jobId" text NOT NULL,
	"tradeId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Trade" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"availableForQuickHire" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Organization" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"tradeId" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"description" text,
	"logoUrl" text,
	"email" text,
	"phone" text,
	"addressId" text NOT NULL,
	"acceptsQuickHire" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Chat" (
	"id" text PRIMARY KEY NOT NULL,
	"bidId" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"jobId" text
);
--> statement-breakpoint
CREATE TABLE "Message" (
	"id" text PRIMARY KEY NOT NULL,
	"content" text NOT NULL,
	"senderId" text NOT NULL,
	"senderType" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"chatId" text NOT NULL,
	"senderAvatarUrl" text,
	"senderInitials" text,
	"commandData" text,
	"isCommand" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "JobImage" (
	"id" text PRIMARY KEY NOT NULL,
	"url" text NOT NULL,
	"description" text,
	"jobId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Bid" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"jobId" text NOT NULL,
	"organizationId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"status" "BidStatus" DEFAULT 'PROPOSED' NOT NULL,
	"amount" integer DEFAULT 0 NOT NULL,
	"description" text DEFAULT '' NOT NULL,
	"estimatedDuration" integer DEFAULT 7 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Property" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"imageUrl" text NOT NULL,
	"addressId" text NOT NULL,
	"accountId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Job" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"propertyId" text NOT NULL,
	"budget" integer NOT NULL,
	"taskBids" boolean DEFAULT false NOT NULL,
	"startsAt" timestamp(3) NOT NULL,
	"deadline" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"status" "JobStatus" DEFAULT 'DRAFT' NOT NULL,
	"completedAt" timestamp(3),
	"contractorCompleted" boolean DEFAULT false NOT NULL,
	"homeownerCompleted" boolean DEFAULT false NOT NULL,
	"isRecurring" boolean DEFAULT false NOT NULL,
	"jobType" "JobType" DEFAULT 'STANDARD' NOT NULL,
	"recurringFrequency" text
);
--> statement-breakpoint
CREATE TABLE "Address" (
	"id" text PRIMARY KEY NOT NULL,
	"street" text NOT NULL,
	"city" text NOT NULL,
	"state" text NOT NULL,
	"zip" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"location" jsonb
);
--> statement-breakpoint
CREATE TABLE "Collaboration" (
	"id" text PRIMARY KEY NOT NULL,
	"primaryOrgId" text NOT NULL,
	"notes" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"crewMemberId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "JobTemplate" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"budget" integer NOT NULL,
	"estimatedDuration" integer DEFAULT 7 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "TemplateTask" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"templateId" text NOT NULL,
	"tradeId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Review" (
	"id" text PRIMARY KEY NOT NULL,
	"jobId" text NOT NULL,
	"rating" integer NOT NULL,
	"comment" text NOT NULL,
	"reviewType" text NOT NULL,
	"reviewerId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "PushSubscription" (
	"id" text PRIMARY KEY NOT NULL,
	"endpoint" text NOT NULL,
	"p256dh" text NOT NULL,
	"auth" text NOT NULL,
	"accountId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Service" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"price" double precision NOT NULL,
	"duration" integer,
	"organizationId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Account" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"role" "AccountRole" DEFAULT 'HOMEOWNER' NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"emailNotifications" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Schedule" (
	"id" text PRIMARY KEY NOT NULL,
	"jobId" text NOT NULL,
	"proposedStartDate" timestamp(3) NOT NULL,
	"proposedEndDate" timestamp(3) NOT NULL,
	"confirmedStartDate" timestamp(3),
	"confirmedEndDate" timestamp(3),
	"status" "ScheduleStatus" DEFAULT 'PROPOSED' NOT NULL,
	"proposedById" text NOT NULL,
	"proposedByRole" "AccountRole" NOT NULL,
	"notes" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"completed" boolean DEFAULT false NOT NULL,
	"completedAt" timestamp(3)
);
--> statement-breakpoint
CREATE TABLE "_BidToTask" (
	"A" text NOT NULL,
	"B" text NOT NULL,
	CONSTRAINT "_BidToTask_AB_pkey" PRIMARY KEY("A","B")
);
--> statement-breakpoint
CREATE TABLE "Membership" (
	"userId" text NOT NULL,
	"organizationId" text NOT NULL,
	"role" text NOT NULL,
	CONSTRAINT "Membership_pkey" PRIMARY KEY("userId","organizationId")
);
--> statement-breakpoint
ALTER TABLE "Task" ADD CONSTRAINT "Task_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Task" ADD CONSTRAINT "Task_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."Trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."Trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."Address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Chat" ADD CONSTRAINT "Chat_bidId_fkey" FOREIGN KEY ("bidId") REFERENCES "public"."Bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Chat" ADD CONSTRAINT "Chat_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Message" ADD CONSTRAINT "Message_chatId_fkey" FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "JobImage" ADD CONSTRAINT "JobImage_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Bid" ADD CONSTRAINT "Bid_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Bid" ADD CONSTRAINT "Bid_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Property" ADD CONSTRAINT "Property_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."Address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Property" ADD CONSTRAINT "Property_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."Account"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Job" ADD CONSTRAINT "Job_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "public"."Property"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Collaboration" ADD CONSTRAINT "Collaboration_primaryOrgId_fkey" FOREIGN KEY ("primaryOrgId") REFERENCES "public"."Organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Collaboration" ADD CONSTRAINT "Collaboration_crewMemberId_fkey" FOREIGN KEY ("crewMemberId") REFERENCES "public"."Organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "TemplateTask" ADD CONSTRAINT "TemplateTask_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."JobTemplate"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "TemplateTask" ADD CONSTRAINT "TemplateTask_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."Trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Review" ADD CONSTRAINT "Review_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "PushSubscription" ADD CONSTRAINT "PushSubscription_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."Account"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Service" ADD CONSTRAINT "Service_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Schedule" ADD CONSTRAINT "Schedule_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."Job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_BidToTask" ADD CONSTRAINT "_BidToTask_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_BidToTask" ADD CONSTRAINT "_BidToTask_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Task"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "Trade_name_key" ON "Trade" USING btree ("name" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Organization_addressId_key" ON "Organization" USING btree ("addressId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Chat_bidId_key" ON "Chat" USING btree ("bidId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Chat_jobId_key" ON "Chat" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "Message_chatId_idx" ON "Message" USING btree ("chatId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Property_addressId_key" ON "Property" USING btree ("addressId" text_ops);--> statement-breakpoint
CREATE INDEX "Collaboration_crewMemberId_idx" ON "Collaboration" USING btree ("crewMemberId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Collaboration_primaryOrgId_crewMemberId_key" ON "Collaboration" USING btree ("primaryOrgId" text_ops,"crewMemberId" text_ops);--> statement-breakpoint
CREATE INDEX "Collaboration_primaryOrgId_idx" ON "Collaboration" USING btree ("primaryOrgId" text_ops);--> statement-breakpoint
CREATE INDEX "TemplateTask_templateId_idx" ON "TemplateTask" USING btree ("templateId" text_ops);--> statement-breakpoint
CREATE INDEX "Review_jobId_idx" ON "Review" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "Review_reviewerId_idx" ON "Review" USING btree ("reviewerId" text_ops);--> statement-breakpoint
CREATE INDEX "PushSubscription_accountId_idx" ON "PushSubscription" USING btree ("accountId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "PushSubscription_endpoint_key" ON "PushSubscription" USING btree ("endpoint" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Account_userId_role_key" ON "Account" USING btree ("userId" text_ops,"role" text_ops);--> statement-breakpoint
CREATE INDEX "Schedule_jobId_idx" ON "Schedule" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Schedule_jobId_key" ON "Schedule" USING btree ("jobId" text_ops);--> statement-breakpoint
CREATE INDEX "_BidToTask_B_index" ON "_BidToTask" USING btree ("B" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Membership_userId_organizationId_key" ON "Membership" USING btree ("userId" text_ops,"organizationId" text_ops);
*/