{"id": "c23c6e27-77b6-4f9f-a166-01046ac5d70d", "prevId": "771bb80e-68ac-4de7-a0df-d2ad66e829ee", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.address": {"name": "address", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "street": {"name": "street", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_chat": {"name": "ai_chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_message": {"name": "ai_message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "parts": {"name": "parts", "type": "jsonb", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "ai_chat_role", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bid": {"name": "bid", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "bid_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}}, "indexes": {}, "foreignKeys": {"bid_jobId_fkey": {"name": "bid_jobId_fkey", "tableFrom": "bid", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "bid_organizationId_fkey": {"name": "bid_organizationId_fkey", "tableFrom": "bid", "tableTo": "organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._bid_to_task": {"name": "_bid_to_task", "schema": "", "columns": {"A": {"name": "A", "type": "text", "primaryKey": false, "notNull": true}, "B": {"name": "B", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"_bid_to_task_B_index": {"name": "_bid_to_task_B_index", "columns": [{"expression": "B", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_bid_to_task_A_fkey": {"name": "_bid_to_task_A_fkey", "tableFrom": "_bid_to_task", "tableTo": "bid", "columnsFrom": ["A"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "_bid_to_task_B_fkey": {"name": "_bid_to_task_B_fkey", "tableFrom": "_bid_to_task", "tableTo": "task", "columnsFrom": ["B"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"_bid_to_task_AB_pkey": {"name": "_bid_to_task_AB_pkey", "columns": ["A", "B"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "bidId": {"name": "bidId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"chat_bidId_key": {"name": "chat_bidId_key", "columns": [{"expression": "bidId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "chat_jobId_key": {"name": "chat_jobId_key", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_bidId_fkey": {"name": "chat_bidId_fkey", "tableFrom": "chat", "tableTo": "bid", "columnsFrom": ["bidId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "chat_jobId_fkey": {"name": "chat_jobId_fkey", "tableFrom": "chat", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collaboration": {"name": "collaboration", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "primaryOrgId": {"name": "primaryOrgId", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "crewMemberId": {"name": "crewMemberId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"collaboration_crewMemberId_idx": {"name": "collaboration_crewMemberId_idx", "columns": [{"expression": "crewMemberId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "collaboration_primaryOrgId_crewMemberId_key": {"name": "collaboration_primaryOrgId_crewMemberId_key", "columns": [{"expression": "primaryOrgId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "crewMemberId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "collaboration_primaryOrgId_idx": {"name": "collaboration_primaryOrgId_idx", "columns": [{"expression": "primaryOrgId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"collaboration_primaryOrgId_fkey": {"name": "collaboration_primaryOrgId_fkey", "tableFrom": "collaboration", "tableTo": "organization", "columnsFrom": ["primaryOrgId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "collaboration_crewMemberId_fkey": {"name": "collaboration_crewMemberId_fkey", "tableFrom": "collaboration", "tableTo": "organization", "columnsFrom": ["crewMemberId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.embeddings": {"name": "embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "resourceId": {"name": "resourceId", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}}, "indexes": {"embeddings_embedding_idx": {"name": "embeddings_embedding_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {"embeddings_resourceId_resources_id_fk": {"name": "embeddings_resourceId_resources_id_fk", "tableFrom": "embeddings", "tableTo": "resources", "columnsFrom": ["resourceId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.job": {"name": "job", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "propertyId": {"name": "propertyId", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "taskBids": {"name": "taskBids", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "startsAt": {"name": "startsAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "deadline": {"name": "deadline", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "job_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "contractorCompleted": {"name": "contractorCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "homeownerCompleted": {"name": "homeownerCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isRecurring": {"name": "isRecurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "jobType": {"name": "jobType", "type": "job_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STANDARD'"}, "recurringFrequency": {"name": "recurringFrequency", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"job_propertyId_fkey": {"name": "job_propertyId_fkey", "tableFrom": "job", "tableTo": "property", "columnsFrom": ["propertyId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.job_image": {"name": "job_image", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"job_image_jobId_fkey": {"name": "job_image_jobId_fkey", "tableFrom": "job_image", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.job_template": {"name": "job_template", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.membership": {"name": "membership", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"membership_userId_organizationId_key": {"name": "membership_userId_organizationId_key", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "organizationId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"membership_organizationId_fkey": {"name": "membership_organizationId_fkey", "tableFrom": "membership", "tableTo": "organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"membership_pkey": {"name": "membership_pkey", "columns": ["userId", "organizationId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.message": {"name": "message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "senderId": {"name": "senderId", "type": "text", "primaryKey": false, "notNull": true}, "senderType": {"name": "senderType", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "senderAvatarUrl": {"name": "senderAvatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "senderInitials": {"name": "senderInitials", "type": "text", "primaryKey": false, "notNull": false}, "commandData": {"name": "commandData", "type": "text", "primaryKey": false, "notNull": false}, "isCommand": {"name": "isCommand", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"message_chatId_idx": {"name": "message_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"message_chatId_fkey": {"name": "message_chatId_fkey", "tableFrom": "message", "tableTo": "chat", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logoUrl": {"name": "logoUrl", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "acceptsQuickHire": {"name": "acceptsQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"organization_addressId_key": {"name": "organization_addressId_key", "columns": [{"expression": "addressId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"organization_tradeId_fkey": {"name": "organization_tradeId_fkey", "tableFrom": "organization", "tableTo": "trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "organization_addressId_fkey": {"name": "organization_addressId_fkey", "tableFrom": "organization", "tableTo": "address", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.property": {"name": "property", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"property_addressId_key": {"name": "property_addressId_key", "columns": [{"expression": "addressId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"property_addressId_fkey": {"name": "property_addressId_fkey", "tableFrom": "property", "tableTo": "address", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "property_accountId_fkey": {"name": "property_accountId_fkey", "tableFrom": "property", "tableTo": "account", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.push_subscription": {"name": "push_subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "p256dh": {"name": "p256dh", "type": "text", "primaryKey": false, "notNull": true}, "auth": {"name": "auth", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"push_subscription_accountId_idx": {"name": "push_subscription_accountId_idx", "columns": [{"expression": "accountId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "push_subscription_endpoint_key": {"name": "push_subscription_endpoint_key", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"push_subscription_accountId_fkey": {"name": "push_subscription_accountId_fkey", "tableFrom": "push_subscription", "tableTo": "account", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resources": {"name": "resources", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.review": {"name": "review", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "reviewType": {"name": "reviewType", "type": "text", "primaryKey": false, "notNull": true}, "reviewerId": {"name": "reviewerId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"review_jobId_idx": {"name": "review_jobId_idx", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "review_reviewerId_idx": {"name": "review_reviewerId_idx", "columns": [{"expression": "reviewerId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"review_jobId_fkey": {"name": "review_jobId_fkey", "tableFrom": "review", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schedule": {"name": "schedule", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "proposedStartDate": {"name": "proposedStartDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "proposedEndDate": {"name": "proposedEndDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "confirmedStartDate": {"name": "confirmedStartDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "confirmedEndDate": {"name": "confirmedEndDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "schedule_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "proposedById": {"name": "proposedById", "type": "text", "primaryKey": false, "notNull": true}, "proposedByRole": {"name": "proposedByRole", "type": "account_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"schedule_jobId_idx": {"name": "schedule_jobId_idx", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"schedule_jobId_fkey": {"name": "schedule_jobId_fkey", "tableFrom": "schedule", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service": {"name": "service", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "double precision", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"service_organizationId_fkey": {"name": "service_organizationId_fkey", "tableFrom": "service", "tableTo": "organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task": {"name": "task", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_jobId_fkey": {"name": "task_jobId_fkey", "tableFrom": "task", "tableTo": "job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "task_tradeId_fkey": {"name": "task_tradeId_fkey", "tableFrom": "task", "tableTo": "trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.template_task": {"name": "template_task", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "templateId": {"name": "templateId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"template_task_templateId_idx": {"name": "template_task_templateId_idx", "columns": [{"expression": "templateId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"template_task_templateId_fkey": {"name": "template_task_templateId_fkey", "tableFrom": "template_task", "tableTo": "job_template", "columnsFrom": ["templateId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "template_task_tradeId_fkey": {"name": "template_task_tradeId_fkey", "tableFrom": "template_task", "tableTo": "trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.trade": {"name": "trade", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "availableForQuickHire": {"name": "availableForQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"trade_name_key": {"name": "trade_name_key", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.two_factor": {"name": "two_factor", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "secret": {"name": "secret", "type": "text", "primaryKey": false, "notNull": true}, "backup_codes": {"name": "backup_codes", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"two_factor_user_id_user_id_fk": {"name": "two_factor_user_id_user_id_fk", "tableFrom": "two_factor", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": false}, "onboarding_complete": {"name": "onboarding_complete", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.account_role": {"name": "account_role", "schema": "public", "values": ["HOMEOWNER", "PROFESSIONAL", "ADMIN"]}, "public.ai_chat_role": {"name": "ai_chat_role", "schema": "public", "values": ["user", "assistant", "system", "data"]}, "public.bid_status": {"name": "bid_status", "schema": "public", "values": ["PROPOSED", "ACCEPTED", "REJECTED", "CANCELED", "WITHDRAWN"]}, "public.job_status": {"name": "job_status", "schema": "public", "values": ["DRAFT", "PUBLISHED", "CLOSED", "CANCELED", "AWARDED", "COMPLETED"]}, "public.job_type": {"name": "job_type", "schema": "public", "values": ["STANDARD", "QUICK_HIRE"]}, "public.schedule_status": {"name": "schedule_status", "schema": "public", "values": ["PROPOSED", "CONFIRMED", "RESCHEDULED", "CANCELED"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}