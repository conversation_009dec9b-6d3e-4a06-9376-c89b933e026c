{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public._prisma_migrations": {"name": "_prisma_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "checksum": {"name": "checksum", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "finished_at": {"name": "finished_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "migration_name": {"name": "migration_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": false}, "rolled_back_at": {"name": "rolled_back_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "applied_steps_count": {"name": "applied_steps_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Task": {"name": "Task", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Task_jobId_fkey": {"name": "Task_jobId_fkey", "tableFrom": "Task", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Task_tradeId_fkey": {"name": "Task_tradeId_fkey", "tableFrom": "Task", "tableTo": "Trade", "schemaTo": "public", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Trade": {"name": "Trade", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "availableForQuickHire": {"name": "availableForQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Trade_name_key": {"name": "Trade_name_key", "columns": [{"expression": "name", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Organization": {"name": "Organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logoUrl": {"name": "logoUrl", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "acceptsQuickHire": {"name": "acceptsQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Organization_addressId_key": {"name": "Organization_addressId_key", "columns": [{"expression": "addressId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Organization_tradeId_fkey": {"name": "Organization_tradeId_fkey", "tableFrom": "Organization", "tableTo": "Trade", "schemaTo": "public", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Organization_addressId_fkey": {"name": "Organization_addressId_fkey", "tableFrom": "Organization", "tableTo": "Address", "schemaTo": "public", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "bidId": {"name": "bidId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"Chat_bidId_key": {"name": "Chat_bidId_key", "columns": [{"expression": "bidId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Chat_jobId_key": {"name": "Chat_jobId_key", "columns": [{"expression": "jobId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Chat_bidId_fkey": {"name": "Chat_bidId_fkey", "tableFrom": "Cha<PERSON>", "tableTo": "Bid", "schemaTo": "public", "columnsFrom": ["bidId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Chat_jobId_fkey": {"name": "Chat_jobId_fkey", "tableFrom": "Cha<PERSON>", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "senderId": {"name": "senderId", "type": "text", "primaryKey": false, "notNull": true}, "senderType": {"name": "senderType", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "senderAvatarUrl": {"name": "senderAvatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "senderInitials": {"name": "senderInitials", "type": "text", "primaryKey": false, "notNull": false}, "commandData": {"name": "commandData", "type": "text", "primaryKey": false, "notNull": false}, "isCommand": {"name": "isCommand", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Message_chatId_idx": {"name": "Message_chatId_idx", "columns": [{"expression": "chatId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Message_chatId_fkey": {"name": "Message_chatId_fkey", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "schemaTo": "public", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.JobImage": {"name": "JobImage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"JobImage_jobId_fkey": {"name": "JobImage_jobId_fkey", "tableFrom": "JobImage", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Bid": {"name": "Bid", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "BidStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}}, "indexes": {}, "foreignKeys": {"Bid_jobId_fkey": {"name": "Bid_jobId_fkey", "tableFrom": "Bid", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Bid_organizationId_fkey": {"name": "Bid_organizationId_fkey", "tableFrom": "Bid", "tableTo": "Organization", "schemaTo": "public", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Property": {"name": "Property", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Property_addressId_key": {"name": "Property_addressId_key", "columns": [{"expression": "addressId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Property_addressId_fkey": {"name": "Property_addressId_fkey", "tableFrom": "Property", "tableTo": "Address", "schemaTo": "public", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Property_accountId_fkey": {"name": "Property_accountId_fkey", "tableFrom": "Property", "tableTo": "Account", "schemaTo": "public", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Job": {"name": "Job", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "propertyId": {"name": "propertyId", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "taskBids": {"name": "taskBids", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "startsAt": {"name": "startsAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "deadline": {"name": "deadline", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "JobStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "completedAt": {"name": "completedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "contractorCompleted": {"name": "contractorCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "homeownerCompleted": {"name": "homeownerCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isRecurring": {"name": "isRecurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "jobType": {"name": "jobType", "type": "JobType", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STANDARD'"}, "recurringFrequency": {"name": "recurringFrequency", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Job_propertyId_fkey": {"name": "Job_propertyId_fkey", "tableFrom": "Job", "tableTo": "Property", "schemaTo": "public", "columnsFrom": ["propertyId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Address": {"name": "Address", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "street": {"name": "street", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Collaboration": {"name": "Collaboration", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "primaryOrgId": {"name": "primaryOrgId", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "crewMemberId": {"name": "crewMemberId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Collaboration_crewMemberId_idx": {"name": "Collaboration_crewMemberId_idx", "columns": [{"expression": "crewMemberId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Collaboration_primaryOrgId_crewMemberId_key": {"name": "Collaboration_primaryOrgId_crewMemberId_key", "columns": [{"expression": "primaryOrgId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "crewMemberId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Collaboration_primaryOrgId_idx": {"name": "Collaboration_primaryOrgId_idx", "columns": [{"expression": "primaryOrgId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Collaboration_primaryOrgId_fkey": {"name": "Collaboration_primaryOrgId_fkey", "tableFrom": "Collaboration", "tableTo": "Organization", "schemaTo": "public", "columnsFrom": ["primaryOrgId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Collaboration_crewMemberId_fkey": {"name": "Collaboration_crewMemberId_fkey", "tableFrom": "Collaboration", "tableTo": "Organization", "schemaTo": "public", "columnsFrom": ["crewMemberId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.JobTemplate": {"name": "JobTemplate", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.TemplateTask": {"name": "TemplateTask", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "templateId": {"name": "templateId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"TemplateTask_templateId_idx": {"name": "TemplateTask_templateId_idx", "columns": [{"expression": "templateId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TemplateTask_templateId_fkey": {"name": "TemplateTask_templateId_fkey", "tableFrom": "TemplateTask", "tableTo": "JobTemplate", "schemaTo": "public", "columnsFrom": ["templateId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "TemplateTask_tradeId_fkey": {"name": "TemplateTask_tradeId_fkey", "tableFrom": "TemplateTask", "tableTo": "Trade", "schemaTo": "public", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Review": {"name": "Review", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "reviewType": {"name": "reviewType", "type": "text", "primaryKey": false, "notNull": true}, "reviewerId": {"name": "reviewerId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"Review_jobId_idx": {"name": "Review_jobId_idx", "columns": [{"expression": "jobId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Review_reviewerId_idx": {"name": "Review_reviewerId_idx", "columns": [{"expression": "reviewerId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Review_jobId_fkey": {"name": "Review_jobId_fkey", "tableFrom": "Review", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.PushSubscription": {"name": "PushSubscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "p256dh": {"name": "p256dh", "type": "text", "primaryKey": false, "notNull": true}, "auth": {"name": "auth", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"PushSubscription_accountId_idx": {"name": "PushSubscription_accountId_idx", "columns": [{"expression": "accountId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "PushSubscription_endpoint_key": {"name": "PushSubscription_endpoint_key", "columns": [{"expression": "endpoint", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"PushSubscription_accountId_fkey": {"name": "PushSubscription_accountId_fkey", "tableFrom": "PushSubscription", "tableTo": "Account", "schemaTo": "public", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Service": {"name": "Service", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "double precision", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Service_organizationId_fkey": {"name": "Service_organizationId_fkey", "tableFrom": "Service", "tableTo": "Organization", "schemaTo": "public", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Account": {"name": "Account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "Account<PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'HOMEOWNER'"}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "emailNotifications": {"name": "emailNotifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"Account_userId_role_key": {"name": "Account_userId_role_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "role", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Schedule": {"name": "Schedule", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "proposedStartDate": {"name": "proposedStartDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "proposedEndDate": {"name": "proposedEndDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "confirmedStartDate": {"name": "confirmedStartDate", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "confirmedEndDate": {"name": "confirmedEndDate", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ScheduleStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "proposedById": {"name": "proposedById", "type": "text", "primaryKey": false, "notNull": true}, "proposedByRole": {"name": "proposedByRole", "type": "Account<PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "completedAt": {"name": "completedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}}, "indexes": {"Schedule_jobId_idx": {"name": "Schedule_jobId_idx", "columns": [{"expression": "jobId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Schedule_jobId_key": {"name": "Schedule_jobId_key", "columns": [{"expression": "jobId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Schedule_jobId_fkey": {"name": "Schedule_jobId_fkey", "tableFrom": "Schedule", "tableTo": "Job", "schemaTo": "public", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public._BidToTask": {"name": "_BidToTask", "schema": "", "columns": {"A": {"name": "A", "type": "text", "primaryKey": false, "notNull": true}, "B": {"name": "B", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"_BidToTask_B_index": {"name": "_BidToTask_B_index", "columns": [{"expression": "B", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_BidToTask_A_fkey": {"name": "_BidToTask_A_fkey", "tableFrom": "_BidToTask", "tableTo": "Bid", "schemaTo": "public", "columnsFrom": ["A"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "_BidToTask_B_fkey": {"name": "_BidToTask_B_fkey", "tableFrom": "_BidToTask", "tableTo": "Task", "schemaTo": "public", "columnsFrom": ["B"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"_BidToTask_AB_pkey": {"name": "_BidToTask_AB_pkey", "columns": ["A", "B"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Membership": {"name": "Membership", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Membership_userId_organizationId_key": {"name": "Membership_userId_organizationId_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "organizationId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Membership_organizationId_fkey": {"name": "Membership_organizationId_fkey", "tableFrom": "Membership", "tableTo": "Organization", "schemaTo": "public", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"Membership_pkey": {"name": "Membership_pkey", "columns": ["userId", "organizationId"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.AccountRole": {"name": "Account<PERSON><PERSON>", "values": ["HOMEOWNER", "PROFESSIONAL", "ADMIN"], "schema": "public"}, "public.BidStatus": {"name": "BidStatus", "values": ["PROPOSED", "ACCEPTED", "REJECTED", "CANCELED", "WITHDRAWN"], "schema": "public"}, "public.JobStatus": {"name": "JobStatus", "values": ["DRAFT", "PUBLISHED", "CLOSED", "CANCELED", "AWARDED", "COMPLETED"], "schema": "public"}, "public.JobType": {"name": "JobType", "values": ["STANDARD", "QUICK_HIRE"], "schema": "public"}, "public.ScheduleStatus": {"name": "ScheduleStatus", "values": ["PROPOSED", "CONFIRMED", "RESCHEDULED", "CANCELED"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}