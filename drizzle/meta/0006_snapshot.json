{"id": "17e31a01-e09a-4c65-8261-a9cab1c2422b", "prevId": "a44928a3-3897-4981-9daf-785e63bfda65", "version": "7", "dialect": "postgresql", "tables": {"public.Account": {"name": "Account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "Account<PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'HOMEOWNER'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "emailNotifications": {"name": "emailNotifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"Account_userId_role_key": {"name": "Account_userId_role_key", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Address": {"name": "Address", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "street": {"name": "street", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_chat": {"name": "ai_chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_message": {"name": "ai_message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "parts": {"name": "parts", "type": "jsonb", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Bid": {"name": "Bid", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "BidStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}}, "indexes": {}, "foreignKeys": {"Bid_jobId_fkey": {"name": "Bid_jobId_fkey", "tableFrom": "Bid", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Bid_organizationId_fkey": {"name": "Bid_organizationId_fkey", "tableFrom": "Bid", "tableTo": "Organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._BidToTask": {"name": "_BidToTask", "schema": "", "columns": {"A": {"name": "A", "type": "text", "primaryKey": false, "notNull": true}, "B": {"name": "B", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"_BidToTask_B_index": {"name": "_BidToTask_B_index", "columns": [{"expression": "B", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_BidToTask_A_fkey": {"name": "_BidToTask_A_fkey", "tableFrom": "_BidToTask", "tableTo": "Bid", "columnsFrom": ["A"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "_BidToTask_B_fkey": {"name": "_BidToTask_B_fkey", "tableFrom": "_BidToTask", "tableTo": "Task", "columnsFrom": ["B"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"_BidToTask_AB_pkey": {"name": "_BidToTask_AB_pkey", "columns": ["A", "B"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "bidId": {"name": "bidId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"Chat_bidId_key": {"name": "Chat_bidId_key", "columns": [{"expression": "bidId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Chat_jobId_key": {"name": "Chat_jobId_key", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Chat_bidId_fkey": {"name": "Chat_bidId_fkey", "tableFrom": "Cha<PERSON>", "tableTo": "Bid", "columnsFrom": ["bidId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Chat_jobId_fkey": {"name": "Chat_jobId_fkey", "tableFrom": "Cha<PERSON>", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Collaboration": {"name": "Collaboration", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "primaryOrgId": {"name": "primaryOrgId", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "crewMemberId": {"name": "crewMemberId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Collaboration_crewMemberId_idx": {"name": "Collaboration_crewMemberId_idx", "columns": [{"expression": "crewMemberId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Collaboration_primaryOrgId_crewMemberId_key": {"name": "Collaboration_primaryOrgId_crewMemberId_key", "columns": [{"expression": "primaryOrgId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "crewMemberId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Collaboration_primaryOrgId_idx": {"name": "Collaboration_primaryOrgId_idx", "columns": [{"expression": "primaryOrgId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Collaboration_primaryOrgId_fkey": {"name": "Collaboration_primaryOrgId_fkey", "tableFrom": "Collaboration", "tableTo": "Organization", "columnsFrom": ["primaryOrgId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Collaboration_crewMemberId_fkey": {"name": "Collaboration_crewMemberId_fkey", "tableFrom": "Collaboration", "tableTo": "Organization", "columnsFrom": ["crewMemberId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.embeddings": {"name": "embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "resourceId": {"name": "resourceId", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}}, "indexes": {"embeddings_embedding_idx": {"name": "embeddings_embedding_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {"embeddings_resourceId_resources_id_fk": {"name": "embeddings_resourceId_resources_id_fk", "tableFrom": "embeddings", "tableTo": "resources", "columnsFrom": ["resourceId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Job": {"name": "Job", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "propertyId": {"name": "propertyId", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "taskBids": {"name": "taskBids", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "startsAt": {"name": "startsAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "deadline": {"name": "deadline", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "JobStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "contractorCompleted": {"name": "contractorCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "homeownerCompleted": {"name": "homeownerCompleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isRecurring": {"name": "isRecurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "jobType": {"name": "jobType", "type": "JobType", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STANDARD'"}, "recurringFrequency": {"name": "recurringFrequency", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"Job_propertyId_fkey": {"name": "Job_propertyId_fkey", "tableFrom": "Job", "tableTo": "Property", "columnsFrom": ["propertyId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.JobImage": {"name": "JobImage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"JobImage_jobId_fkey": {"name": "JobImage_jobId_fkey", "tableFrom": "JobImage", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.JobTemplate": {"name": "JobTemplate", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "estimatedDuration": {"name": "estimatedDuration", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Membership": {"name": "Membership", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Membership_userId_organizationId_key": {"name": "Membership_userId_organizationId_key", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "organizationId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Membership_organizationId_fkey": {"name": "Membership_organizationId_fkey", "tableFrom": "Membership", "tableTo": "Organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"Membership_pkey": {"name": "Membership_pkey", "columns": ["userId", "organizationId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "senderId": {"name": "senderId", "type": "text", "primaryKey": false, "notNull": true}, "senderType": {"name": "senderType", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "chatId": {"name": "chatId", "type": "text", "primaryKey": false, "notNull": true}, "senderAvatarUrl": {"name": "senderAvatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "senderInitials": {"name": "senderInitials", "type": "text", "primaryKey": false, "notNull": false}, "commandData": {"name": "commandData", "type": "text", "primaryKey": false, "notNull": false}, "isCommand": {"name": "isCommand", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Message_chatId_idx": {"name": "Message_chatId_idx", "columns": [{"expression": "chatId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Message_chatId_fkey": {"name": "Message_chatId_fkey", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Organization": {"name": "Organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logoUrl": {"name": "logoUrl", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "acceptsQuickHire": {"name": "acceptsQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Organization_addressId_key": {"name": "Organization_addressId_key", "columns": [{"expression": "addressId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Organization_tradeId_fkey": {"name": "Organization_tradeId_fkey", "tableFrom": "Organization", "tableTo": "Trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Organization_addressId_fkey": {"name": "Organization_addressId_fkey", "tableFrom": "Organization", "tableTo": "Address", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Property": {"name": "Property", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "addressId": {"name": "addressId", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Property_addressId_key": {"name": "Property_addressId_key", "columns": [{"expression": "addressId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Property_addressId_fkey": {"name": "Property_addressId_fkey", "tableFrom": "Property", "tableTo": "Address", "columnsFrom": ["addressId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Property_accountId_fkey": {"name": "Property_accountId_fkey", "tableFrom": "Property", "tableTo": "Account", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.PushSubscription": {"name": "PushSubscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "p256dh": {"name": "p256dh", "type": "text", "primaryKey": false, "notNull": true}, "auth": {"name": "auth", "type": "text", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"PushSubscription_accountId_idx": {"name": "PushSubscription_accountId_idx", "columns": [{"expression": "accountId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "PushSubscription_endpoint_key": {"name": "PushSubscription_endpoint_key", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"PushSubscription_accountId_fkey": {"name": "PushSubscription_accountId_fkey", "tableFrom": "PushSubscription", "tableTo": "Account", "columnsFrom": ["accountId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resources": {"name": "resources", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Review": {"name": "Review", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "reviewType": {"name": "reviewType", "type": "text", "primaryKey": false, "notNull": true}, "reviewerId": {"name": "reviewerId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"Review_jobId_idx": {"name": "Review_jobId_idx", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Review_reviewerId_idx": {"name": "Review_reviewerId_idx", "columns": [{"expression": "reviewerId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Review_jobId_fkey": {"name": "Review_jobId_fkey", "tableFrom": "Review", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Schedule": {"name": "Schedule", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "proposedStartDate": {"name": "proposedStartDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "proposedEndDate": {"name": "proposedEndDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "confirmedStartDate": {"name": "confirmedStartDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "confirmedEndDate": {"name": "confirmedEndDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ScheduleStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PROPOSED'"}, "proposedById": {"name": "proposedById", "type": "text", "primaryKey": false, "notNull": true}, "proposedByRole": {"name": "proposedByRole", "type": "Account<PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"Schedule_jobId_idx": {"name": "Schedule_jobId_idx", "columns": [{"expression": "jobId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Schedule_jobId_fkey": {"name": "Schedule_jobId_fkey", "tableFrom": "Schedule", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Service": {"name": "Service", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "double precision", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "organizationId": {"name": "organizationId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Service_organizationId_fkey": {"name": "Service_organizationId_fkey", "tableFrom": "Service", "tableTo": "Organization", "columnsFrom": ["organizationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Task": {"name": "Task", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "jobId": {"name": "jobId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Task_jobId_fkey": {"name": "Task_jobId_fkey", "tableFrom": "Task", "tableTo": "Job", "columnsFrom": ["jobId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "Task_tradeId_fkey": {"name": "Task_tradeId_fkey", "tableFrom": "Task", "tableTo": "Trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.TemplateTask": {"name": "TemplateTask", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "templateId": {"name": "templateId", "type": "text", "primaryKey": false, "notNull": true}, "tradeId": {"name": "tradeId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"TemplateTask_templateId_idx": {"name": "TemplateTask_templateId_idx", "columns": [{"expression": "templateId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TemplateTask_templateId_fkey": {"name": "TemplateTask_templateId_fkey", "tableFrom": "TemplateTask", "tableTo": "JobTemplate", "columnsFrom": ["templateId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "TemplateTask_tradeId_fkey": {"name": "TemplateTask_tradeId_fkey", "tableFrom": "TemplateTask", "tableTo": "Trade", "columnsFrom": ["tradeId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Trade": {"name": "Trade", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "availableForQuickHire": {"name": "availableForQuickHire", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"Trade_name_key": {"name": "Trade_name_key", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.AccountRole": {"name": "Account<PERSON><PERSON>", "schema": "public", "values": ["HOMEOWNER", "PROFESSIONAL", "ADMIN"]}, "public.role": {"name": "role", "schema": "public", "values": ["user", "assistant", "system", "data"]}, "public.BidStatus": {"name": "BidStatus", "schema": "public", "values": ["PROPOSED", "ACCEPTED", "REJECTED", "CANCELED", "WITHDRAWN"]}, "public.JobStatus": {"name": "JobStatus", "schema": "public", "values": ["DRAFT", "PUBLISHED", "CLOSED", "CANCELED", "AWARDED", "COMPLETED"]}, "public.JobType": {"name": "JobType", "schema": "public", "values": ["STANDARD", "QUICK_HIRE"]}, "public.ScheduleStatus": {"name": "ScheduleStatus", "schema": "public", "values": ["PROPOSED", "CONFIRMED", "RESCHEDULED", "CANCELED"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}