ALTER TABLE "property" RENAME COLUMN "accountId" TO "userId";--> statement-breakpoint
ALTER TABLE "membership" DROP CONSTRAINT "membership_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "property" DROP CONSTRAINT "property_accountId_fkey";
--> statement-breakpoint
ALTER TABLE "membership" ADD CONSTRAINT "membership_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "membership" ADD CONSTRAINT "membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "property" ADD CONSTRAINT "property_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE cascade;