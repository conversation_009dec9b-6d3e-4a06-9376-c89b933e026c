CREATE TABLE "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp NOT NULL,
	"token" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"ip_address" text,
	"user_agent" text,
	"user_id" text NOT NULL,
	"impersonated_by" text,
	CONSTRAINT "session_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "two_factor" (
	"id" text PRIMARY KEY NOT NULL,
	"secret" text NOT NULL,
	"backup_codes" text NOT NULL,
	"user_id" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean NOT NULL,
	"image" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"role" text,
	"banned" boolean,
	"ban_reason" text,
	"ban_expires" timestamp,
	"two_factor_enabled" boolean,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "Account" RENAME TO "account";--> statement-breakpoint
ALTER TABLE "Address" RENAME TO "address";--> statement-breakpoint
ALTER TABLE "Bid" RENAME TO "bid";--> statement-breakpoint
ALTER TABLE "_BidToTask" RENAME TO "_bid_to_task";--> statement-breakpoint
ALTER TABLE "Chat" RENAME TO "chat";--> statement-breakpoint
ALTER TABLE "Collaboration" RENAME TO "collaboration";--> statement-breakpoint
ALTER TABLE "Job" RENAME TO "job";--> statement-breakpoint
ALTER TABLE "JobImage" RENAME TO "job_image";--> statement-breakpoint
ALTER TABLE "JobTemplate" RENAME TO "job_template";--> statement-breakpoint
ALTER TABLE "Membership" RENAME TO "membership";--> statement-breakpoint
ALTER TABLE "Message" RENAME TO "message";--> statement-breakpoint
ALTER TABLE "Organization" RENAME TO "organization";--> statement-breakpoint
ALTER TABLE "Property" RENAME TO "property";--> statement-breakpoint
ALTER TABLE "PushSubscription" RENAME TO "push_subscription";--> statement-breakpoint
ALTER TABLE "Review" RENAME TO "review";--> statement-breakpoint
ALTER TABLE "Schedule" RENAME TO "schedule";--> statement-breakpoint
ALTER TABLE "Service" RENAME TO "service";--> statement-breakpoint
ALTER TABLE "Task" RENAME TO "task";--> statement-breakpoint
ALTER TABLE "TemplateTask" RENAME TO "template_task";--> statement-breakpoint
ALTER TABLE "Trade" RENAME TO "trade";--> statement-breakpoint
ALTER TABLE "account" RENAME COLUMN "userId" TO "user_id";--> statement-breakpoint
ALTER TABLE "account" RENAME COLUMN "createdAt" TO "created_at";--> statement-breakpoint
ALTER TABLE "account" RENAME COLUMN "updatedAt" TO "updated_at";--> statement-breakpoint
ALTER TABLE "bid" DROP CONSTRAINT "Bid_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "bid" DROP CONSTRAINT "Bid_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "_bid_to_task" DROP CONSTRAINT "_BidToTask_A_fkey";
--> statement-breakpoint
ALTER TABLE "_bid_to_task" DROP CONSTRAINT "_BidToTask_B_fkey";
--> statement-breakpoint
ALTER TABLE "chat" DROP CONSTRAINT "Chat_bidId_fkey";
--> statement-breakpoint
ALTER TABLE "chat" DROP CONSTRAINT "Chat_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "collaboration" DROP CONSTRAINT "Collaboration_primaryOrgId_fkey";
--> statement-breakpoint
ALTER TABLE "collaboration" DROP CONSTRAINT "Collaboration_crewMemberId_fkey";
--> statement-breakpoint
ALTER TABLE "job" DROP CONSTRAINT "Job_propertyId_fkey";
--> statement-breakpoint
ALTER TABLE "job_image" DROP CONSTRAINT "JobImage_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "membership" DROP CONSTRAINT "Membership_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "message" DROP CONSTRAINT "Message_chatId_fkey";
--> statement-breakpoint
ALTER TABLE "organization" DROP CONSTRAINT "Organization_tradeId_fkey";
--> statement-breakpoint
ALTER TABLE "organization" DROP CONSTRAINT "Organization_addressId_fkey";
--> statement-breakpoint
ALTER TABLE "property" DROP CONSTRAINT "Property_addressId_fkey";
--> statement-breakpoint
ALTER TABLE "property" DROP CONSTRAINT "Property_accountId_fkey";
--> statement-breakpoint
ALTER TABLE "push_subscription" DROP CONSTRAINT "PushSubscription_accountId_fkey";
--> statement-breakpoint
ALTER TABLE "review" DROP CONSTRAINT "Review_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "schedule" DROP CONSTRAINT "Schedule_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "service" DROP CONSTRAINT "Service_organizationId_fkey";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "Task_jobId_fkey";
--> statement-breakpoint
ALTER TABLE "task" DROP CONSTRAINT "Task_tradeId_fkey";
--> statement-breakpoint
ALTER TABLE "template_task" DROP CONSTRAINT "TemplateTask_templateId_fkey";
--> statement-breakpoint
ALTER TABLE "template_task" DROP CONSTRAINT "TemplateTask_tradeId_fkey";
--> statement-breakpoint
DROP INDEX "Account_userId_role_key";--> statement-breakpoint
DROP INDEX "_BidToTask_B_index";--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "account_id" text NOT NULL;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "provider_id" text NOT NULL;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "access_token" text;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "refresh_token" text;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "id_token" text;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "access_token_expires_at" timestamp;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "refresh_token_expires_at" timestamp;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "scope" text;--> statement-breakpoint
ALTER TABLE "account" ADD COLUMN "password" text;--> statement-breakpoint
ALTER TABLE "session" ADD CONSTRAINT "session_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "two_factor" ADD CONSTRAINT "two_factor_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "account" ADD CONSTRAINT "account_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bid" ADD CONSTRAINT "Bid_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bid" ADD CONSTRAINT "Bid_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_bid_to_task" ADD CONSTRAINT "_BidToTask_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "_bid_to_task" ADD CONSTRAINT "_BidToTask_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."task"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "Chat_bidId_fkey" FOREIGN KEY ("bidId") REFERENCES "public"."bid"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "Chat_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "collaboration" ADD CONSTRAINT "Collaboration_primaryOrgId_fkey" FOREIGN KEY ("primaryOrgId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "collaboration" ADD CONSTRAINT "Collaboration_crewMemberId_fkey" FOREIGN KEY ("crewMemberId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "job" ADD CONSTRAINT "Job_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "public"."property"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "job_image" ADD CONSTRAINT "JobImage_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "membership" ADD CONSTRAINT "Membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "Message_chatId_fkey" FOREIGN KEY ("chatId") REFERENCES "public"."chat"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "Organization_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "Organization_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "property" ADD CONSTRAINT "Property_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "public"."address"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "property" ADD CONSTRAINT "Property_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE restrict ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "push_subscription" ADD CONSTRAINT "PushSubscription_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "review" ADD CONSTRAINT "Review_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "schedule" ADD CONSTRAINT "Schedule_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "service" ADD CONSTRAINT "Service_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."organization"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "Task_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "public"."job"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "task" ADD CONSTRAINT "Task_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "template_task" ADD CONSTRAINT "TemplateTask_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "public"."job_template"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "template_task" ADD CONSTRAINT "TemplateTask_tradeId_fkey" FOREIGN KEY ("tradeId") REFERENCES "public"."trade"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE INDEX "_bid_to_task_B_index" ON "_bid_to_task" USING btree ("B" text_ops);--> statement-breakpoint
ALTER TABLE "account" DROP COLUMN "role";--> statement-breakpoint
ALTER TABLE "account" DROP COLUMN "emailNotifications";