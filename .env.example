# TradeCrews Environment Configuration
#
# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.
#
# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.
#
# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# =============================================================================
# CORE APPLICATION
# =============================================================================

# Node Environment
NODE_ENV="development"

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Primary database connection URL (required)
# For local development: postgresql://username:password@localhost:5432/tradecrews
# For production: Use your hosted PostgreSQL URL (Neon, Supabase, etc.)
DATABASE_URL="postgresql://username:password@localhost:5432/tradecrews"

# Direct database connection URL (optional, used by some hosting providers)
# Usually the same as DATABASE_URL unless using connection pooling
DIRECT_URL="postgresql://username:password@localhost:5432/tradecrews"

# =============================================================================
# AUTHENTICATION (Better Auth)
# =============================================================================

# Better Auth secret key (generate a secure random string)
BETTER_AUTH_SECRET="your_secure_random_string_here"

# Better Auth URL (your application's base URL)
BETTER_AUTH_URL="http://localhost:3000"

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================

# Google OAuth (for Google Sign-In)
# Get these from: https://console.developers.google.com/
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Microsoft OAuth (for Microsoft Sign-In)
# Get these from: https://portal.azure.com/
MICROSOFT_CLIENT_ID="your_microsoft_client_id"
MICROSOFT_CLIENT_SECRET="your_microsoft_client_secret"

# =============================================================================
# FILE STORAGE (Transloadit)
# =============================================================================

# Transloadit configuration for file uploads and image processing
# Get these from: https://transloadit.com/
TRANSLOADIT_KEY="your_transloadit_key"
TRANSLOADIT_SECRET="your_transloadit_secret"
TRANSLOADIT_TEMPLATE_ID="your_transloadit_template_id"
TRANSLOADIT_TEMPLATE_ID=your-template-id
NEXT_PUBLIC_UPLOAD_BASE_URL=https://your-upload-base-url
# Public URL for accessing uploaded files
NEXT_PUBLIC_STORAGE_URL="https://pub-6086ffae2ef94e43bffa087f1493edb6.r2.dev"

# =============================================================================
# REAL-TIME COMMUNICATION (Pusher)
# =============================================================================

# Pusher configuration for real-time chat and notifications
# Get these from: https://pusher.com/
PUSHER_APP_ID="your_pusher_app_id"
PUSHER_KEY="your_pusher_key"
PUSHER_SECRET="your_pusher_secret"
PUSHER_CLUSTER="us2"

# Public Pusher configuration (exposed to client)
NEXT_PUBLIC_PUSHER_KEY="your_pusher_key"
NEXT_PUBLIC_PUSHER_CLUSTER="us2"

# =============================================================================
# AI INTEGRATION (OpenAI)
# =============================================================================

# OpenAI API key for AI assistant "Jack"
# Get this from: https://platform.openai.com/api-keys
OPENAI_API_KEY="sk-your_openai_api_key"

# =============================================================================
# GEOCODING & MAPS
# =============================================================================

# Geocoding API for address validation and coordinates
# Default uses OpenStreetMap (free), can be replaced with Google Maps API
GEOCODING_API_URL="https://nominatim.openstreetmap.org"

# =============================================================================
# EMAIL SERVICES (Resend)
# =============================================================================

# Resend API key for sending transactional emails
# Get this from: https://resend.com/
RESEND_API_KEY="re_your_resend_api_key"

# =============================================================================
# PUSH NOTIFICATIONS (Web Push)
# =============================================================================

# VAPID keys for web push notifications
# Generate these using: npx web-push generate-vapid-keys
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your_vapid_public_key"
VAPID_PRIVATE_KEY="your_vapid_private_key"

# =============================================================================
# ANALYTICS (PostHog)
# =============================================================================

# PostHog configuration for user analytics and feature flags
# Get these from: https://posthog.com/
NEXT_PUBLIC_POSTHOG_KEY="phc_your_posthog_project_key"
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"

# =============================================================================
# DEVELOPMENT & DEPLOYMENT
# =============================================================================

# Skip environment validation during build (useful for Docker builds)
# SKIP_ENV_VALIDATION="true"

# Enable bundle analysis (used by build:analyze script)
# ANALYZE="true"

# Vercel deployment URL (automatically set by Vercel)
# VERCEL_URL="your-app.vercel.app"

# Custom port for local development (default: 3000)
# PORT="3000"
